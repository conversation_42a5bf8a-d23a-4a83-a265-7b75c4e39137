/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/9.0.53
 * Generated at: 2025-08-04 06:23:31 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.include.dialog;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.polarion.platform.i18n.Localization;
import org.polarion.svnwebclient.web.model.data.*;

public final class changeConfirmationContent_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent,
                 org.apache.jasper.runtime.JspSourceImports {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private static final java.util.Set<java.lang.String> _jspx_imports_packages;

  private static final java.util.Set<java.lang.String> _jspx_imports_classes;

  static {
    _jspx_imports_packages = new java.util.HashSet<>();
    _jspx_imports_packages.add("javax.servlet");
    _jspx_imports_packages.add("javax.servlet.http");
    _jspx_imports_packages.add("javax.servlet.jsp");
    _jspx_imports_packages.add("org.polarion.svnwebclient.web.model.data");
    _jspx_imports_classes = new java.util.HashSet<>();
    _jspx_imports_classes.add("com.polarion.platform.i18n.Localization");
  }

  private volatile javax.el.ExpressionFactory _el_expressionfactory;
  private volatile org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public java.util.Set<java.lang.String> getPackageImports() {
    return _jspx_imports_packages;
  }

  public java.util.Set<java.lang.String> getClassImports() {
    return _jspx_imports_classes;
  }

  public javax.el.ExpressionFactory _jsp_getExpressionFactory() {
    if (_el_expressionfactory == null) {
      synchronized (this) {
        if (_el_expressionfactory == null) {
          _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
        }
      }
    }
    return _el_expressionfactory;
  }

  public org.apache.tomcat.InstanceManager _jsp_getInstanceManager() {
    if (_jsp_instancemanager == null) {
      synchronized (this) {
        if (_jsp_instancemanager == null) {
          _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
        }
      }
    }
    return _jsp_instancemanager;
  }

  public void _jspInit() {
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
      throws java.io.IOException, javax.servlet.ServletException {

    if (!javax.servlet.DispatcherType.ERROR.equals(request.getDispatcherType())) {
      final java.lang.String _jspx_method = request.getMethod();
      if ("OPTIONS".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        return;
      }
      if (!"GET".equals(_jspx_method) && !"POST".equals(_jspx_method) && !"HEAD".equals(_jspx_method)) {
        response.setHeader("Allow","GET, HEAD, POST, OPTIONS");
        response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "JSPs only permit GET, POST or HEAD. Jasper also permits OPTIONS");
        return;
      }
    }

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      org.polarion.svnwebclient.web.controller.ChangeConfirmation bean = null;
      bean = (org.polarion.svnwebclient.web.controller.ChangeConfirmation) _jspx_page_context.getAttribute("bean", javax.servlet.jsp.PageContext.REQUEST_SCOPE);
      if (bean == null){
        throw new java.lang.InstantiationException("bean bean not found within scope");
      }
      out.write('\n');

RepoChangeResult changeResult = bean.getChangeResult();

      out.write("\n");
      out.write("<form name=\"directoryAdd\" method=\"POST\" action=\"");
      out.print(bean.getOkUrl());
      out.write("\" style=\"padding:0;margin:0;\">\n");
      out.write("    <table class=\"dialogcontent\" cellspacing=\"0\" cellpadding=\"0\" width=\"100%\" height=\"100%\">\n");
      out.write("        <tr>\n");
      out.write("            <td>\n");
      out.write("                <b>");
      out.print(Localization.getString("svnwebclient.changeConfirmationContent.message"));
      out.write("</b>\n");
      out.write("            </td>\n");
      out.write("        </tr>\n");
      out.write("        <tr>\n");
      out.write("            <td>\n");
      out.write("                ");
      out.print(changeResult.getMessage());
      out.write("\n");
      out.write("            </td>\n");
      out.write("        </tr>    \n");
      out.write("        <tr>\n");
      out.write("            <td style=\"padding-top:10px;\">\n");
      out.write("                <b>");
      out.print(Localization.getString("svnwebclient.changeConfirmationContent.changedElements"));
      out.write("</b>\n");
      out.write("            </td>\n");
      out.write("        </tr>\n");
      out.write("        <tr>\n");
      out.write("            <td>\n");
      out.write("                <table id=\"table_list_of_files\" name=\"table_list_of_files\" class=\"list\" width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" rules=\"all\">\n");
      out.write("                    <tr>\n");
      out.write("                        <th nowrap=\"true\" width=\"30%\">\n");
      out.write("                            ");
      out.print(Localization.getString("svnwebclient.revprops.name"));
      out.write("\n");
      out.write("                        </th>\n");
      out.write("                        <th nowrap=\"true\" width=\"5%\">\n");
      out.write("                            ");
      out.print(Localization.getString("svnwebclient.revprops.revision"));
      out.write("\n");
      out.write("                        </th>\n");
      out.write("                        <th nowrap=\"true\" width=\"5%\">\n");
      out.write("                            ");
      out.print(Localization.getString("svnwebclient.revprops.date"));
      out.write("\n");
      out.write("                        </th>\n");
      out.write("                        <th nowrap=\"true\" width=\"10%\">\n");
      out.write("                            ");
      out.print(Localization.getString("svnwebclient.revprops.author"));
      out.write("\n");
      out.write("                        </th>\n");
      out.write("                        <th nowrap=\"true\" width=\"45%\">\n");
      out.write("                            ");
      out.print(Localization.getString("svnwebclient.revprops.comment"));
      out.write("\n");
      out.write("                        </th>\n");
      out.write("                    </tr>\n");

int counter = 0;
        for (int i = 0; i < changeResult.getChanges().size(); i++) {
    RepoChangeResult.RepoChange element = (RepoChangeResult.RepoChange) changeResult.getChanges().get(i);

      out.write("            \n");
      out.write("                    <tr>\n");
      out.write("                        <td>\n");
      out.write("                            <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n");
      out.write("                                <tr>\n");
      out.write("                                    <td class=\"internal\" style=\"padding-right:5px;\">\n");
      out.write("                                        <img src=\"");
      out.print(element.getImage());
      out.write("\" />\n");
      out.write("                                    </td>\n");
      out.write("                                    <td class=\"internal\" width=\"100%\" nowrap=\"true\">\n");
      out.write("                                        ");
      out.print(element.getName());
      out.write("\n");
      out.write("                                    </td>                                        \n");
      out.write("                                </tr>\n");
      out.write("                            </table>\n");
      out.write("                        </td>\n");
      out.write("                        <td align=\"right\">\n");
      out.write("                            ");
      out.print(element.getDecoratedRevision());
      out.write("\n");
      out.write("                        </td>\n");
      out.write("                        <td nowrap=\"true\">\n");
      out.write("                            <span title=\"");
      out.print( Localization.getString("svnwebclient.revprops.ago", element.getAge()) );
      out.write('"');
      out.write('>');
      out.print(element.getDate());
      out.write("</span>\n");
      out.write("                        </td>\n");
      out.write("                        <td nowrap=\"true\">\n");
      out.write("                            ");
      out.print(element.getAuthor());
      out.write("\n");
      out.write("                        </td>\n");
                                
			if (element.isMultiLineComment()) {
				counter ++;
				String cell = "cell_" + counter;
				String tool = "tooltip_" + counter;

      out.write("	\n");
      out.write("						<td id=\"");
      out.print(cell);
      out.write("\" onmouseover=\"xstooltip_show('");
      out.print(tool);
      out.write("', '");
      out.print(cell);
      out.write("');\" onmouseout=\"xstooltip_hide('");
      out.print(tool);
      out.write("');\">									\n");
      out.write("							<div id=\"");
      out.print(tool);
      out.write("\" class=\"xstooltip\">\n");
      out.write("								");
      out.print(element.getTooltip());
      out.write("	\n");
      out.write("							</div>						\n");
      out.write("							<img src=\"images/multiline_text.gif\" style=\"position:absolute;margin-top:2px\" align=\"middle\" width=\"8\" height=\"9\">\n");
      out.write("		     				&nbsp;&nbsp;&nbsp;&nbsp;");
      out.print(element.getFirstLine());
      out.write("				\n");
      out.write("		     			</td>	\n");

			} else {								 

      out.write("				    \n");
      out.write("			         	<td>\n");
      out.write("                            ");
      out.print(element.getComment());
      out.write("\n");
      out.write("                        </td>\n");

			}

      out.write(" \n");
      out.write("                    </tr>\n");

        }                    

      out.write("        \n");
      out.write("                </table>\n");
      out.write("            </td>\n");
      out.write("        </tr>    \n");
      out.write("        <tr>\n");
      out.write("            <td width=\"100%\" style=\"padding-top:20px;padding-bottom:0px;\">\n");
      out.write("                <table cellspacing=\"0\" cellpadding=\"0\" width=\"100%\">\n");
      out.write("                    <tr>\n");
      out.write("                        <td align=\"left\">\n");
      out.write("                            <input type=\"submit\" class=\"button\" value=\"");
      out.print( Localization.getString("svnwebclient.generic.ok") );
      out.write("\"/>                \n");
      out.write("                        </td>\n");
      out.write("                        <td width=\"100%\"/>\n");
      out.write("                    </tr>\n");
      out.write("                </table>\n");
      out.write("            </td>    \n");
      out.write("        </tr>    \n");
      out.write("    </table>\n");
      out.write("</form>   \n");
      out.write("<script language=\"javascript\">\n");
      out.write("    firstsecond('table_list_of_files');\n");
      out.write("</script>\n");
      out.write("\n");
      out.write("    ");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try {
            if (response.isCommitted()) {
              out.flush();
            } else {
              out.clearBuffer();
            }
          } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
