2025-08-04 14:01:50,823 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-04 14:01:50,851 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-04 14:01:50,853 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-04 14:01:50,853 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,411.62 GB
 [Mon Aug 04 14:01:50 CST 2025]
2025-08-04 14:01:51,047 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-04 14:01:51,047 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Mon Aug 04 14:01:51 CST 2025]
2025-08-04 14:01:51,049 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-04 14:01:51,049 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Mon Aug 04 14:01:51 CST 2025]
2025-08-04 14:01:51,052 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-04 14:01:55,117 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Tue Aug 05 01:00:55 CST 2025
2025-08-04 14:01:55,118 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-04 14:01:55,119 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-04 14:01:55,128 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          4          5         44%          9          7          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Mon Aug 04 14:01:55 CST 2025]
2025-08-04 14:01:55,131 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-04 14:11:55,121 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-04 14:11:55,128 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-04 14:11:55,130 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:1
 [Mon Aug 04 14:11:55 CST 2025]
2025-08-04 14:11:55,142 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-04 14:11:55,142 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,411.56 GB
 [Mon Aug 04 14:11:55 CST 2025]
2025-08-04 14:11:55,353 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-04 14:11:55,353 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Mon Aug 04 14:11:55 CST 2025]
2025-08-04 14:11:55,355 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-04 14:11:55,355 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 19; Time: 0.263 s.
 [Mon Aug 04 14:11:55 CST 2025]
2025-08-04 14:11:55,357 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-04 14:11:55,358 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Mon Aug 04 14:11:55 CST 2025]
2025-08-04 14:11:55,478 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-04 14:11:55,479 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Mon Aug 04 14:11:55 CST 2025]
2025-08-04 14:11:55,626 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-04 14:21:55,128 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-04 14:21:55,130 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-04 14:21:55,130 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:1
 [Mon Aug 04 14:21:55 CST 2025]
2025-08-04 14:21:55,142 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-04 14:21:55,142 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,412.04 GB
 [Mon Aug 04 14:21:55 CST 2025]
2025-08-04 14:21:55,328 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-04 14:21:55,328 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Mon Aug 04 14:21:55 CST 2025]
2025-08-04 14:21:55,330 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-04 14:21:55,330 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 63; Time: 0.642 s.
 [Mon Aug 04 14:21:55 CST 2025]
2025-08-04 14:21:55,332 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-04 14:21:55,332 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Mon Aug 04 14:21:55 CST 2025]
2025-08-04 14:21:55,448 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-04 14:21:55,448 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  94.78% memory free after GC.
 [Mon Aug 04 14:21:55 CST 2025]
2025-08-04 14:21:55,589 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-04 14:31:55,134 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-04 14:31:55,135 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-04 14:31:55,136 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:1
 [Mon Aug 04 14:31:55 CST 2025]
2025-08-04 14:31:55,145 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-04 14:31:55,145 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,411 GB
 [Mon Aug 04 14:31:55 CST 2025]
2025-08-04 14:31:55,330 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-04 14:31:55,331 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Mon Aug 04 14:31:55 CST 2025]
2025-08-04 14:31:55,332 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-04 14:31:55,332 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 80; Time: 1.033 s.
 [Mon Aug 04 14:31:55 CST 2025]
2025-08-04 14:31:55,334 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-04 14:31:55,334 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Mon Aug 04 14:31:55 CST 2025]
2025-08-04 14:31:55,477 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-04 14:31:55,477 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  96.10% memory free after GC.
 [Mon Aug 04 14:31:55 CST 2025]
2025-08-04 14:31:55,681 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
2025-08-04 14:41:55,142 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-04 14:41:55,146 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-04 14:41:55,146 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:1
 [Mon Aug 04 14:41:55 CST 2025]
2025-08-04 14:41:55,161 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-04 14:41:55,162 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,411.98 GB
 [Mon Aug 04 14:41:55 CST 2025]
2025-08-04 14:41:55,356 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-04 14:41:55,356 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Mon Aug 04 14:41:55 CST 2025]
2025-08-04 14:41:55,358 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-04 14:41:55,358 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 81; Time: 1.069 s.
 [Mon Aug 04 14:41:55 CST 2025]
2025-08-04 14:41:55,360 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-04 14:41:55,360 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Mon Aug 04 14:41:55 CST 2025]
2025-08-04 14:41:55,482 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-04 14:41:55,482 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  96.10% memory free after GC.
 [Mon Aug 04 14:41:55 CST 2025]
2025-08-04 14:41:55,626 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
