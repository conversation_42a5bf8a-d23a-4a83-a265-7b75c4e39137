2025-08-04 22:05:27,007 [main | u:p] INFO  com.polarion.platform.monitoring - Full monitoring results are stored in file /opt/polarion/data/workspace/monitoring/results.txt
2025-08-04 22:05:27,030 [main | u:p] INFO  com.polarion.platform.monitoring - Executing actions from stage PREBOOT
2025-08-04 22:05:27,032 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-04 22:05:27,032 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,407.43 GB
 [Mon Aug 04 22:05:27 CST 2025]
2025-08-04 22:05:27,232 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.jvm.version'
2025-08-04 22:05:27,232 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.jvm.version (JVM Version) = JVM Version 
Microsoft OpenJDK 64-Bit Server VM 11.0.27+6-LTS
 [Mon Aug 04 22:05:27 CST 2025]
2025-08-04 22:05:27,235 [main | u:p] INFO  com.polarion.platform.monitoring - Executing action 'system.info.os.version'
2025-08-04 22:05:27,235 [main | u:p] INFO  com.polarion.platform.monitoring - system.info.os.version (OS Version) = OS Version 
Mac OS X 15.5 aarch64
 [Mon Aug 04 22:05:27 CST 2025]
2025-08-04 22:05:27,237 [main | u:p] INFO  com.polarion.platform.monitoring - Finished with actions from stage PREBOOT: {OK=3}
2025-08-04 22:05:31,499 [main | u:p] INFO  com.polarion.platform.monitoring - Next slow periodic actions will be executed on Tue Aug 05 01:00:31 CST 2025
2025-08-04 22:05:31,501 [main] INFO  com.polarion.platform.monitoring - Executing actions from stage POSTBOOT
2025-08-04 22:05:31,501 [main] INFO  com.polarion.platform.monitoring - Executing action 'polarion.info.cache.statistics'
2025-08-04 22:05:31,528 [main] INFO  com.polarion.platform.monitoring - polarion.info.cache.statistics (Statistics of caches) = Statistics of caches 
                         CACHE       HITS     MISSES  HIT_RATIO       GETS       PUTS    REMOVAL   EVICTION 
     attachments-content-cache          0          0          0%          0          0          0          0 
                baseline-cache          0          0          0%          0          0          0          0 
                            bq          0          0          0%          0          0          0          0 
                dao-attachment          0          0          0%          0          0          0          0 
                   dao-comment          0          0          0%          0          0          0          0 
                    dao-global          0          4          0%          4          0          0          0 
                    dao-module          0          0          0%          0          0          0          0 
          dao-moduleattachment          0          0          0%          0          0          0          0 
             dao-modulecomment          0          0          0%          0          0          0          0 
                      dao-plan          0          0          0%          0          0          0          0 
                   dao-project          0          0          0%          0          0          0          0 
              dao-projectgroup          0          6          0%          6          3          0          0 
                  dao-richpage          0          0          0%          0          0          0          0 
        dao-richpageattachment          0          0          0%          0          0          0          0 
                   dao-testrun          0          0          0%          0          0          0          0 
         dao-testrunattachment          0          0          0%          0          0          0          0 
                      dao-user          0          0          0%          0          0          0          0 
                  dao-wikipage          0          0          0%          0          0          0          0 
        dao-wikipageattachment          0          0          0%          0          0          0          0 
                  dao-workitem          0          0          0%          0          0          0          0 
      derived-linked-revisions          0          0          0%          0          0          0          0 
                  formulas-svg          0          0          0%          0          0          0          0 
                github-commits          0          0          0%          0          0          0          0 
            historical-queries          0          0          0%          0          0          0          0 
      historical-queries-sizes          0          0          0%          0          0          0          0 
        oslc-linked-item-cache          0          0          0%          0          0          0          0 
               plan-statistics          0          0          0%          0          0          0          0 
                   rmd-default          3          5         38%          8          6          0          0 
                   ss-combined          0          0          0%          0          0          0          0 
                    ss-context          0          0          0%          0          0          0          0 
                     wiki-page          2          8         20%         10          9          1          0 
              wiki-page-exists          0          0          0%          0          9          1          0 

 [Mon Aug 04 22:05:31 CST 2025]
2025-08-04 22:05:31,530 [main] INFO  com.polarion.platform.monitoring - Finished with actions from stage POSTBOOT: {OK=1}
2025-08-04 22:15:31,518 [Monitoring] INFO  com.polarion.platform.monitoring - Executing actions from stage PERIODIC with speed FAST
2025-08-04 22:15:31,528 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'polarion.sso.statistics'
2025-08-04 22:15:31,532 [Monitoring] INFO  com.polarion.platform.monitoring - polarion.sso.statistics (Statistics of all active sso sessions) = Statistics of logged users 
Logged active users:1
 [Mon Aug 04 22:15:31 CST 2025]
2025-08-04 22:15:31,549 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.disk.data.free'
2025-08-04 22:15:31,549 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.disk.data.free (Free data disk '/opt/polarion/data' space) = Disk Space 
1,407.38 GB
 [Mon Aug 04 22:15:31 CST 2025]
2025-08-04 22:15:31,973 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.full.cycles'
2025-08-04 22:15:31,974 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.full.cycles (Full Garbage Collector cycles) = Full Garbage Collector cycles 
Full GC cycles: 0; Time: 0.0 s.
 [Mon Aug 04 22:15:31 CST 2025]
2025-08-04 22:15:31,981 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.garbageCollector.normal.cycles'
2025-08-04 22:15:31,982 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.garbageCollector.normal.cycles (Normal Garbage Collector cycles) = Normal Garbage Collector cycles 
Normal GC cycles: 15; Time: 0.19 s.
 [Mon Aug 04 22:15:31 CST 2025]
2025-08-04 22:15:31,989 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.nonheap.free'
2025-08-04 22:15:31,990 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.nonheap.free (Free non-heap memory) = Java Metaspace Memory 
Unlimited
 [Mon Aug 04 22:15:31 CST 2025]
2025-08-04 22:15:32,125 [Monitoring] INFO  com.polarion.platform.monitoring - Executing action 'system.info.memory.oldGen.free'
2025-08-04 22:15:32,126 [Monitoring] INFO  com.polarion.platform.monitoring - system.info.memory.oldGen.free (Free Old Gen memory after GC) = Java Heap Memory 
Old Gen pool has  100.00% memory free after GC.
 [Mon Aug 04 22:15:32 CST 2025]
2025-08-04 22:15:32,276 [Monitoring] INFO  com.polarion.platform.monitoring - Finished with actions from stage PERIODIC: {OK=6}
