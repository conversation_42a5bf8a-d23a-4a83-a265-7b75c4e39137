2025-08-03 11:49:48,220 [Catalina-utility-2] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-03 11:49:49,078 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-03 11:49:49,081 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[90]')
2025-08-03 11:49:49,081 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[90]')
2025-08-03 11:49:49,085 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[96]')
2025-08-03 11:49:49,092 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-03 11:49:49,094 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[90]')
2025-08-03 11:49:49,097 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[92]')
2025-08-03 11:49:50,404 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-03 11:49:50,555 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6e0cd144-7f000001-7403b7df-9d09ccf6] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-03 11:49:50,581 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-03 11:49:50,582 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
