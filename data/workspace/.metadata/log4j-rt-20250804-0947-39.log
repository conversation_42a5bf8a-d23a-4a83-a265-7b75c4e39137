2025-08-04 09:47:46,257 [Catalina-utility-6] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-04 09:47:46,988 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-04 09:47:46,991 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 09:47:46,991 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 09:47:46,993 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[88]')
2025-08-04 09:47:46,997 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-04 09:47:46,998 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 09:47:46,998 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[84]')
2025-08-04 09:49:32,552 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-04 09:49:32,665 [ajp-nio-127.0.0.1-8889-exec-1 | cID:72c50a62-c0a844bd-54793fdd-b53c3bc7] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-04 09:49:32,716 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-04 09:49:32,717 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
