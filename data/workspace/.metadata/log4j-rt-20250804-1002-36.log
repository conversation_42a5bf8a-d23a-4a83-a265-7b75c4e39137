2025-08-04 10:02:43,529 [Catalina-utility-5] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-04 10:02:44,127 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-04 10:02:44,127 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 10:02:44,128 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[84]')
2025-08-04 10:02:44,128 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-04 10:02:44,131 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 10:02:44,131 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 10:02:44,132 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[88]')
2025-08-04 10:02:44,902 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-04 10:02:45,014 [ajp-nio-127.0.0.1-8889-exec-1 | cID:72d12185-7f000001-417a303b-748c4ffb] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-04 10:02:45,033 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-04 10:02:45,033 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
