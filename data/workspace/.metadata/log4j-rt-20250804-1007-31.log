2025-08-04 10:07:39,178 [Catalina-utility-5] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-04 10:07:39,705 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-04 10:07:39,707 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 10:07:39,708 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 10:07:39,710 [Catalina-utility-5] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[88]')
2025-08-04 10:07:39,887 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-04 10:07:39,888 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[82]')
2025-08-04 10:07:39,888 [Catalina-utility-5] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[84]')
2025-08-04 10:07:40,570 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-04 10:07:40,688 [ajp-nio-127.0.0.1-8889-exec-1 | cID:72d5a47e-7f000001-39c1b088-5e29079d] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-04 10:07:40,708 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-04 10:07:40,708 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
