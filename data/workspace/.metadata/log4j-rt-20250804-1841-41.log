2025-08-04 18:41:50,486 [Catalina-utility-6] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-04 18:41:51,619 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-04 18:41:51,621 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 18:41:51,622 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 18:41:51,626 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[89]')
2025-08-04 18:41:51,845 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-04 18:41:51,845 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 18:41:51,846 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[85]')
2025-08-04 18:41:54,003 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-04 18:41:54,202 [ajp-nio-127.0.0.1-8889-exec-1 | cID:74ac6dfb-c0a844bd-653e2946-a855fc08] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-04 18:41:54,230 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-04 18:41:54,230 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
