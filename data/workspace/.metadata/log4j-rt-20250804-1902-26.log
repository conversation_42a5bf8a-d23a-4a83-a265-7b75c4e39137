2025-08-04 19:02:32,839 [Catalina-utility-2] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-04 19:02:33,670 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-04 19:02:33,673 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 19:02:33,674 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 19:02:33,675 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[89]')
2025-08-04 19:02:33,681 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-04 19:02:33,681 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 19:02:33,682 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[85]')
2025-08-04 19:02:35,319 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-04 19:02:35,499 [ajp-nio-127.0.0.1-8889-exec-1 | cID:74bf5ed6-7f000001-5cc430cb-6deb5ef7] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-04 19:02:35,531 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-04 19:02:35,532 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
