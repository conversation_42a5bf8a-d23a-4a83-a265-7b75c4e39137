2025-08-04 19:20:14,940 [Catalina-utility-2] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-04 19:20:15,814 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-04 19:20:15,817 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 19:20:15,818 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 19:20:15,819 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[89]')
2025-08-04 19:20:15,823 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-04 19:20:15,824 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 19:20:15,825 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[85]')
2025-08-04 19:20:16,739 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-04 19:20:16,841 [ajp-nio-127.0.0.1-8889-exec-1 | cID:74cf90b7-7f000001-6f5db8ff-9e106e94] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-04 19:20:16,859 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-04 19:20:16,859 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
