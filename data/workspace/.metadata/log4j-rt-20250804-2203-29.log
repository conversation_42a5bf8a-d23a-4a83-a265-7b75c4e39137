2025-08-04 22:03:39,153 [Catalina-utility-3] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-04 22:03:39,820 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-04 22:03:39,822 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 22:03:39,823 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 22:03:39,824 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[89]')
2025-08-04 22:03:39,828 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-04 22:03:39,828 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[83]')
2025-08-04 22:03:39,829 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[85]')
2025-08-04 22:03:40,408 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-04 22:03:40,518 [ajp-nio-127.0.0.1-8889-exec-1 | cID:75652855-c0a844bd-4faad6d6-dd802cd5] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-04 22:03:40,538 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-04 22:03:40,538 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
