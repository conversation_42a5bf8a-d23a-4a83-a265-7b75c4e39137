2025-08-05 16:59:10,832 [Catalina-utility-3] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-08-05 16:59:11,742 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-08-05 16:59:11,742 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[85]')
2025-08-05 16:59:11,743 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[87]')
2025-08-05 16:59:11,747 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-08-05 16:59:11,749 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[85]')
2025-08-05 16:59:11,750 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[85]')
2025-08-05 16:59:11,753 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[91]')
2025-08-05 16:59:12,817 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-08-05 16:59:12,951 [ajp-nio-127.0.0.1-8889-exec-1 | cID:7974c69f-c0a844bd-19cc229b-5543ad87] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-08-05 16:59:12,978 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-08-05 16:59:12,978 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
