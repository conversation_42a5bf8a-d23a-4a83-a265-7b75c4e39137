2025-08-02 23:17:42,187 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 23:17:42,187 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-02 23:17:42,188 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 23:17:42,188 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-02 23:17:42,188 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-02 23:17:42,188 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:42,188 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-02 23:17:46,760 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-02 23:17:46,935 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.175 s. ]
2025-08-02 23:17:46,935 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-02 23:17:47,025 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0893 s. ]
2025-08-02 23:17:47,082 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-02 23:17:47,209 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-02 23:17:47,462 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.28 s. ]
2025-08-02 23:17:47,576 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:47,576 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-02 23:17:47,608 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-08-02 23:17:47,609 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:47,609 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-02 23:17:47,614 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-02 23:17:47,615 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-02 23:17:47,615 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-02 23:17:47,615 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-02 23:17:47,615 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-02 23:17:47,615 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-02 23:17:47,626 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-02 23:17:47,775 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-02 23:17:47,883 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-02 23:17:48,340 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-08-02 23:17:48,353 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:48,353 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-02 23:17:48,602 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-02 23:17:48,614 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-08-02 23:17:48,640 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:48,640 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-02 23:17:48,644 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-02 23:17:48,700 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-02 23:17:48,762 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-02 23:17:48,813 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-02 23:17:48,839 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-02 23:17:48,869 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-02 23:17:48,913 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-02 23:17:48,961 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-02 23:17:49,014 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-02 23:17:49,014 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.4 s. ]
2025-08-02 23:17:49,014 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:49,014 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-02 23:17:49,031 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-02 23:17:49,031 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:49,031 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-02 23:17:49,159 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-02 23:17:49,163 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-02 23:17:49,338 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.31 s. ]
2025-08-02 23:17:49,340 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:49,340 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-02 23:17:49,350 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-02 23:17:49,350 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-02 23:17:49,350 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-02 23:17:52,480 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.13 s. ]
2025-08-02 23:17:52,481 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-02 23:17:52,481 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.3 s. ]
2025-08-02 23:17:52,481 [main] INFO  com.polarion.platform.startup - ****************************************************************
