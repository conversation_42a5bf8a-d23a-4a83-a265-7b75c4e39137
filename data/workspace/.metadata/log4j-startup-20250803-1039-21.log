2025-08-03 10:39:21,688 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 10:39:21,688 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 10:39:21,688 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 10:39:21,688 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 10:39:21,688 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 10:39:21,689 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:21,689 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 10:39:26,352 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 10:39:26,514 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.162 s. ]
2025-08-03 10:39:26,514 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 10:39:26,590 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0752 s. ]
2025-08-03 10:39:26,677 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 10:39:26,828 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-03 10:39:27,106 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.43 s. ]
2025-08-03 10:39:27,212 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:27,212 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 10:39:27,249 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-03 10:39:27,249 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:27,249 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 10:39:27,256 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-03 10:39:27,256 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-03 10:39:27,257 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-03 10:39:27,256 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-03 10:39:27,257 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-03 10:39:27,257 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-03 10:39:27,267 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 10:39:27,420 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 10:39:27,514 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 10:39:28,222 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.97 s. ]
2025-08-03 10:39:28,249 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:28,249 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-03 10:39:28,628 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 10:39:28,644 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.42 s. ]
2025-08-03 10:39:28,676 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:28,676 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-03 10:39:28,681 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 10:39:28,751 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 10:39:28,816 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 10:39:28,870 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 10:39:28,902 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 10:39:28,932 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 10:39:28,988 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 10:39:29,054 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 10:39:29,119 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 10:39:29,119 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.48 s. ]
2025-08-03 10:39:29,119 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:29,119 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-03 10:39:29,140 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-03 10:39:29,140 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:29,140 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 10:39:29,278 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 10:39:29,282 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 10:39:29,420 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.28 s. ]
2025-08-03 10:39:29,422 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:29,422 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 10:39:29,437 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-08-03 10:39:29,437 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 10:39:29,437 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 10:39:33,659 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.22 s. ]
2025-08-03 10:39:33,659 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 10:39:33,659 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12 s. ]
2025-08-03 10:39:33,659 [main] INFO  com.polarion.platform.startup - ****************************************************************
