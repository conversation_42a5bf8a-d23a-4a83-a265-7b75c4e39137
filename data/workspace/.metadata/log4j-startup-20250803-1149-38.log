2025-08-03 11:49:38,549 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 11:49:38,549 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 11:49:38,550 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 11:49:38,550 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 11:49:38,550 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 11:49:38,550 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:38,550 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 11:49:43,405 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 11:49:43,564 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.158 s. ]
2025-08-03 11:49:43,564 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 11:49:43,638 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0739 s. ]
2025-08-03 11:49:43,714 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 11:49:43,898 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-03 11:49:44,189 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.65 s. ]
2025-08-03 11:49:44,298 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:44,298 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 11:49:44,332 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-03 11:49:44,333 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:44,333 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 11:49:44,339 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-03 11:49:44,339 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-03 11:49:44,339 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-03 11:49:44,339 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-03 11:49:44,340 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-03 11:49:44,340 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-03 11:49:44,350 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 11:49:44,511 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 11:49:44,610 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 11:49:45,303 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.97 s. ]
2025-08-03 11:49:45,320 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:45,320 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-03 11:49:45,647 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 11:49:45,662 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.36 s. ]
2025-08-03 11:49:45,699 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:45,699 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-03 11:49:45,705 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 11:49:45,767 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 11:49:45,836 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 11:49:45,888 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 11:49:45,922 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 11:49:45,957 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 11:49:46,009 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 11:49:46,074 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 11:49:46,142 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 11:49:46,142 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.48 s. ]
2025-08-03 11:49:46,142 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:46,142 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-03 11:49:46,161 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-03 11:49:46,162 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:46,162 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 11:49:46,349 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 11:49:46,354 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 11:49:46,502 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.34 s. ]
2025-08-03 11:49:46,503 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:46,503 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 11:49:46,511 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 11:49:46,511 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 11:49:46,511 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 11:49:50,630 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.12 s. ]
2025-08-03 11:49:50,630 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 11:49:50,630 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.1 s. ]
2025-08-03 11:49:50,630 [main] INFO  com.polarion.platform.startup - ****************************************************************
