2025-08-03 14:43:05,212 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 14:43:05,212 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 14:43:05,212 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 14:43:05,213 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 14:43:05,213 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 14:43:05,213 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:05,213 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 14:43:10,084 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 14:43:10,256 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.172 s. ]
2025-08-03 14:43:10,256 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 14:43:10,315 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0585 s. ]
2025-08-03 14:43:10,389 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 14:43:10,551 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-03 14:43:10,812 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.61 s. ]
2025-08-03 14:43:10,923 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:10,923 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 14:43:10,952 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-03 14:43:10,952 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:10,952 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 14:43:10,958 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-03 14:43:10,958 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-03 14:43:10,958 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-03 14:43:10,959 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-03 14:43:10,959 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-03 14:43:10,959 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-03 14:43:10,966 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 14:43:11,127 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 14:43:11,228 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 14:43:11,729 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.78 s. ]
2025-08-03 14:43:11,747 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:11,747 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-03 14:43:12,011 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 14:43:12,025 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.3 s. ]
2025-08-03 14:43:12,053 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:12,053 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-03 14:43:12,057 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 14:43:12,114 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 14:43:12,182 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 14:43:12,235 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 14:43:12,271 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 14:43:12,305 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 14:43:12,359 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 14:43:12,421 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 14:43:12,471 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 14:43:12,471 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.45 s. ]
2025-08-03 14:43:12,471 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:12,471 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-03 14:43:12,488 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-03 14:43:12,489 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:12,489 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 14:43:12,599 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 14:43:12,603 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 14:43:12,733 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-08-03 14:43:12,734 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:12,734 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 14:43:12,743 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 14:43:12,743 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 14:43:12,743 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 14:43:15,758 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.01 s. ]
2025-08-03 14:43:15,758 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 14:43:15,758 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.5 s. ]
2025-08-03 14:43:15,758 [main] INFO  com.polarion.platform.startup - ****************************************************************
