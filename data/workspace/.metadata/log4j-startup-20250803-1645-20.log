2025-08-03 16:45:20,398 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 16:45:20,398 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 16:45:20,398 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 16:45:20,398 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 16:45:20,398 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 16:45:20,398 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:20,398 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 16:45:26,086 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 16:45:26,264 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.177 s. ]
2025-08-03 16:45:26,264 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 16:45:26,342 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0779 s. ]
2025-08-03 16:45:26,412 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 16:45:26,563 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-03 16:45:26,870 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.48 s. ]
2025-08-03 16:45:26,995 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:26,995 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 16:45:27,032 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.16 s. ]
2025-08-03 16:45:27,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:27,033 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 16:45:27,040 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-03 16:45:27,040 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-03 16:45:27,040 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-03 16:45:27,040 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-03 16:45:27,040 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-03 16:45:27,040 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-03 16:45:27,053 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 16:45:27,244 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 16:45:27,316 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 16:45:27,861 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.83 s. ]
2025-08-03 16:45:27,881 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:27,881 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-03 16:45:28,227 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 16:45:28,238 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.38 s. ]
2025-08-03 16:45:28,271 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:28,271 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-03 16:45:28,278 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 16:45:28,350 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 16:45:28,437 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 16:45:28,492 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 16:45:28,531 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 16:45:28,567 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 16:45:28,656 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 16:45:28,738 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 16:45:28,825 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 16:45:28,825 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.59 s. ]
2025-08-03 16:45:28,825 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:28,825 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-03 16:45:28,847 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-03 16:45:28,847 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:28,847 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 16:45:28,999 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 16:45:29,003 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 16:45:29,203 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.36 s. ]
2025-08-03 16:45:29,204 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:29,204 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 16:45:29,215 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 16:45:29,215 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 16:45:29,215 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 16:45:32,911 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.7 s. ]
2025-08-03 16:45:32,912 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 16:45:32,912 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.5 s. ]
2025-08-03 16:45:32,912 [main] INFO  com.polarion.platform.startup - ****************************************************************
