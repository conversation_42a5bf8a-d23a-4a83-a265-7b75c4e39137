2025-08-03 17:04:30,652 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 17:04:30,652 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 17:04:30,652 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 17:04:30,652 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 17:04:30,652 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 17:04:30,652 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:30,652 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 17:04:35,826 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 17:04:36,001 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.175 s. ]
2025-08-03 17:04:36,001 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 17:04:36,072 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0707 s. ]
2025-08-03 17:04:36,201 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 17:04:36,419 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-03 17:04:36,745 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.1 s. ]
2025-08-03 17:04:36,876 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:36,876 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 17:04:36,918 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.17 s. ]
2025-08-03 17:04:36,918 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:36,918 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 17:04:36,925 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-03 17:04:36,926 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-03 17:04:36,925 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-03 17:04:36,925 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-03 17:04:36,926 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-03 17:04:36,925 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-03 17:04:36,936 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 17:04:37,110 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 17:04:37,223 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 17:04:38,006 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.09 s. ]
2025-08-03 17:04:38,026 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:38,026 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-03 17:04:38,333 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 17:04:38,349 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.34 s. ]
2025-08-03 17:04:38,381 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:38,381 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-03 17:04:38,387 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 17:04:38,546 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 17:04:38,652 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 17:04:38,704 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 17:04:38,730 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 17:04:38,754 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 17:04:38,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 17:04:38,847 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 17:04:38,902 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 17:04:38,902 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.55 s. ]
2025-08-03 17:04:38,902 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:38,902 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-03 17:04:38,929 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.03 s. ]
2025-08-03 17:04:38,929 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:38,929 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 17:04:39,105 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 17:04:39,112 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 17:04:39,262 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.33 s. ]
2025-08-03 17:04:39,264 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:39,264 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 17:04:39,276 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 17:04:39,276 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:04:39,276 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 17:04:47,258 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 7.98 s. ]
2025-08-03 17:04:47,259 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 17:04:47,259 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 16.6 s. ]
2025-08-03 17:04:47,259 [main] INFO  com.polarion.platform.startup - ****************************************************************
