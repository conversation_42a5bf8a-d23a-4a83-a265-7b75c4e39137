2025-08-03 17:18:47,406 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 17:18:47,407 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 17:18:47,407 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 17:18:47,407 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 17:18:47,407 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 17:18:47,407 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:47,407 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 17:18:52,278 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 17:18:52,439 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.16 s. ]
2025-08-03 17:18:52,439 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 17:18:52,528 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0892 s. ]
2025-08-03 17:18:52,600 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 17:18:52,728 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-03 17:18:53,011 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.61 s. ]
2025-08-03 17:18:53,126 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:53,126 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 17:18:53,161 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-08-03 17:18:53,161 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:53,161 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 17:18:53,167 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-03 17:18:53,167 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-03 17:18:53,167 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-03 17:18:53,167 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-03 17:18:53,167 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-03 17:18:53,167 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-03 17:18:53,177 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 17:18:53,350 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 17:18:53,452 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 17:18:54,102 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.94 s. ]
2025-08-03 17:18:54,119 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:54,119 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-03 17:18:54,404 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 17:18:54,429 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-08-03 17:18:54,463 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:54,463 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-03 17:18:54,470 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 17:18:54,534 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 17:18:54,608 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 17:18:54,666 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 17:18:54,698 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 17:18:54,733 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 17:18:54,783 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 17:18:54,848 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 17:18:54,906 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 17:18:54,906 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.48 s. ]
2025-08-03 17:18:54,906 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:54,906 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-03 17:18:54,931 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-03 17:18:54,931 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:54,931 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 17:18:55,090 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 17:18:55,097 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 17:18:55,264 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.33 s. ]
2025-08-03 17:18:55,264 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:55,264 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 17:18:55,278 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 17:18:55,278 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 17:18:55,278 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 17:18:58,712 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.43 s. ]
2025-08-03 17:18:58,713 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 17:18:58,714 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.3 s. ]
2025-08-03 17:18:58,714 [main] INFO  com.polarion.platform.startup - ****************************************************************
