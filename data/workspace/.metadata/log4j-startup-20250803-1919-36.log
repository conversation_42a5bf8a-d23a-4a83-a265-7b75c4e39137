2025-08-03 19:19:36,835 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:19:36,835 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 19:19:36,835 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:19:36,835 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 19:19:36,835 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 19:19:36,835 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:36,835 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 19:19:41,100 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 19:19:41,241 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.141 s. ]
2025-08-03 19:19:41,242 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 19:19:41,284 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0427 s. ]
2025-08-03 19:19:41,330 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 19:19:41,450 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-03 19:19:41,685 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.86 s. ]
2025-08-03 19:19:41,768 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:41,768 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 19:19:41,797 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-03 19:19:41,797 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:41,797 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 19:19:41,802 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-03 19:19:41,802 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-03 19:19:41,802 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-03 19:19:41,802 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-03 19:19:41,802 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-03 19:19:41,802 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-03 19:19:41,809 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 19:19:41,920 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 19:19:42,018 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 19:19:42,475 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.68 s. ]
2025-08-03 19:19:42,510 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:42,510 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 4/9)
2025-08-03 19:19:42,518 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 19:19:42,564 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 19:19:42,609 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 19:19:42,656 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 19:19:42,679 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 19:19:42,704 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 19:19:42,740 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 19:19:42,780 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 19:19:42,816 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 19:19:42,816 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-08-03 19:19:42,816 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:42,816 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 5/9)
2025-08-03 19:19:42,830 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-03 19:19:42,830 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:42,830 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 6/9)
2025-08-03 19:19:43,042 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 19:19:43,054 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-03 19:19:43,054 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:43,054 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 19:19:43,150 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 19:19:43,152 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 19:19:43,245 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.19 s. ]
2025-08-03 19:19:43,246 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:43,246 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 19:19:43,252 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 19:19:43,252 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:19:43,252 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 19:19:45,440 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.19 s. ]
2025-08-03 19:19:45,440 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:19:45,440 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.61 s. ]
2025-08-03 19:19:45,440 [main] INFO  com.polarion.platform.startup - ****************************************************************
