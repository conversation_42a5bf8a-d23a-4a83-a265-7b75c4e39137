2025-08-03 19:20:53,764 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:20:53,764 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 19:20:53,764 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:20:53,764 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 19:20:53,764 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 19:20:53,764 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:20:53,764 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 19:20:58,060 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 19:20:58,176 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.116 s. ]
2025-08-03 19:20:58,176 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 19:20:58,213 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0367 s. ]
2025-08-03 19:20:58,258 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 19:20:58,361 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-03 19:20:58,642 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.88 s. ]
2025-08-03 19:20:58,753 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:20:58,753 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 19:20:58,775 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-08-03 19:20:58,775 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:20:58,775 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 19:20:58,781 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-03 19:20:58,781 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-03 19:20:58,781 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-03 19:20:58,781 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-03 19:20:58,781 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-03 19:20:58,782 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-03 19:20:58,787 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 19:20:58,895 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 19:20:58,994 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 19:20:59,469 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-08-03 19:20:59,501 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:20:59,501 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 4/9)
2025-08-03 19:20:59,508 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 19:20:59,555 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 19:20:59,599 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 19:20:59,639 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 19:20:59,666 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 19:20:59,694 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 19:20:59,720 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 19:20:59,753 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 19:20:59,776 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 19:20:59,776 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-08-03 19:20:59,776 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:20:59,776 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 5/9)
2025-08-03 19:20:59,791 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-03 19:20:59,792 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:20:59,792 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 6/9)
2025-08-03 19:20:59,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 19:20:59,982 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.19 s. ]
2025-08-03 19:20:59,982 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:20:59,982 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 19:21:00,071 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 19:21:00,073 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 19:21:00,175 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.19 s. ]
2025-08-03 19:21:00,176 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:00,176 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 19:21:00,183 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 19:21:00,183 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:00,183 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 19:21:02,445 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.26 s. ]
2025-08-03 19:21:02,446 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:21:02,446 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.68 s. ]
2025-08-03 19:21:02,446 [main] INFO  com.polarion.platform.startup - ****************************************************************
