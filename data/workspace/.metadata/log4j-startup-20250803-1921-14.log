2025-08-03 19:21:14,614 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:21:14,614 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 19:21:14,614 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:21:14,614 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 19:21:14,614 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 19:21:14,614 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:14,614 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 19:21:18,973 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 19:21:19,095 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.122 s. ]
2025-08-03 19:21:19,095 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 19:21:19,130 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.035 s. ]
2025-08-03 19:21:19,187 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 19:21:19,294 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-03 19:21:19,541 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.93 s. ]
2025-08-03 19:21:19,615 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:19,615 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 19:21:19,637 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-03 19:21:19,637 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:19,637 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 19:21:19,641 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-03 19:21:19,642 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-08-03 19:21:19,642 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-03 19:21:19,642 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-03 19:21:19,642 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-03 19:21:19,642 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-03 19:21:19,651 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 19:21:19,769 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 19:21:19,867 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 19:21:20,339 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-08-03 19:21:20,374 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:20,374 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 4/9)
2025-08-03 19:21:20,381 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 19:21:20,422 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 19:21:20,464 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 19:21:20,501 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 19:21:20,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 19:21:20,548 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 19:21:20,580 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 19:21:20,606 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 19:21:20,633 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 19:21:20,633 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-08-03 19:21:20,633 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:20,633 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 5/9)
2025-08-03 19:21:20,646 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-03 19:21:20,646 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:20,646 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 6/9)
2025-08-03 19:21:20,830 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 19:21:20,839 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.19 s. ]
2025-08-03 19:21:20,840 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:20,840 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 19:21:20,930 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 19:21:20,932 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 19:21:21,041 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.2 s. ]
2025-08-03 19:21:21,042 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:21,042 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 19:21:21,049 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 19:21:21,049 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:21,049 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 19:21:24,464 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.41 s. ]
2025-08-03 19:21:24,466 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:21:24,466 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.85 s. ]
2025-08-03 19:21:24,466 [main] INFO  com.polarion.platform.startup - ****************************************************************
