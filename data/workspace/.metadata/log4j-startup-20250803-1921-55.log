2025-08-03 19:21:56,057 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:21:56,058 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-03 19:21:56,058 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:21:56,058 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-03 19:21:56,058 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-03 19:21:56,058 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:21:56,058 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-03 19:22:00,479 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-03 19:22:00,591 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.112 s. ]
2025-08-03 19:22:00,591 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-03 19:22:00,630 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0385 s. ]
2025-08-03 19:22:00,686 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-03 19:22:00,805 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-03 19:22:01,080 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.03 s. ]
2025-08-03 19:22:01,179 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:22:01,179 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-03 19:22:01,199 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-03 19:22:01,200 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:22:01,200 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-03 19:22:01,205 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-03 19:22:01,205 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-03 19:22:01,205 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-08-03 19:22:01,205 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-03 19:22:01,206 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-03 19:22:01,205 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-03 19:22:01,212 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-03 19:22:01,329 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-03 19:22:01,464 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-03 19:22:01,932 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-08-03 19:22:02,000 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:22:02,000 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 4/9)
2025-08-03 19:22:02,023 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-03 19:22:02,098 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-03 19:22:02,154 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-03 19:22:02,208 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-03 19:22:02,238 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-03 19:22:02,267 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-03 19:22:02,313 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-03 19:22:02,365 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-03 19:22:02,420 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-03 19:22:02,421 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.49 s. ]
2025-08-03 19:22:02,421 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:22:02,421 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 5/9)
2025-08-03 19:22:02,438 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-03 19:22:02,439 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:22:02,439 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 6/9)
2025-08-03 19:22:02,752 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-03 19:22:02,764 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-08-03 19:22:02,765 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:22:02,766 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-03 19:22:02,881 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-03 19:22:02,884 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-03 19:22:03,032 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-08-03 19:22:03,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:22:03,033 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-03 19:22:03,041 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-03 19:22:03,041 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-03 19:22:03,041 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-03 19:22:05,806 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.76 s. ]
2025-08-03 19:22:05,806 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-03 19:22:05,806 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.75 s. ]
2025-08-03 19:22:05,806 [main] INFO  com.polarion.platform.startup - ****************************************************************
