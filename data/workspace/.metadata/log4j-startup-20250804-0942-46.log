2025-08-04 09:42:46,381 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:42:46,381 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 09:42:46,381 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:42:46,382 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 09:42:46,382 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 09:42:46,382 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:46,382 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 09:42:50,503 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 09:42:50,655 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.152 s. ]
2025-08-04 09:42:50,655 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 09:42:50,692 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0373 s. ]
2025-08-04 09:42:50,740 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 09:42:50,879 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 09:42:51,104 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.73 s. ]
2025-08-04 09:42:51,185 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:51,185 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 09:42:51,216 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 09:42:51,216 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:51,216 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 09:42:51,221 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 09:42:51,221 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 09:42:51,221 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-04 09:42:51,221 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 09:42:51,222 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 09:42:51,221 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 09:42:51,227 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 09:42:51,320 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 09:42:51,448 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 09:42:51,934 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-08-04 09:42:51,966 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:51,966 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 4/9)
2025-08-04 09:42:51,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 09:42:52,021 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 09:42:52,060 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 09:42:52,094 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 09:42:52,110 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 09:42:52,128 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 09:42:52,149 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 09:42:52,174 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 09:42:52,198 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 09:42:52,198 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.26 s. ]
2025-08-04 09:42:52,198 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:52,198 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 5/9)
2025-08-04 09:42:52,213 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 09:42:52,213 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:52,213 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 6/9)
2025-08-04 09:42:52,405 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 09:42:52,413 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.2 s. ]
2025-08-04 09:42:52,413 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:52,413 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 09:42:52,512 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 09:42:52,515 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 09:42:52,623 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-04 09:42:52,624 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:52,624 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 09:42:52,631 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 09:42:52,631 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:42:52,631 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 09:42:54,717 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.09 s. ]
2025-08-04 09:42:54,718 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:42:54,718 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.34 s. ]
2025-08-04 09:42:54,718 [main] INFO  com.polarion.platform.startup - ****************************************************************
