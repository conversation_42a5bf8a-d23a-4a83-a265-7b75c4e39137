2025-08-04 09:44:56,683 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:44:56,683 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 09:44:56,683 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:44:56,683 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 09:44:56,683 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 09:44:56,683 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:44:56,683 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 09:45:00,744 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 09:45:00,870 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.125 s. ]
2025-08-04 09:45:00,870 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 09:45:00,906 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0365 s. ]
2025-08-04 09:45:00,951 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 09:45:01,053 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 09:45:01,279 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.6 s. ]
2025-08-04 09:45:01,356 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:01,356 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 09:45:01,377 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 09:45:01,377 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:01,377 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 09:45:01,381 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 09:45:01,381 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 09:45:01,382 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-04 09:45:01,381 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 09:45:01,382 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-04 09:45:01,382 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 09:45:01,387 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 09:45:01,489 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 09:45:01,603 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 09:45:02,081 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-08-04 09:45:02,092 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:02,092 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 09:45:02,275 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 09:45:02,287 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.21 s. ]
2025-08-04 09:45:02,313 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:02,314 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 09:45:02,317 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 09:45:02,369 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 09:45:02,408 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 09:45:02,441 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 09:45:02,462 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 09:45:02,477 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 09:45:02,497 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 09:45:02,521 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 09:45:02,548 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 09:45:02,548 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.26 s. ]
2025-08-04 09:45:02,548 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:02,548 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 09:45:02,560 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 09:45:02,560 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:02,560 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 09:45:02,655 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 09:45:02,657 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 09:45:02,773 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-04 09:45:02,774 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:02,774 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 09:45:02,781 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 09:45:02,781 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:02,781 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 09:45:04,893 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.11 s. ]
2025-08-04 09:45:04,893 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:45:04,893 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.21 s. ]
2025-08-04 09:45:04,893 [main] INFO  com.polarion.platform.startup - ****************************************************************
