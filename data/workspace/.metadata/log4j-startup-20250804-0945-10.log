2025-08-04 09:45:10,488 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:45:10,488 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 09:45:10,488 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:45:10,488 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 09:45:10,488 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 09:45:10,489 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:10,489 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 09:45:14,470 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 09:45:14,594 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.123 s. ]
2025-08-04 09:45:14,594 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 09:45:14,636 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.042 s. ]
2025-08-04 09:45:14,678 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 09:45:14,764 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 09:45:14,985 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.5 s. ]
2025-08-04 09:45:15,063 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:15,063 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 09:45:15,079 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.09 s. ]
2025-08-04 09:45:15,079 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:15,079 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 09:45:15,085 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-04 09:45:15,085 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-04 09:45:15,085 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-04 09:45:15,086 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 09:45:15,086 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 09:45:15,086 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 09:45:15,093 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 09:45:15,199 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 09:45:15,305 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 09:45:15,743 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.66 s. ]
2025-08-04 09:45:15,754 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:15,754 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 09:45:15,950 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 09:45:15,960 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 09:45:15,984 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:15,984 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 09:45:15,989 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 09:45:16,048 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 09:45:16,094 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 09:45:16,123 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 09:45:16,139 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 09:45:16,159 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 09:45:16,187 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 09:45:16,220 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 09:45:16,245 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 09:45:16,245 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-08-04 09:45:16,245 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:16,245 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 09:45:16,258 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 09:45:16,259 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:16,259 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 09:45:16,356 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 09:45:16,358 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
