2025-08-04 09:45:19,773 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:45:19,773 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 09:45:19,773 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:45:19,773 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 09:45:19,773 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 09:45:19,773 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:19,774 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 09:45:23,642 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 09:45:23,746 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.104 s. ]
2025-08-04 09:45:23,746 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 09:45:23,789 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0426 s. ]
2025-08-04 09:45:23,830 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 09:45:23,931 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 09:45:24,160 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.39 s. ]
2025-08-04 09:45:24,233 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:24,233 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 09:45:24,253 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.09 s. ]
2025-08-04 09:45:24,253 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:24,253 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 09:45:24,258 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-04 09:45:24,258 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-04 09:45:24,258 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 09:45:24,258 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 09:45:24,258 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 09:45:24,258 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-08-04 09:45:24,264 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 09:45:24,379 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 09:45:24,472 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 09:45:24,921 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.67 s. ]
2025-08-04 09:45:24,931 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:24,931 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 09:45:25,120 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 09:45:25,131 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.21 s. ]
2025-08-04 09:45:25,155 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:25,155 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 09:45:25,158 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 09:45:25,200 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 09:45:25,243 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 09:45:25,274 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 09:45:25,292 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 09:45:25,309 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 09:45:25,330 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 09:45:25,358 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 09:45:25,381 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 09:45:25,381 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.25 s. ]
2025-08-04 09:45:25,381 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:25,382 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 09:45:25,394 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 09:45:25,394 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:25,394 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 09:45:25,517 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 09:45:25,522 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
