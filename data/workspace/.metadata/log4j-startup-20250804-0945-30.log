2025-08-04 09:45:30,958 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:45:30,958 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 09:45:30,958 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:45:30,958 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 09:45:30,958 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 09:45:30,958 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:30,959 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 09:45:34,925 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 09:45:35,032 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.106 s. ]
2025-08-04 09:45:35,032 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 09:45:35,072 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0393 s. ]
2025-08-04 09:45:35,121 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 09:45:35,213 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 09:45:35,434 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.48 s. ]
2025-08-04 09:45:35,508 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:35,508 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 09:45:35,529 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 09:45:35,529 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:35,529 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 09:45:35,534 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-04 09:45:35,534 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 09:45:35,534 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-04 09:45:35,534 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 09:45:35,534 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-04 09:45:35,534 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 09:45:35,539 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 09:45:35,643 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 09:45:35,748 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 09:45:36,217 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-08-04 09:45:36,228 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:36,228 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 09:45:36,440 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 09:45:36,452 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-04 09:45:36,479 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:36,479 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 09:45:36,482 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 09:45:36,534 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 09:45:36,589 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 09:45:36,629 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 09:45:36,653 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 09:45:36,675 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 09:45:36,717 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 09:45:36,762 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 09:45:36,812 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 09:45:36,812 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-08-04 09:45:36,812 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:36,812 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 09:45:36,827 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 09:45:36,827 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:36,828 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 09:45:36,926 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 09:45:36,929 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 09:45:37,064 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-08-04 09:45:37,064 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:37,064 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 09:45:37,072 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 09:45:37,072 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:45:37,072 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 09:45:39,332 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.26 s. ]
2025-08-04 09:45:39,337 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:45:39,337 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.37 s. ]
2025-08-04 09:45:39,337 [main] INFO  com.polarion.platform.startup - ****************************************************************
