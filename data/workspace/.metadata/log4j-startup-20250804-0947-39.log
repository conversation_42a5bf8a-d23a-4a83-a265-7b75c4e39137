2025-08-04 09:47:39,353 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:47:39,353 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 09:47:39,353 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:47:39,354 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 09:47:39,354 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 09:47:39,354 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:39,354 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 09:47:43,366 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 09:47:43,492 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.126 s. ]
2025-08-04 09:47:43,492 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 09:47:43,534 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0418 s. ]
2025-08-04 09:47:43,582 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 09:47:43,685 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 13 s. ]
2025-08-04 09:47:43,916 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.57 s. ]
2025-08-04 09:47:43,994 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:43,994 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 09:47:44,017 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 09:47:44,018 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:44,018 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 09:47:44,023 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 09:47:44,023 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 09:47:44,023 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-04 09:47:44,023 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 09:47:44,023 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 09:47:44,023 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 09:47:44,029 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 09:47:44,132 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 09:47:44,245 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 09:47:44,693 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.68 s. ]
2025-08-04 09:47:44,704 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:44,704 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 09:47:44,901 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 09:47:44,910 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 09:47:44,934 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:44,934 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 09:47:44,937 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 09:47:44,983 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 09:47:45,020 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 09:47:45,050 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 09:47:45,067 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 09:47:45,084 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 09:47:45,115 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 09:47:45,153 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 09:47:45,195 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 09:47:45,195 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-08-04 09:47:45,195 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:45,195 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 09:47:45,209 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 09:47:45,209 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:45,209 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 09:47:45,299 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 09:47:45,301 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 09:47:45,401 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.19 s. ]
2025-08-04 09:47:45,402 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:45,402 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 09:47:45,408 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 09:47:45,408 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:47:45,408 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 09:49:32,765 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 107.36 s. ]
2025-08-04 09:49:32,766 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:49:32,766 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 113 s. ]
2025-08-04 09:49:32,766 [main] INFO  com.polarion.platform.startup - ****************************************************************
