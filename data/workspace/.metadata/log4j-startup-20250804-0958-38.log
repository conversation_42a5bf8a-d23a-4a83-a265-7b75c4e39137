2025-08-04 09:58:38,559 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:58:38,559 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 09:58:38,559 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:58:38,559 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 09:58:38,559 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 09:58:38,559 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:38,559 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 09:58:42,725 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 09:58:42,879 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.154 s. ]
2025-08-04 09:58:42,879 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 09:58:42,953 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0739 s. ]
2025-08-04 09:58:43,027 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 09:58:43,142 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 20 s. ]
2025-08-04 09:58:43,373 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.82 s. ]
2025-08-04 09:58:43,454 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:43,454 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 09:58:43,475 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 09:58:43,476 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:43,476 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 09:58:43,480 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 09:58:43,480 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 09:58:43,480 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 09:58:43,480 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 09:58:43,480 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 09:58:43,480 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-04 09:58:43,485 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 09:58:43,603 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 09:58:43,731 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 09:58:44,185 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-08-04 09:58:44,197 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:44,197 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 09:58:44,391 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 09:58:44,403 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 09:58:44,426 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:44,426 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 09:58:44,429 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 09:58:44,470 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 09:58:44,510 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 09:58:44,537 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 09:58:44,554 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 09:58:44,571 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 09:58:44,594 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 09:58:44,617 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 09:58:44,639 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 09:58:44,639 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.24 s. ]
2025-08-04 09:58:44,639 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:44,639 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 09:58:44,653 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 09:58:44,653 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:44,653 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 09:58:44,744 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 09:58:44,747 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 09:58:44,858 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-04 09:58:44,859 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:44,859 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 09:58:44,865 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 09:58:44,865 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 09:58:44,865 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 09:58:47,047 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.18 s. ]
2025-08-04 09:58:47,047 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 09:58:47,047 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.49 s. ]
2025-08-04 09:58:47,047 [main] INFO  com.polarion.platform.startup - ****************************************************************
