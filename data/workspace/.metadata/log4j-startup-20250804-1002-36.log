2025-08-04 10:02:36,256 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:02:36,256 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 10:02:36,256 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:02:36,256 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 10:02:36,256 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 10:02:36,256 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:36,256 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 10:02:40,552 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 10:02:40,684 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.132 s. ]
2025-08-04 10:02:40,684 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 10:02:40,724 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0394 s. ]
2025-08-04 10:02:40,777 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 10:02:40,877 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 19 s. ]
2025-08-04 10:02:41,105 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.85 s. ]
2025-08-04 10:02:41,197 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:41,197 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 10:02:41,220 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 10:02:41,220 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:41,220 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 10:02:41,225 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 10:02:41,225 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 10:02:41,225 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-04 10:02:41,225 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-08-04 10:02:41,226 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 10:02:41,226 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-04 10:02:41,235 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 10:02:41,352 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 10:02:41,427 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 10:02:41,936 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-08-04 10:02:41,949 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:41,949 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 10:02:42,152 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 10:02:42,162 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-04 10:02:42,191 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:42,191 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 10:02:42,197 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 10:02:42,261 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 10:02:42,321 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 10:02:42,363 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 10:02:42,385 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 10:02:42,414 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 10:02:42,460 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 10:02:42,513 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 10:02:42,555 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 10:02:42,555 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.39 s. ]
2025-08-04 10:02:42,556 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:42,556 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 10:02:42,572 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 10:02:42,572 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:42,572 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 10:02:42,666 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 10:02:42,671 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 10:02:42,790 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-08-04 10:02:42,791 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:42,792 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 10:02:42,800 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 10:02:42,800 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:02:42,800 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 10:02:45,071 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.27 s. ]
2025-08-04 10:02:45,071 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:02:45,071 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.82 s. ]
2025-08-04 10:02:45,071 [main] INFO  com.polarion.platform.startup - ****************************************************************
