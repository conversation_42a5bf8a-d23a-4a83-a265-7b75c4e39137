2025-08-04 10:07:31,872 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:07:31,872 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 10:07:31,872 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:07:31,872 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 10:07:31,872 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 10:07:31,872 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:31,872 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 10:07:36,351 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 10:07:36,496 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.145 s. ]
2025-08-04 10:07:36,496 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 10:07:36,536 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0396 s. ]
2025-08-04 10:07:36,589 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 10:07:36,707 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 18 s. ]
2025-08-04 10:07:36,950 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.08 s. ]
2025-08-04 10:07:37,034 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:37,034 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 10:07:37,056 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 10:07:37,056 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:37,056 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 10:07:37,061 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-04 10:07:37,061 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 10:07:37,061 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 10:07:37,061 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 10:07:37,062 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 10:07:37,062 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 10:07:37,068 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 10:07:37,195 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 10:07:37,315 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 10:07:37,800 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-08-04 10:07:37,811 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:37,811 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 10:07:38,020 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 10:07:38,029 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-04 10:07:38,052 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:38,052 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 10:07:38,056 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 10:07:38,100 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 10:07:38,141 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 10:07:38,172 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 10:07:38,190 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 10:07:38,210 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 10:07:38,239 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 10:07:38,268 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 10:07:38,300 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 10:07:38,300 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.27 s. ]
2025-08-04 10:07:38,300 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:38,300 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 10:07:38,312 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 10:07:38,312 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:38,312 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 10:07:38,407 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 10:07:38,409 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 10:07:38,518 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-04 10:07:38,519 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:38,519 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 10:07:38,526 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 10:07:38,526 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:07:38,526 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 10:07:40,751 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.22 s. ]
2025-08-04 10:07:40,751 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:07:40,751 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.88 s. ]
2025-08-04 10:07:40,751 [main] INFO  com.polarion.platform.startup - ****************************************************************
