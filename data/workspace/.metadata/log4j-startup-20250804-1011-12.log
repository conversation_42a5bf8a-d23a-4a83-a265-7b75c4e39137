2025-08-04 10:11:12,253 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:11:12,253 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 10:11:12,253 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:11:12,253 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 10:11:12,253 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 10:11:12,253 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:12,253 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 10:11:16,428 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 10:11:16,564 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.136 s. ]
2025-08-04 10:11:16,564 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 10:11:16,616 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0513 s. ]
2025-08-04 10:11:16,665 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 10:11:16,763 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 18 s. ]
2025-08-04 10:11:16,993 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.75 s. ]
2025-08-04 10:11:17,077 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:17,077 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 10:11:17,101 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 10:11:17,101 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:17,101 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 10:11:17,106 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 10:11:17,106 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 10:11:17,106 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-04 10:11:17,106 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-04 10:11:17,106 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 10:11:17,106 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 10:11:17,114 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 10:11:17,237 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 10:11:17,333 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 10:11:17,785 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.68 s. ]
2025-08-04 10:11:17,797 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:17,797 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 10:11:17,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 10:11:17,979 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.19 s. ]
2025-08-04 10:11:18,002 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:18,002 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 10:11:18,006 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 10:11:18,054 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 10:11:18,096 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 10:11:18,129 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 10:11:18,146 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 10:11:18,161 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 10:11:18,180 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 10:11:18,202 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 10:11:18,227 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 10:11:18,227 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.25 s. ]
2025-08-04 10:11:18,227 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:18,227 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 10:11:18,242 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 10:11:18,242 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:18,242 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 10:11:18,332 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 10:11:18,334 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 10:11:18,442 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.2 s. ]
2025-08-04 10:11:18,442 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:18,442 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 10:11:18,448 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 10:11:18,449 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:11:18,449 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 10:11:20,588 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.14 s. ]
2025-08-04 10:11:20,589 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:11:20,589 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.34 s. ]
2025-08-04 10:11:20,589 [main] INFO  com.polarion.platform.startup - ****************************************************************
