2025-08-04 10:35:26,983 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:35:26,983 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 10:35:26,983 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:35:26,983 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 10:35:26,983 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 10:35:26,983 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:26,983 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 10:35:31,358 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 10:35:31,595 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.238 s. ]
2025-08-04 10:35:31,596 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 10:35:31,681 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.085 s. ]
2025-08-04 10:35:31,758 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 10:35:32,000 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 17 s. ]
2025-08-04 10:35:32,262 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.29 s. ]
2025-08-04 10:35:32,355 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:32,355 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 10:35:32,379 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 10:35:32,379 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:32,379 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 10:35:32,385 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-04 10:35:32,385 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 10:35:32,385 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 10:35:32,386 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 10:35:32,385 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 10:35:32,386 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-08-04 10:35:32,396 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 10:35:32,515 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 10:35:32,635 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 10:35:33,151 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-08-04 10:35:33,171 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:33,171 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 10:35:33,428 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 10:35:33,438 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.29 s. ]
2025-08-04 10:35:33,461 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:33,461 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 10:35:33,464 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 10:35:33,514 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 10:35:33,563 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 10:35:33,599 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 10:35:33,620 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 10:35:33,639 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 10:35:33,667 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 10:35:33,704 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 10:35:33,728 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 10:35:33,728 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-08-04 10:35:33,728 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:33,728 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 10:35:33,741 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 10:35:33,741 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:33,741 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 10:35:33,847 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 10:35:33,849 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 10:35:33,974 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-04 10:35:33,975 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:33,975 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 10:35:33,981 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 10:35:33,981 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 10:35:33,981 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 10:35:36,163 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.18 s. ]
2025-08-04 10:35:36,163 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 10:35:36,164 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.18 s. ]
2025-08-04 10:35:36,164 [main] INFO  com.polarion.platform.startup - ****************************************************************
