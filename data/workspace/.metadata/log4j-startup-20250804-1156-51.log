2025-08-04 11:56:52,068 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 11:56:52,068 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 11:56:52,068 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 11:56:52,068 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 11:56:52,068 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 11:56:52,068 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:52,069 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 11:56:57,484 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 11:56:57,737 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.252 s. ]
2025-08-04 11:56:57,737 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 11:56:57,797 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.06 s. ]
2025-08-04 11:56:57,848 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 11:56:57,971 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-04 11:56:58,210 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.15 s. ]
2025-08-04 11:56:58,308 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:58,308 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 11:56:58,364 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-08-04 11:56:58,365 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:58,365 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 11:56:58,371 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-04 11:56:58,371 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-04 11:56:58,371 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 11:56:58,371 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 11:56:58,371 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-04 11:56:58,371 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 11:56:58,380 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 11:56:58,537 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 11:56:58,630 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 11:56:59,133 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-08-04 11:56:59,146 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:59,146 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 11:56:59,369 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 11:56:59,383 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-08-04 11:56:59,409 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:59,409 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 11:56:59,412 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 11:56:59,446 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 11:56:59,481 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 11:56:59,504 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 11:56:59,520 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 11:56:59,536 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 11:56:59,561 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 11:56:59,588 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 11:56:59,615 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 11:56:59,616 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.23 s. ]
2025-08-04 11:56:59,616 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:59,616 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 11:56:59,630 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 11:56:59,630 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:59,630 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 11:56:59,739 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 11:56:59,743 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 11:56:59,856 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-04 11:56:59,857 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:59,857 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 11:56:59,866 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 11:56:59,866 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 11:56:59,866 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 11:57:02,624 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.76 s. ]
2025-08-04 11:57:02,624 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 11:57:02,624 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.6 s. ]
2025-08-04 11:57:02,624 [main] INFO  com.polarion.platform.startup - ****************************************************************
