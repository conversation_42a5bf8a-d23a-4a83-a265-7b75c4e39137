2025-08-04 12:06:30,839 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:06:30,839 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 12:06:30,839 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:06:30,839 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 12:06:30,839 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 12:06:30,839 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:30,839 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 12:06:35,227 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 12:06:35,515 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.288 s. ]
2025-08-04 12:06:35,516 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 12:06:35,566 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0504 s. ]
2025-08-04 12:06:35,614 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 12:06:35,735 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-04 12:06:35,983 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.15 s. ]
2025-08-04 12:06:36,082 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:36,082 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 12:06:36,111 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-08-04 12:06:36,111 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:36,111 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 12:06:36,119 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 12:06:36,119 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 12:06:36,119 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 12:06:36,119 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 12:06:36,119 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 12:06:36,120 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 12:06:36,129 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 12:06:36,344 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 12:06:36,456 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 12:06:37,142 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.03 s. ]
2025-08-04 12:06:37,156 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:37,156 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 12:06:37,482 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 12:06:37,515 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.37 s. ]
2025-08-04 12:06:37,599 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:37,599 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 12:06:37,611 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 12:06:37,670 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 12:06:37,753 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 12:06:37,809 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 12:06:37,837 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 12:06:37,863 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 12:06:37,905 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 12:06:37,957 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 12:06:38,006 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 12:06:38,006 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.49 s. ]
2025-08-04 12:06:38,006 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:38,006 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 12:06:38,023 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 12:06:38,023 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:38,023 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 12:06:38,139 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 12:06:38,142 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 12:06:38,287 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-08-04 12:06:38,287 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:38,287 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 12:06:38,295 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 12:06:38,295 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:06:38,295 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 12:06:40,929 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.63 s. ]
2025-08-04 12:06:40,930 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:06:40,930 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.1 s. ]
2025-08-04 12:06:40,930 [main] INFO  com.polarion.platform.startup - ****************************************************************
