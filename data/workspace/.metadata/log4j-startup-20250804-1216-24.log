2025-08-04 12:16:24,981 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:16:24,981 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 12:16:24,981 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:16:24,981 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 12:16:24,981 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 12:16:24,981 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:24,981 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 12:16:29,556 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 12:16:29,734 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.178 s. ]
2025-08-04 12:16:29,734 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 12:16:29,787 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.053 s. ]
2025-08-04 12:16:29,837 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 12:16:29,948 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 16 s. ]
2025-08-04 12:16:30,178 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.2 s. ]
2025-08-04 12:16:30,264 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:30,264 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 12:16:30,295 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 12:16:30,295 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:30,295 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 12:16:30,301 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 12:16:30,300 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 12:16:30,300 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-04 12:16:30,300 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 12:16:30,300 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 12:16:30,300 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 12:16:30,309 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 12:16:30,432 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 12:16:30,528 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 12:16:31,011 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-08-04 12:16:31,023 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:31,023 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 12:16:31,243 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 12:16:31,260 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-08-04 12:16:31,289 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:31,289 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 12:16:31,293 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 12:16:31,352 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 12:16:31,403 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 12:16:31,442 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 12:16:31,475 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 12:16:31,508 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 12:16:31,553 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 12:16:31,599 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 12:16:31,655 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 12:16:31,655 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.39 s. ]
2025-08-04 12:16:31,655 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:31,655 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 12:16:31,677 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 12:16:31,678 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:31,678 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 12:16:31,776 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 12:16:31,779 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 12:16:31,912 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-04 12:16:31,913 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:31,913 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 12:16:31,921 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 12:16:31,921 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:16:31,921 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 12:16:34,397 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.48 s. ]
2025-08-04 12:16:34,397 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:16:34,397 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.42 s. ]
2025-08-04 12:16:34,397 [main] INFO  com.polarion.platform.startup - ****************************************************************
