2025-08-04 12:26:22,636 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:26:22,637 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 12:26:22,637 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:26:22,637 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 12:26:22,637 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 12:26:22,637 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:22,637 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 12:26:26,938 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 12:26:27,091 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.152 s. ]
2025-08-04 12:26:27,091 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 12:26:27,130 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0395 s. ]
2025-08-04 12:26:27,183 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 12:26:27,305 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-04 12:26:27,558 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.93 s. ]
2025-08-04 12:26:27,649 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:27,649 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 12:26:27,678 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 12:26:27,678 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:27,678 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 12:26:27,683 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 12:26:27,684 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-04 12:26:27,684 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 12:26:27,684 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 12:26:27,684 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 12:26:27,684 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 12:26:27,692 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 12:26:27,827 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 12:26:27,929 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 12:26:28,431 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-08-04 12:26:28,443 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:28,443 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 12:26:28,692 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 12:26:28,708 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-08-04 12:26:28,733 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:28,733 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 12:26:28,737 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 12:26:28,792 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 12:26:28,847 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 12:26:28,896 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 12:26:28,921 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 12:26:28,952 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 12:26:29,004 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 12:26:29,047 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 12:26:29,076 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 12:26:29,076 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-08-04 12:26:29,077 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:29,077 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 12:26:29,091 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 12:26:29,092 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:29,092 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 12:26:29,199 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 12:26:29,202 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 12:26:29,333 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-08-04 12:26:29,334 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:29,334 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 12:26:29,346 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 12:26:29,346 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:26:29,346 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 12:26:32,443 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.1 s. ]
2025-08-04 12:26:32,445 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:26:32,445 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.81 s. ]
2025-08-04 12:26:32,445 [main] INFO  com.polarion.platform.startup - ****************************************************************
