2025-08-04 12:30:45,772 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:30:45,772 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 12:30:45,772 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:30:45,773 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 12:30:45,773 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 12:30:45,773 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:45,773 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 12:30:50,422 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 12:30:50,638 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.217 s. ]
2025-08-04 12:30:50,638 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 12:30:50,733 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0941 s. ]
2025-08-04 12:30:50,793 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 12:30:50,910 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-04 12:30:51,161 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.4 s. ]
2025-08-04 12:30:51,274 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:51,274 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 12:30:51,302 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-04 12:30:51,302 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:51,302 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 12:30:51,308 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 12:30:51,307 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 12:30:51,308 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 12:30:51,308 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 12:30:51,308 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 12:30:51,308 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 12:30:51,314 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 12:30:51,435 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 12:30:51,559 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 12:30:52,042 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-08-04 12:30:52,058 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:52,058 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 12:30:52,295 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 12:30:52,310 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-08-04 12:30:52,346 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:52,346 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 12:30:52,349 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 12:30:52,405 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 12:30:52,456 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 12:30:52,497 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 12:30:52,523 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 12:30:52,550 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 12:30:52,591 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 12:30:52,641 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 12:30:52,690 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 12:30:52,690 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.38 s. ]
2025-08-04 12:30:52,690 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:52,690 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 12:30:52,704 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 12:30:52,705 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:52,705 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 12:30:52,803 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 12:30:52,807 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 12:30:52,973 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-08-04 12:30:52,974 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:52,974 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 12:30:53,002 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.03 s. ]
2025-08-04 12:30:53,002 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:30:53,002 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 12:30:55,632 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.63 s. ]
2025-08-04 12:30:55,632 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:30:55,632 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.86 s. ]
2025-08-04 12:30:55,632 [main] INFO  com.polarion.platform.startup - ****************************************************************
