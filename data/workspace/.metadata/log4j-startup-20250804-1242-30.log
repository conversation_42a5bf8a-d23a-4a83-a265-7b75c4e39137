2025-08-04 12:42:30,810 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:42:30,810 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 12:42:30,810 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:42:30,810 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 12:42:30,810 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 12:42:30,810 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:30,810 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 12:42:35,139 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 12:42:35,271 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.132 s. ]
2025-08-04 12:42:35,271 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 12:42:35,321 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0501 s. ]
2025-08-04 12:42:35,371 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 12:42:35,472 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-04 12:42:35,700 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.9 s. ]
2025-08-04 12:42:35,781 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:35,781 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 12:42:35,803 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 12:42:35,804 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:35,804 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 12:42:35,808 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-04 12:42:35,808 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-04 12:42:35,808 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-04 12:42:35,809 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-04 12:42:35,808 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 12:42:35,809 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 12:42:35,814 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 12:42:35,919 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 12:42:36,038 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 12:42:36,618 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.81 s. ]
2025-08-04 12:42:36,636 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:36,636 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 12:42:36,848 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 12:42:36,859 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-04 12:42:36,885 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:36,885 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 12:42:36,888 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 12:42:36,932 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 12:42:36,971 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 12:42:37,003 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 12:42:37,023 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 12:42:37,039 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 12:42:37,059 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 12:42:37,083 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 12:42:37,110 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 12:42:37,110 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.25 s. ]
2025-08-04 12:42:37,110 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:37,110 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 12:42:37,125 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 12:42:37,125 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:37,125 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 12:42:37,210 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 12:42:37,212 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 12:42:37,305 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.18 s. ]
2025-08-04 12:42:37,306 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:37,306 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 12:42:37,312 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 12:42:37,312 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:42:37,312 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 12:42:39,470 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.16 s. ]
2025-08-04 12:42:39,470 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:42:39,470 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.66 s. ]
2025-08-04 12:42:39,470 [main] INFO  com.polarion.platform.startup - ****************************************************************
