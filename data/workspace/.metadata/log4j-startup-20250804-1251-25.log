2025-08-04 12:51:25,253 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:51:25,253 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 12:51:25,253 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:51:25,253 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 12:51:25,253 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 12:51:25,253 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:25,253 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 12:51:29,524 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 12:51:29,677 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.153 s. ]
2025-08-04 12:51:29,677 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 12:51:29,735 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0574 s. ]
2025-08-04 12:51:29,786 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 12:51:29,907 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 15 s. ]
2025-08-04 12:51:30,155 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.91 s. ]
2025-08-04 12:51:30,241 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:30,241 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 12:51:30,268 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 12:51:30,268 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:30,268 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 12:51:30,274 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 12:51:30,274 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 12:51:30,274 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 12:51:30,274 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 12:51:30,274 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 12:51:30,274 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 12:51:30,283 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 12:51:30,403 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 12:51:30,501 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 12:51:30,976 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-08-04 12:51:30,988 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:30,988 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 12:51:31,205 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 12:51:31,218 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-04 12:51:31,252 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:31,252 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 12:51:31,256 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 12:51:31,317 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 12:51:31,388 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 12:51:31,428 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 12:51:31,452 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 12:51:31,476 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 12:51:31,512 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 12:51:31,561 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 12:51:31,605 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 12:51:31,605 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.39 s. ]
2025-08-04 12:51:31,606 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:31,606 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 12:51:31,624 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 12:51:31,624 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:31,624 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 12:51:31,721 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 12:51:31,723 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 12:51:31,822 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.2 s. ]
2025-08-04 12:51:31,823 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:31,823 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 12:51:31,830 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 12:51:31,830 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:51:31,830 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 12:51:34,319 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.49 s. ]
2025-08-04 12:51:34,319 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:51:34,319 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.07 s. ]
2025-08-04 12:51:34,319 [main] INFO  com.polarion.platform.startup - ****************************************************************
