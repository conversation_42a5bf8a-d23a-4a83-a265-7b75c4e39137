2025-08-04 12:55:51,218 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:55:51,218 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 12:55:51,218 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:55:51,219 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 12:55:51,219 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 12:55:51,219 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:51,219 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 12:55:55,655 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 12:55:55,911 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.256 s. ]
2025-08-04 12:55:55,911 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 12:55:56,007 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0964 s. ]
2025-08-04 12:55:56,064 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 12:55:56,172 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 12:55:56,416 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.2 s. ]
2025-08-04 12:55:56,500 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:56,501 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 12:55:56,530 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 12:55:56,530 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:56,530 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 12:55:56,537 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 12:55:56,537 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 12:55:56,537 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-04 12:55:56,537 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 12:55:56,537 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 12:55:56,537 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-04 12:55:56,553 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 12:55:56,663 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 12:55:56,779 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 12:55:57,323 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.79 s. ]
2025-08-04 12:55:57,336 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:57,336 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 12:55:57,544 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 12:55:57,554 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-04 12:55:57,580 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:57,580 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 12:55:57,584 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 12:55:57,643 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 12:55:57,687 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 12:55:57,725 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 12:55:57,743 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 12:55:57,763 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 12:55:57,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 12:55:57,834 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 12:55:57,864 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 12:55:57,864 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-08-04 12:55:57,864 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:57,864 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 12:55:57,879 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 12:55:57,879 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:57,879 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 12:55:57,993 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 12:55:57,997 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 12:55:58,101 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-08-04 12:55:58,102 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:58,102 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 12:55:58,110 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 12:55:58,110 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 12:55:58,110 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 12:56:01,067 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.96 s. ]
2025-08-04 12:56:01,067 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 12:56:01,067 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.85 s. ]
2025-08-04 12:56:01,067 [main] INFO  com.polarion.platform.startup - ****************************************************************
