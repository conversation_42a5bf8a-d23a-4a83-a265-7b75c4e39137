2025-08-04 13:05:30,600 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:05:30,600 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 13:05:30,600 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:05:30,600 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 13:05:30,600 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 13:05:30,600 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:30,600 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 13:05:34,836 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 13:05:34,957 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.121 s. ]
2025-08-04 13:05:34,957 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 13:05:35,007 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0494 s. ]
2025-08-04 13:05:35,054 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 13:05:35,159 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 13:05:35,389 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.79 s. ]
2025-08-04 13:05:35,470 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:35,470 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 13:05:35,494 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 13:05:35,495 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:35,495 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 13:05:35,499 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-04 13:05:35,499 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 13:05:35,499 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-04 13:05:35,499 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-04 13:05:35,499 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 13:05:35,499 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 13:05:35,505 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 13:05:35,605 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 13:05:35,717 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 13:05:36,165 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.67 s. ]
2025-08-04 13:05:36,175 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:36,175 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 13:05:36,368 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 13:05:36,387 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 13:05:36,410 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:36,410 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 13:05:36,414 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 13:05:36,460 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 13:05:36,506 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 13:05:36,537 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 13:05:36,555 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 13:05:36,572 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 13:05:36,602 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 13:05:36,631 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 13:05:36,653 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 13:05:36,653 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.27 s. ]
2025-08-04 13:05:36,653 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:36,653 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 13:05:36,667 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 13:05:36,667 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:36,667 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 13:05:36,754 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 13:05:36,756 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 13:05:36,844 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.18 s. ]
2025-08-04 13:05:36,844 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:36,844 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 13:05:36,851 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 13:05:36,851 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:05:36,851 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 13:05:38,982 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.13 s. ]
2025-08-04 13:05:38,982 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:05:38,982 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.38 s. ]
2025-08-04 13:05:38,982 [main] INFO  com.polarion.platform.startup - ****************************************************************
