2025-08-04 13:35:17,755 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:35:17,755 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 13:35:17,755 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:35:17,755 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 13:35:17,755 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 13:35:17,756 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:17,756 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 13:35:22,115 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 13:35:22,259 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.143 s. ]
2025-08-04 13:35:22,259 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 13:35:22,305 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0462 s. ]
2025-08-04 13:35:22,374 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 13:35:22,523 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 13:35:22,781 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.03 s. ]
2025-08-04 13:35:22,871 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:22,871 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 13:35:22,901 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 13:35:22,901 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:22,901 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 13:35:22,906 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 13:35:22,907 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-04 13:35:22,906 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 13:35:22,906 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 13:35:22,907 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-04 13:35:22,907 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 13:35:22,915 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 13:35:23,054 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 13:35:23,185 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 13:35:23,677 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.78 s. ]
2025-08-04 13:35:23,690 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:23,690 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 13:35:23,909 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 13:35:23,924 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-08-04 13:35:23,951 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:23,951 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 13:35:23,955 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 13:35:24,010 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 13:35:24,068 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 13:35:24,109 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 13:35:24,136 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 13:35:24,159 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 13:35:24,194 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 13:35:24,239 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 13:35:24,280 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 13:35:24,280 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-08-04 13:35:24,280 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:24,280 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 13:35:24,299 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 13:35:24,299 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:24,299 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 13:35:24,422 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 13:35:24,425 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 13:35:24,570 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-08-04 13:35:24,571 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:24,571 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 13:35:24,577 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 13:35:24,578 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:35:24,578 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 13:35:27,394 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.82 s. ]
2025-08-04 13:35:27,395 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:35:27,395 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.64 s. ]
2025-08-04 13:35:27,395 [main] INFO  com.polarion.platform.startup - ****************************************************************
