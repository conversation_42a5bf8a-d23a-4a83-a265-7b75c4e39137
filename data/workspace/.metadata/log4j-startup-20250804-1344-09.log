2025-08-04 13:44:09,963 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:44:09,963 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 13:44:09,963 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:44:09,963 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 13:44:09,964 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 13:44:09,964 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:09,964 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 13:44:14,658 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 13:44:14,814 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.157 s. ]
2025-08-04 13:44:14,814 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 13:44:14,881 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0662 s. ]
2025-08-04 13:44:14,930 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 13:44:15,048 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 13:44:15,301 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.35 s. ]
2025-08-04 13:44:15,395 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:15,395 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 13:44:15,422 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 13:44:15,422 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:15,422 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 13:44:15,427 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 13:44:15,428 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 13:44:15,428 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 13:44:15,428 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 13:44:15,428 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-04 13:44:15,428 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 13:44:15,435 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 13:44:15,570 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 13:44:15,652 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 13:44:16,304 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.88 s. ]
2025-08-04 13:44:16,318 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:16,318 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 13:44:16,572 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 13:44:16,584 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-08-04 13:44:16,618 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:16,618 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 13:44:16,623 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 13:44:16,681 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 13:44:16,747 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 13:44:16,799 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 13:44:16,830 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 13:44:16,858 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 13:44:16,910 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 13:44:16,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 13:44:17,046 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 13:44:17,046 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.46 s. ]
2025-08-04 13:44:17,046 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:17,046 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 13:44:17,071 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.03 s. ]
2025-08-04 13:44:17,071 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:17,071 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 13:44:17,195 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 13:44:17,200 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 13:44:17,329 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-08-04 13:44:17,329 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:17,329 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 13:44:17,337 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 13:44:17,337 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 13:44:17,337 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 13:44:21,590 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.25 s. ]
2025-08-04 13:44:21,590 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 13:44:21,591 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.6 s. ]
2025-08-04 13:44:21,591 [main] INFO  com.polarion.platform.startup - ****************************************************************
