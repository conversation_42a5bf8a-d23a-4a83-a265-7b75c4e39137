2025-08-04 14:01:45,431 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:01:45,432 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 14:01:45,432 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:01:45,432 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 14:01:45,432 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 14:01:45,432 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:45,432 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 14:01:50,449 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 14:01:50,605 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.156 s. ]
2025-08-04 14:01:50,605 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 14:01:50,642 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.037 s. ]
2025-08-04 14:01:50,692 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 14:01:50,816 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-08-04 14:01:51,052 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.63 s. ]
2025-08-04 14:01:51,133 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:51,133 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 14:01:51,158 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 14:01:51,158 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:51,158 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 14:01:51,163 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 14:01:51,163 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-04 14:01:51,163 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-04 14:01:51,163 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-04 14:01:51,163 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 14:01:51,163 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 14:01:51,169 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 14:01:51,299 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 14:01:51,389 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 14:01:51,850 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-08-04 14:01:51,861 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:51,861 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 14:01:52,059 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 14:01:52,070 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 14:01:52,096 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:52,096 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 14:01:52,099 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 14:01:52,150 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 14:01:52,201 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 14:01:52,231 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 14:01:52,249 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 14:01:52,281 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 14:01:52,309 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 14:01:52,354 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 14:01:52,390 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 14:01:52,390 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-08-04 14:01:52,390 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:52,390 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 14:01:52,408 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 14:01:52,408 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:52,408 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 14:01:52,518 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 14:01:52,523 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 14:01:52,662 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-08-04 14:01:52,663 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:52,663 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 14:01:52,672 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 14:01:52,672 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:01:52,672 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 14:01:55,132 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.46 s. ]
2025-08-04 14:01:55,132 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:01:55,132 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.7 s. ]
2025-08-04 14:01:55,132 [main] INFO  com.polarion.platform.startup - ****************************************************************
