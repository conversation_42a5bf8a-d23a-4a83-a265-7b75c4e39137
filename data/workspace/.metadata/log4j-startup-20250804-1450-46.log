2025-08-04 14:50:46,424 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:50:46,424 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 14:50:46,424 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:50:46,425 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 14:50:46,425 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 14:50:46,425 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:46,425 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 14:50:51,010 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 14:50:51,162 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.152 s. ]
2025-08-04 14:50:51,162 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 14:50:51,215 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0527 s. ]
2025-08-04 14:50:51,279 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 14:50:51,427 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 13 s. ]
2025-08-04 14:50:51,656 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.24 s. ]
2025-08-04 14:50:51,737 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:51,737 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 14:50:51,768 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 14:50:51,769 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:51,769 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 14:50:51,774 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-04 14:50:51,775 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-08-04 14:50:51,775 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-04 14:50:51,774 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 14:50:51,775 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 14:50:51,775 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 14:50:51,787 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 14:50:51,906 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-04 14:50:52,027 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-04 14:50:52,496 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-08-04 14:50:52,508 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:52,508 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 14:50:52,744 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 14:50:52,755 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-08-04 14:50:52,783 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:52,783 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 14:50:52,788 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 14:50:52,857 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 14:50:52,900 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-08-04 14:50:52,941 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-08-04 14:50:52,961 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 14:50:52,975 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 14:50:52,994 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 14:50:53,025 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-04 14:50:53,055 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-04 14:50:53,055 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-08-04 14:50:53,055 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:53,055 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 14:50:53,068 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 14:50:53,083 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:53,083 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 14:50:53,198 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-04 14:50:53,221 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-04 14:50:53,382 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 14:50:53,383 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 14:50:53,454 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.39 s. ]
2025-08-04 14:50:53,455 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:53,455 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 14:50:53,466 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 14:50:53,466 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:50:53,466 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 14:50:55,822 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.36 s. ]
2025-08-04 14:50:55,822 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:50:55,822 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.4 s. ]
2025-08-04 14:50:55,822 [main] INFO  com.polarion.platform.startup - ****************************************************************
