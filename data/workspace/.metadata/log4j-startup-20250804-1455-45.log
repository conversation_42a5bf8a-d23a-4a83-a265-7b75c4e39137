2025-08-04 14:55:45,459 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:55:45,459 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 14:55:45,459 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:55:45,459 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 14:55:45,459 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 14:55:45,459 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:45,459 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 14:55:49,830 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 14:55:49,965 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.135 s. ]
2025-08-04 14:55:49,965 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 14:55:50,006 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0405 s. ]
2025-08-04 14:55:50,062 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 14:55:50,190 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 13 s. ]
2025-08-04 14:55:50,433 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.98 s. ]
2025-08-04 14:55:50,522 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:50,522 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 14:55:50,552 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 14:55:50,552 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:50,552 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 14:55:50,557 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 14:55:50,557 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-04 14:55:50,557 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-04 14:55:50,557 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 14:55:50,557 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 14:55:50,558 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-04 14:55:50,564 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 14:55:50,701 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-04 14:55:50,798 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-04 14:55:51,302 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-08-04 14:55:51,314 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:51,314 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 14:55:51,558 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 14:55:51,571 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-08-04 14:55:51,598 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:51,599 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 14:55:51,604 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 14:55:51,652 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 14:55:51,704 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-08-04 14:55:51,748 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-08-04 14:55:51,770 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 14:55:51,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 14:55:51,832 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 14:55:51,892 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-04 14:55:51,941 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-04 14:55:51,941 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-08-04 14:55:51,941 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:51,941 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 14:55:51,955 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 14:55:51,956 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:51,956 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 14:55:52,066 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 14:55:52,069 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 14:55:52,194 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-08-04 14:55:52,195 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:52,195 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 14:55:52,201 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 14:55:52,201 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 14:55:52,201 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 14:55:54,609 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.41 s. ]
2025-08-04 14:55:54,609 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 14:55:54,609 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.15 s. ]
2025-08-04 14:55:54,609 [main] INFO  com.polarion.platform.startup - ****************************************************************
