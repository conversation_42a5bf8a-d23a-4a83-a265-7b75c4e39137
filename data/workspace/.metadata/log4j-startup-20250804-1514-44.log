2025-08-04 15:14:44,179 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:14:44,179 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 15:14:44,179 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:14:44,180 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 15:14:44,180 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 15:14:44,180 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:44,180 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 15:14:48,438 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 15:14:48,599 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.162 s. ]
2025-08-04 15:14:48,599 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 15:14:48,675 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0752 s. ]
2025-08-04 15:14:48,725 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 15:14:48,867 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 13 s. ]
2025-08-04 15:14:49,131 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.96 s. ]
2025-08-04 15:14:49,259 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:49,259 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 15:14:49,291 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.16 s. ]
2025-08-04 15:14:49,292 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:49,292 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 15:14:49,299 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 15:14:49,299 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 15:14:49,299 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 15:14:49,300 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-04 15:14:49,300 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 15:14:49,300 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 15:14:49,313 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 15:14:49,481 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-04 15:14:49,632 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-04 15:14:50,254 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.96 s. ]
2025-08-04 15:14:50,274 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:50,274 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 15:14:50,514 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 15:14:50,531 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-08-04 15:14:50,584 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:50,584 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 15:14:50,588 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 15:14:50,640 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 15:14:50,688 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-08-04 15:14:50,738 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-08-04 15:14:50,759 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 15:14:50,783 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 15:14:50,817 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 15:14:50,865 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-04 15:14:50,905 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-04 15:14:50,905 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-08-04 15:14:50,905 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:50,906 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 15:14:50,921 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 15:14:50,921 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:50,921 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 15:14:51,021 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 15:14:51,023 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 15:14:51,138 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-08-04 15:14:51,139 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:51,139 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 15:14:51,148 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 15:14:51,148 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:14:51,148 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 15:14:53,632 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.48 s. ]
2025-08-04 15:14:53,632 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:14:53,632 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.45 s. ]
2025-08-04 15:14:53,632 [main] INFO  com.polarion.platform.startup - ****************************************************************
