2025-08-04 15:20:30,178 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:20:30,178 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 15:20:30,178 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:20:30,178 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 15:20:30,178 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 15:20:30,178 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:30,178 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 15:20:34,247 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 15:20:34,382 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.135 s. ]
2025-08-04 15:20:34,382 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 15:20:34,445 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0626 s. ]
2025-08-04 15:20:34,495 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 15:20:34,618 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 13 s. ]
2025-08-04 15:20:34,855 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.68 s. ]
2025-08-04 15:20:34,934 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:34,934 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 15:20:34,960 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 15:20:34,960 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:34,960 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 15:20:34,965 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-04 15:20:34,965 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 15:20:34,965 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-04 15:20:34,965 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-04 15:20:34,965 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 15:20:34,965 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-08-04 15:20:34,971 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-08-04 15:20:35,099 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 15:20:35,195 [LowLevelDataService-contextInitializer-1 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 15:20:35,654 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-08-04 15:20:35,667 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:35,667 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 15:20:35,880 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 15:20:35,891 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-04 15:20:35,915 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:35,915 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 15:20:35,918 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 15:20:35,995 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 15:20:36,030 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-08-04 15:20:36,067 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 15:20:36,104 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-08-04 15:20:36,118 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-08-04 15:20:36,134 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-08-04 15:20:36,163 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 15:20:36,189 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 15:20:36,189 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-08-04 15:20:36,189 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:36,189 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 15:20:36,202 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 15:20:36,216 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:36,216 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 15:20:36,312 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-04 15:20:36,331 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-04 15:20:36,431 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 15:20:36,432 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 15:20:36,492 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-08-04 15:20:36,493 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:36,493 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 15:20:36,499 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 15:20:36,499 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:20:36,499 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 15:20:38,741 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.24 s. ]
2025-08-04 15:20:38,741 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:20:38,741 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.56 s. ]
2025-08-04 15:20:38,741 [main] INFO  com.polarion.platform.startup - ****************************************************************
