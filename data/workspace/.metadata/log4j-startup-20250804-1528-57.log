2025-08-04 15:28:58,005 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:28:58,005 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 15:28:58,005 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:28:58,005 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 15:28:58,005 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 15:28:58,005 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:28:58,005 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 15:29:02,361 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 15:29:02,518 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.157 s. ]
2025-08-04 15:29:02,519 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 15:29:02,598 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.079 s. ]
2025-08-04 15:29:02,644 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 15:29:02,763 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 15:29:03,045 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.05 s. ]
2025-08-04 15:29:03,154 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:29:03,154 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 15:29:03,178 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-08-04 15:29:03,179 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:29:03,179 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 15:29:03,184 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-04 15:29:03,184 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-04 15:29:03,184 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-04 15:29:03,184 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 15:29:03,184 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-08-04 15:29:03,184 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-04 15:29:03,193 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-08-04 15:29:03,321 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 15:29:03,421 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 15:29:03,894 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-08-04 15:29:03,905 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:29:03,905 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 15:29:04,119 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 15:29:04,130 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-04 15:29:04,154 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:29:04,154 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 15:29:04,157 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 15:29:04,215 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 15:29:04,240 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-08-04 15:29:04,278 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-08-04 15:29:04,312 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-08-04 15:29:04,348 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-08-04 15:29:04,368 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-08-04 15:29:04,403 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 15:29:04,429 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 15:29:04,429 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-08-04 15:29:04,429 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:29:04,429 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 15:29:04,441 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 15:29:04,455 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:29:04,455 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 15:29:04,552 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-04 15:29:04,581 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-04 15:29:04,697 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 15:29:04,698 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 15:29:04,767 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.33 s. ]
2025-08-04 15:29:04,768 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:29:04,768 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 15:29:04,775 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 15:29:04,775 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:29:04,775 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 15:29:07,090 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.31 s. ]
2025-08-04 15:29:07,090 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:29:07,090 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.08 s. ]
2025-08-04 15:29:07,090 [main] INFO  com.polarion.platform.startup - ****************************************************************
