2025-08-04 15:59:31,258 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:59:31,258 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 15:59:31,259 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:59:31,259 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 15:59:31,259 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 15:59:31,259 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:31,259 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 15:59:35,411 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 15:59:35,545 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.133 s. ]
2025-08-04 15:59:35,545 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 15:59:35,580 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.035 s. ]
2025-08-04 15:59:35,631 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 15:59:35,763 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 15:59:35,994 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.74 s. ]
2025-08-04 15:59:36,071 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:36,071 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 15:59:36,095 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 15:59:36,095 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:36,095 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 15:59:36,101 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 15:59:36,101 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 15:59:36,101 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 15:59:36,101 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-08-04 15:59:36,101 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 15:59:36,101 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 15:59:36,108 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-08-04 15:59:36,235 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 15:59:36,350 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 15:59:36,920 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.83 s. ]
2025-08-04 15:59:36,956 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:36,956 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 15:59:37,186 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 15:59:37,197 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-08-04 15:59:37,225 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:37,225 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 15:59:37,228 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 15:59:37,281 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 15:59:37,309 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-08-04 15:59:37,356 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-08-04 15:59:37,398 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-08-04 15:59:37,449 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-08-04 15:59:37,467 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-08-04 15:59:37,508 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 15:59:37,551 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 15:59:37,551 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.35 s. ]
2025-08-04 15:59:37,552 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:37,552 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 15:59:37,567 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 15:59:37,567 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:37,567 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 15:59:37,681 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 15:59:37,690 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 15:59:37,843 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.28 s. ]
2025-08-04 15:59:37,843 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:37,843 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 15:59:37,850 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 15:59:37,850 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 15:59:37,850 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 15:59:40,195 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.34 s. ]
2025-08-04 15:59:40,195 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 15:59:40,195 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.94 s. ]
2025-08-04 15:59:40,195 [main] INFO  com.polarion.platform.startup - ****************************************************************
