2025-08-04 16:02:01,807 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:02:01,807 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 16:02:01,807 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:02:01,807 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 16:02:01,807 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 16:02:01,807 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:01,807 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 16:02:06,108 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 16:02:06,255 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.147 s. ]
2025-08-04 16:02:06,255 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 16:02:06,315 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0599 s. ]
2025-08-04 16:02:06,367 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 16:02:06,505 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 16:02:06,745 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.94 s. ]
2025-08-04 16:02:06,832 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:06,832 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 16:02:06,857 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 16:02:06,858 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:06,858 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 16:02:06,863 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 16:02:06,863 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 16:02:06,863 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 16:02:06,863 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 16:02:06,863 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-08-04 16:02:06,863 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 16:02:06,870 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-08-04 16:02:07,000 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 16:02:07,104 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 16:02:07,564 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-08-04 16:02:07,577 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:07,577 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 16:02:07,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 16:02:07,806 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-04 16:02:07,831 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:07,831 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 16:02:07,834 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 16:02:07,905 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 16:02:07,927 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-08-04 16:02:07,963 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-08-04 16:02:07,990 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-08-04 16:02:08,019 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-08-04 16:02:08,034 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-08-04 16:02:08,057 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 16:02:08,079 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 16:02:08,079 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.27 s. ]
2025-08-04 16:02:08,079 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:08,079 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 16:02:08,094 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 16:02:08,094 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:08,094 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 16:02:08,206 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 16:02:08,209 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 16:02:08,330 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-08-04 16:02:08,331 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:08,331 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 16:02:08,338 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 16:02:08,338 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:02:08,338 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 16:02:10,740 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.4 s. ]
2025-08-04 16:02:10,740 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:02:10,741 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.93 s. ]
2025-08-04 16:02:10,741 [main] INFO  com.polarion.platform.startup - ****************************************************************
