2025-08-04 16:22:29,892 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:22:29,898 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 16:22:29,900 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:22:29,901 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 16:22:29,901 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 16:22:29,903 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:29,907 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 16:22:34,309 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 16:22:34,490 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.181 s. ]
2025-08-04 16:22:34,490 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 16:22:34,546 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.056 s. ]
2025-08-04 16:22:34,624 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 16:22:34,797 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 16:22:35,058 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.18 s. ]
2025-08-04 16:22:35,137 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:35,137 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 16:22:35,162 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 16:22:35,162 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:35,162 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 16:22:35,167 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-08-04 16:22:35,168 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 16:22:35,167 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-08-04 16:22:35,167 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 16:22:35,167 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 16:22:35,167 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-04 16:22:35,173 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-08-04 16:22:35,318 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 16:22:35,439 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 16:22:35,976 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.81 s. ]
2025-08-04 16:22:35,991 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:35,991 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 16:22:36,219 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 16:22:36,230 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-08-04 16:22:36,258 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:36,258 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 16:22:36,261 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 16:22:36,318 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 16:22:36,343 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-08-04 16:22:36,382 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-08-04 16:22:36,424 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-08-04 16:22:36,464 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-08-04 16:22:36,487 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-08-04 16:22:36,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 16:22:36,565 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 16:22:36,565 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-08-04 16:22:36,565 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:36,565 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 16:22:36,581 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 16:22:36,581 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:36,581 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 16:22:36,692 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 16:22:36,697 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 16:22:36,905 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.32 s. ]
2025-08-04 16:22:36,905 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:36,905 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 16:22:36,912 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 16:22:36,912 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:22:36,912 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 16:22:39,525 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.61 s. ]
2025-08-04 16:22:39,525 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:22:39,525 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.64 s. ]
2025-08-04 16:22:39,525 [main] INFO  com.polarion.platform.startup - ****************************************************************
