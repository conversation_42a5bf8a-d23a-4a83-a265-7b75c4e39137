2025-08-04 16:33:50,999 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:33:50,999 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 16:33:50,999 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:33:50,999 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 16:33:51,000 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 16:33:51,000 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:51,000 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 16:33:55,210 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 16:33:55,352 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-08-04 16:33:55,352 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 16:33:55,391 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0393 s. ]
2025-08-04 16:33:55,444 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 16:33:55,561 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 16:33:55,817 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.82 s. ]
2025-08-04 16:33:55,926 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:55,926 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 16:33:55,954 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-04 16:33:55,954 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:55,954 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 16:33:55,960 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-04 16:33:55,960 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 16:33:55,960 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-08-04 16:33:55,960 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 16:33:55,960 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-04 16:33:55,960 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 16:33:55,969 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-08-04 16:33:56,104 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 16:33:56,201 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 16:33:56,717 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-08-04 16:33:56,731 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:56,731 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 16:33:56,953 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 16:33:56,970 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-08-04 16:33:57,001 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:57,001 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 16:33:57,006 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 16:33:57,048 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 16:33:57,073 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-08-04 16:33:57,116 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-08-04 16:33:57,152 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-08-04 16:33:57,190 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-08-04 16:33:57,209 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-08-04 16:33:57,288 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 16:33:57,337 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 16:33:57,337 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-08-04 16:33:57,337 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:57,337 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 16:33:57,352 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 16:33:57,352 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:57,352 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 16:33:57,466 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 16:33:57,469 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 16:33:57,603 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-08-04 16:33:57,604 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:57,604 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 16:33:57,610 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 16:33:57,610 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:33:57,610 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 16:34:01,393 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.78 s. ]
2025-08-04 16:34:01,393 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:34:01,393 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.4 s. ]
2025-08-04 16:34:01,393 [main] INFO  com.polarion.platform.startup - ****************************************************************
