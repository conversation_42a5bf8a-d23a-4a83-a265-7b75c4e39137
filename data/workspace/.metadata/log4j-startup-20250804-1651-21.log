2025-08-04 16:51:21,893 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:51:21,893 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 16:51:21,894 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:51:21,894 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 16:51:21,894 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 16:51:21,894 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:21,894 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 16:51:26,103 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 16:51:26,235 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.131 s. ]
2025-08-04 16:51:26,235 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 16:51:26,301 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0658 s. ]
2025-08-04 16:51:26,353 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 16:51:26,489 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 16:51:26,717 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.83 s. ]
2025-08-04 16:51:26,798 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:26,798 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 16:51:26,827 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 16:51:26,827 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:26,827 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 16:51:26,832 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 16:51:26,832 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 16:51:26,832 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 16:51:26,832 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (3/9)
2025-08-04 16:51:26,832 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 16:51:26,832 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-04 16:51:26,837 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-08-04 16:51:26,964 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 16:51:27,090 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 16:51:27,564 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-08-04 16:51:27,576 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:27,576 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 16:51:27,772 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 16:51:27,783 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 16:51:27,807 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:27,807 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 16:51:27,810 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 16:51:27,858 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 16:51:27,886 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-08-04 16:51:27,922 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-08-04 16:51:27,960 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-08-04 16:51:28,002 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-08-04 16:51:28,021 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-08-04 16:51:28,062 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 16:51:28,090 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 16:51:28,090 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-08-04 16:51:28,090 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:28,090 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 16:51:28,103 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 16:51:28,103 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:28,103 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 16:51:28,207 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 16:51:28,210 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 16:51:28,325 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-08-04 16:51:28,325 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:28,325 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 16:51:28,331 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 16:51:28,331 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 16:51:28,331 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 16:51:30,492 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.16 s. ]
2025-08-04 16:51:30,492 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 16:51:30,492 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.6 s. ]
2025-08-04 16:51:30,492 [main] INFO  com.polarion.platform.startup - ****************************************************************
