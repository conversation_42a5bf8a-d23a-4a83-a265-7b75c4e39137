2025-08-04 17:12:31,206 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 17:12:31,206 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 17:12:31,206 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 17:12:31,206 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 17:12:31,206 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 17:12:31,206 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:31,206 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 17:12:35,564 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 17:12:35,731 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.166 s. ]
2025-08-04 17:12:35,731 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 17:12:35,790 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0589 s. ]
2025-08-04 17:12:35,847 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 17:12:35,983 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 17:12:36,233 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.03 s. ]
2025-08-04 17:12:36,343 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:36,343 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 17:12:36,376 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-04 17:12:36,376 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:36,376 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 17:12:36,383 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 17:12:36,383 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 17:12:36,383 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-04 17:12:36,383 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-08-04 17:12:36,383 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 17:12:36,383 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 17:12:36,396 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-08-04 17:12:36,541 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 17:12:36,683 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 17:12:37,200 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.82 s. ]
2025-08-04 17:12:37,214 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:37,214 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 17:12:37,445 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 17:12:37,454 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-08-04 17:12:37,481 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:37,481 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 17:12:37,484 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 17:12:37,531 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 17:12:37,560 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-08-04 17:12:37,604 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-08-04 17:12:37,644 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-08-04 17:12:37,686 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-08-04 17:12:37,704 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-08-04 17:12:37,758 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 17:12:37,805 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 17:12:37,805 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.35 s. ]
2025-08-04 17:12:37,805 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:37,805 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 17:12:37,823 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 17:12:37,824 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:37,824 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 17:12:37,934 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 17:12:37,936 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 17:12:38,076 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-08-04 17:12:38,076 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:38,076 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 17:12:38,084 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 17:12:38,084 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:12:38,084 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 17:12:40,409 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.32 s. ]
2025-08-04 17:12:40,409 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 17:12:40,409 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.2 s. ]
2025-08-04 17:12:40,409 [main] INFO  com.polarion.platform.startup - ****************************************************************
