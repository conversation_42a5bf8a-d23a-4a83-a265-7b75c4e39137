2025-08-04 17:17:21,640 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 17:17:21,640 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 17:17:21,641 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 17:17:21,641 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 17:17:21,641 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 17:17:21,641 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:21,641 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 17:17:25,883 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 17:17:26,012 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.129 s. ]
2025-08-04 17:17:26,012 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 17:17:26,054 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0421 s. ]
2025-08-04 17:17:26,104 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 17:17:26,208 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 17:17:26,445 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.81 s. ]
2025-08-04 17:17:26,532 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:26,532 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 17:17:26,557 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 17:17:26,557 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:26,557 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 17:17:26,565 [LowLevelDataService-contextInitializer-6 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 17:17:26,565 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 17:17:26,565 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 17:17:26,565 [LowLevelDataService-contextInitializer-3 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 17:17:26,565 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 17:17:26,565 [LowLevelDataService-contextInitializer-4 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-08-04 17:17:26,576 [LowLevelDataService-contextInitializer-1 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (7/9)
2025-08-04 17:17:26,721 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 17:17:26,827 [LowLevelDataService-contextInitializer-3 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 17:17:27,310 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-08-04 17:17:27,321 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:27,321 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 17:17:27,550 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 17:17:27,561 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-08-04 17:17:27,586 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:27,586 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 17:17:27,590 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 17:17:27,629 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 17:17:27,658 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (3/9)
2025-08-04 17:17:27,701 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (4/9)
2025-08-04 17:17:27,736 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-08-04 17:17:27,774 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (6/9)
2025-08-04 17:17:27,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (7/9)
2025-08-04 17:17:27,837 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 17:17:27,880 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 17:17:27,880 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-08-04 17:17:27,880 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:27,880 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 17:17:27,897 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 17:17:27,897 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:27,897 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 17:17:28,001 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 17:17:28,004 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 17:17:28,093 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.2 s. ]
2025-08-04 17:17:28,094 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:28,094 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 17:17:28,100 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 17:17:28,100 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 17:17:28,100 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 17:17:30,451 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.35 s. ]
2025-08-04 17:17:30,452 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 17:17:30,452 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.81 s. ]
2025-08-04 17:17:30,452 [main] INFO  com.polarion.platform.startup - ****************************************************************
