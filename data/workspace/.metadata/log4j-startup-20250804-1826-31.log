2025-08-04 18:26:31,359 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:26:31,359 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 18:26:31,359 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:26:31,359 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 18:26:31,359 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 18:26:31,359 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:31,359 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 18:26:34,280 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 18:26:34,532 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.252 s. ]
2025-08-04 18:26:34,533 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 18:26:34,663 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.13 s. ]
2025-08-04 18:26:34,805 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 18:26:35,000 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 18:26:35,256 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 3.9 s. ]
2025-08-04 18:26:35,371 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:35,371 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 18:26:35,420 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.16 s. ]
2025-08-04 18:26:35,420 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:35,420 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 18:26:35,431 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 18:26:35,430 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 18:26:35,431 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 18:26:35,430 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-04 18:26:35,430 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-04 18:26:35,431 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 18:26:35,448 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 18:26:35,644 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 18:26:35,742 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 18:26:36,311 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.89 s. ]
2025-08-04 18:26:36,327 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:36,327 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 18:26:36,593 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 18:26:36,612 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.3 s. ]
2025-08-04 18:26:36,657 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:36,657 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 18:26:36,673 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 18:26:36,756 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 18:26:36,808 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 18:26:36,835 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 18:26:36,859 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 18:26:36,887 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 18:26:36,914 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 18:26:36,944 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 18:26:36,979 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 18:26:36,979 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-08-04 18:26:36,979 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:36,979 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 18:26:36,994 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 18:26:37,013 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:37,013 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 18:26:37,132 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-04 18:26:37,166 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-04 18:26:37,343 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 18:26:37,344 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 18:26:37,411 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.42 s. ]
2025-08-04 18:26:37,412 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:37,412 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 18:26:37,419 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 18:26:37,419 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:26:37,419 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 18:26:40,409 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.99 s. ]
2025-08-04 18:26:40,410 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:26:40,410 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.05 s. ]
2025-08-04 18:26:40,410 [main] INFO  com.polarion.platform.startup - ****************************************************************
