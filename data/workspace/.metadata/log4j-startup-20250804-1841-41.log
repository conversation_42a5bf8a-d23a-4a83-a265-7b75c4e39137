2025-08-04 18:41:41,849 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:41:41,849 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 18:41:41,849 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:41:41,849 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 18:41:41,850 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 18:41:41,850 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:41,850 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 18:41:45,862 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 18:41:46,068 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.206 s. ]
2025-08-04 18:41:46,068 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 18:41:46,160 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0909 s. ]
2025-08-04 18:41:46,236 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 18:41:46,469 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 18:41:46,790 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.95 s. ]
2025-08-04 18:41:46,932 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:46,932 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 18:41:46,970 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.18 s. ]
2025-08-04 18:41:46,971 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:46,971 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 18:41:46,980 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 18:41:46,980 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 18:41:46,980 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-04 18:41:46,980 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 18:41:46,980 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-04 18:41:46,980 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-04 18:41:46,993 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 18:41:47,263 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 18:41:47,355 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 18:41:47,943 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.97 s. ]
2025-08-04 18:41:47,959 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:47,959 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 18:41:48,264 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 18:41:48,284 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.34 s. ]
2025-08-04 18:41:48,330 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:48,330 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 18:41:48,337 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 18:41:48,420 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 18:41:48,494 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 18:41:48,529 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 18:41:48,563 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 18:41:48,629 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 18:41:48,684 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 18:41:48,746 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 18:41:48,799 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 18:41:48,799 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.51 s. ]
2025-08-04 18:41:48,799 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:48,799 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 18:41:48,818 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 18:41:48,818 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:48,818 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 18:41:48,974 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 18:41:48,979 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 18:41:49,132 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.31 s. ]
2025-08-04 18:41:49,133 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:49,133 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 18:41:49,144 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 18:41:49,144 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:41:49,144 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 18:41:54,291 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 5.15 s. ]
2025-08-04 18:41:54,293 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:41:54,293 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.4 s. ]
2025-08-04 18:41:54,293 [main] INFO  com.polarion.platform.startup - ****************************************************************
