2025-08-04 18:44:58,195 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:44:58,195 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 18:44:58,195 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:44:58,196 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 18:44:58,196 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 18:44:58,196 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:44:58,196 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 18:45:02,317 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 18:45:02,451 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.134 s. ]
2025-08-04 18:45:02,451 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 18:45:02,486 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0347 s. ]
2025-08-04 18:45:02,536 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 18:45:02,639 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 18:45:02,864 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.67 s. ]
2025-08-04 18:45:02,939 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:45:02,939 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 18:45:02,965 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 18:45:02,965 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:45:02,965 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 18:45:02,976 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-04 18:45:02,976 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-08-04 18:45:02,977 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 18:45:02,977 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 18:45:02,977 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 18:45:02,977 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 18:45:02,982 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 18:45:03,098 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 18:45:03,195 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 18:45:03,656 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-08-04 18:45:03,666 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:45:03,667 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 18:45:03,864 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 18:45:03,875 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 18:45:03,899 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:45:03,900 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 18:45:03,903 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 18:45:03,949 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 18:45:03,995 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 18:45:04,017 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 18:45:04,038 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 18:45:04,067 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 18:45:04,088 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 18:45:04,111 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 18:45:04,139 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 18:45:04,139 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.26 s. ]
2025-08-04 18:45:04,139 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:45:04,139 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 18:45:04,153 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 18:45:04,153 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:45:04,153 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 18:45:04,248 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 18:45:04,253 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 18:45:04,375 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-08-04 18:45:04,375 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:45:04,375 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 18:45:04,381 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 18:45:04,381 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:45:04,381 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 18:45:06,571 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.19 s. ]
2025-08-04 18:45:06,572 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:45:06,573 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.38 s. ]
2025-08-04 18:45:06,573 [main] INFO  com.polarion.platform.startup - ****************************************************************
