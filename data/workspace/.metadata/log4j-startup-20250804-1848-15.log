2025-08-04 18:48:15,839 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:48:15,839 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 18:48:15,839 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:48:15,839 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 18:48:15,839 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 18:48:15,839 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:15,839 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 18:48:20,057 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 18:48:20,232 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.175 s. ]
2025-08-04 18:48:20,232 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 18:48:20,305 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0719 s. ]
2025-08-04 18:48:20,367 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 18:48:20,485 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 18:48:20,714 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.88 s. ]
2025-08-04 18:48:20,791 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:20,791 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 18:48:20,813 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 18:48:20,813 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:20,813 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 18:48:20,818 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 18:48:20,818 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 18:48:20,818 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 18:48:20,818 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 18:48:20,818 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 18:48:20,818 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 18:48:20,825 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 18:48:20,948 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 18:48:21,048 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 18:48:21,482 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.67 s. ]
2025-08-04 18:48:21,495 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:21,495 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 18:48:21,704 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 18:48:21,716 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-04 18:48:21,739 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:21,739 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 18:48:21,742 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 18:48:21,787 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 18:48:21,835 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 18:48:21,857 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 18:48:21,876 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 18:48:21,898 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 18:48:21,922 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 18:48:21,951 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 18:48:21,978 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 18:48:21,979 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.26 s. ]
2025-08-04 18:48:21,979 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:21,979 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 18:48:21,996 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 18:48:21,996 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:21,996 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 18:48:22,086 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 18:48:22,089 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 18:48:22,202 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-04 18:48:22,203 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:22,203 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 18:48:22,209 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 18:48:22,209 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 18:48:22,209 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 18:48:24,322 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.11 s. ]
2025-08-04 18:48:24,322 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 18:48:24,322 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.48 s. ]
2025-08-04 18:48:24,322 [main] INFO  com.polarion.platform.startup - ****************************************************************
