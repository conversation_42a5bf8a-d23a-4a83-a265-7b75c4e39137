2025-08-04 19:02:26,772 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:02:26,773 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:02:26,773 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:02:26,773 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:02:26,773 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:02:26,773 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:26,773 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:02:29,418 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:02:29,591 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.173 s. ]
2025-08-04 19:02:29,591 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:02:29,641 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0506 s. ]
2025-08-04 19:02:29,705 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:02:29,854 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:02:30,103 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 3.34 s. ]
2025-08-04 19:02:30,188 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:30,188 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:02:30,215 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 19:02:30,216 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:30,216 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:02:30,221 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-04 19:02:30,222 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 19:02:30,221 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 19:02:30,222 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 19:02:30,221 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 19:02:30,222 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 19:02:30,228 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:02:30,380 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:02:30,500 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:02:31,013 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.8 s. ]
2025-08-04 19:02:31,024 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:31,024 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:02:31,275 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:02:31,291 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-08-04 19:02:31,327 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:31,327 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:02:31,332 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:02:31,390 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:02:31,455 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:02:31,490 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:02:31,517 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:02:31,560 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:02:31,606 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:02:31,659 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:02:31,701 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:02:31,701 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.41 s. ]
2025-08-04 19:02:31,701 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:31,701 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:02:31,718 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 19:02:31,718 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:31,718 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:02:31,827 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:02:31,832 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:02:31,941 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-08-04 19:02:31,942 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:31,942 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:02:31,949 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 19:02:31,949 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:02:31,949 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:02:35,617 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.67 s. ]
2025-08-04 19:02:35,624 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:02:35,624 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.84 s. ]
2025-08-04 19:02:35,624 [main] INFO  com.polarion.platform.startup - ****************************************************************
