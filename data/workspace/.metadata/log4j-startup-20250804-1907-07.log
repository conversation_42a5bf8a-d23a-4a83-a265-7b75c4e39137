2025-08-04 19:07:07,928 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:07:07,928 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:07:07,928 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:07:07,928 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:07:07,928 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:07:07,928 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:07,928 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:07:10,921 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:07:11,152 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.231 s. ]
2025-08-04 19:07:11,152 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:07:11,216 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0645 s. ]
2025-08-04 19:07:11,273 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:07:11,455 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:07:11,705 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 3.78 s. ]
2025-08-04 19:07:11,811 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:11,811 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:07:11,849 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-04 19:07:11,850 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:11,850 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:07:11,863 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-04 19:07:11,863 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 19:07:11,863 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 19:07:11,863 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 19:07:11,863 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-04 19:07:11,863 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-04 19:07:11,878 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:07:12,097 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:07:12,193 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:07:12,697 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.85 s. ]
2025-08-04 19:07:12,710 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:12,710 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:07:12,952 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:07:12,970 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-08-04 19:07:13,040 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:13,040 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:07:13,044 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:07:13,106 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:07:13,158 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:07:13,194 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:07:13,238 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:07:13,320 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:07:13,402 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:07:13,523 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:07:13,606 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:07:13,607 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.64 s. ]
2025-08-04 19:07:13,607 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:13,607 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:07:13,635 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.03 s. ]
2025-08-04 19:07:13,635 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:13,635 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:07:13,801 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:07:13,807 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:07:13,951 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.32 s. ]
2025-08-04 19:07:13,952 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:13,952 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:07:13,972 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-08-04 19:07:13,972 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:07:13,972 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:07:17,327 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.35 s. ]
2025-08-04 19:07:17,327 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:07:17,327 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.4 s. ]
2025-08-04 19:07:17,327 [main] INFO  com.polarion.platform.startup - ****************************************************************
