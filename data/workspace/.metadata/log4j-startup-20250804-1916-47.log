2025-08-04 19:16:47,908 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:16:47,908 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:16:47,908 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:16:47,908 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:16:47,908 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:16:47,908 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:47,908 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:16:51,351 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:16:51,499 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.148 s. ]
2025-08-04 19:16:51,499 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:16:51,537 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0379 s. ]
2025-08-04 19:16:51,585 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:16:51,689 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:16:51,916 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.01 s. ]
2025-08-04 19:16:51,992 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:51,992 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:16:52,021 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 19:16:52,021 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:52,021 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:16:52,025 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 19:16:52,025 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-04 19:16:52,025 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 19:16:52,026 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-04 19:16:52,026 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 19:16:52,026 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 19:16:52,032 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:16:52,146 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:16:52,264 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:16:52,713 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-08-04 19:16:52,725 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:52,725 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:16:52,912 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:16:52,924 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.21 s. ]
2025-08-04 19:16:52,945 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:52,945 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:16:52,948 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:16:52,990 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:16:53,024 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:16:53,038 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:16:53,054 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:16:53,075 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:16:53,094 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:16:53,118 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:16:53,146 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:16:53,146 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.22 s. ]
2025-08-04 19:16:53,146 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:53,146 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:16:53,160 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 19:16:53,160 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:53,160 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:16:53,304 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:16:53,310 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:16:53,484 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.32 s. ]
2025-08-04 19:16:53,484 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:53,485 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:16:53,491 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 19:16:53,491 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:16:53,491 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:16:55,781 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.29 s. ]
2025-08-04 19:16:55,781 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:16:55,781 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 7.87 s. ]
2025-08-04 19:16:55,781 [main] INFO  com.polarion.platform.startup - ****************************************************************
