2025-08-04 19:18:52,422 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:18:52,422 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:18:52,422 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:18:52,422 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:18:52,422 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:18:52,422 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:18:52,422 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:18:58,179 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:18:58,320 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.14 s. ]
2025-08-04 19:18:58,320 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:18:58,362 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.042 s. ]
2025-08-04 19:18:58,428 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:18:58,542 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:18:58,807 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.39 s. ]
2025-08-04 19:18:58,907 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:18:58,907 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:18:58,931 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 19:18:58,932 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:18:58,932 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:18:58,936 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-04 19:18:58,937 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 19:18:58,936 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 19:18:58,936 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-04 19:18:58,936 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-04 19:18:58,936 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 19:18:58,950 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:18:59,114 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:18:59,202 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:18:59,682 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-08-04 19:18:59,694 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:18:59,694 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:18:59,887 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:18:59,900 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 19:18:59,926 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:18:59,926 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:18:59,930 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:18:59,974 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:19:00,033 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:19:00,055 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:19:00,074 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:19:00,101 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:19:00,121 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:19:00,145 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:19:00,167 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:19:00,168 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.27 s. ]
2025-08-04 19:19:00,168 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:19:00,168 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:19:00,181 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 19:19:00,182 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:19:00,182 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:19:00,296 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:19:00,299 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:19:00,409 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-04 19:19:00,409 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:19:00,409 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:19:00,416 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 19:19:00,416 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:19:00,416 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
