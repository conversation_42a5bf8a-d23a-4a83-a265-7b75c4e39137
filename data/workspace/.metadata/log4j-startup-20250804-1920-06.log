2025-08-04 19:20:06,233 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:20:06,233 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:20:06,233 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:20:06,233 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:20:06,233 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:20:06,233 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:06,233 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:20:11,584 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:20:11,726 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-08-04 19:20:11,726 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:20:11,796 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.07 s. ]
2025-08-04 19:20:11,850 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:20:11,971 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:20:12,232 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.01 s. ]
2025-08-04 19:20:12,327 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:12,327 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:20:12,350 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 19:20:12,350 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:12,350 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:20:12,355 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 19:20:12,355 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 19:20:12,355 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-04 19:20:12,355 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-04 19:20:12,355 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 19:20:12,355 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 19:20:12,361 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:20:12,503 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:20:12,601 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:20:13,101 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-08-04 19:20:13,112 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:13,112 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:20:13,302 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:20:13,315 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.21 s. ]
2025-08-04 19:20:13,342 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:13,342 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:20:13,345 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:20:13,406 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:20:13,466 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:20:13,484 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:20:13,508 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:20:13,544 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:20:13,581 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:20:13,633 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:20:13,672 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:20:13,672 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-08-04 19:20:13,673 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:13,673 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:20:13,688 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 19:20:13,688 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:13,688 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:20:13,800 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:20:13,802 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:20:13,920 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-04 19:20:13,921 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:13,921 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:20:13,930 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 19:20:13,930 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:20:13,930 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:20:16,891 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.96 s. ]
2025-08-04 19:20:16,892 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:20:16,892 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.7 s. ]
2025-08-04 19:20:16,892 [main] INFO  com.polarion.platform.startup - ****************************************************************
