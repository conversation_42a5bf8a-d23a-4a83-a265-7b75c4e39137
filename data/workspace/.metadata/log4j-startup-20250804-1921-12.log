2025-08-04 19:21:12,691 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:21:12,691 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:21:12,691 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:21:12,691 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:21:12,691 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:21:12,691 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:12,691 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:21:19,061 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:21:19,207 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.146 s. ]
2025-08-04 19:21:19,207 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:21:19,280 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0729 s. ]
2025-08-04 19:21:19,335 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:21:19,467 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:21:19,715 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 7.03 s. ]
2025-08-04 19:21:19,817 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:19,817 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:21:19,843 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-08-04 19:21:19,843 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:19,843 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:21:19,848 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 19:21:19,848 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 19:21:19,848 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 19:21:19,848 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-08-04 19:21:19,848 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 19:21:19,848 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 19:21:19,855 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:21:19,986 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:21:20,115 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:21:20,593 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-08-04 19:21:20,605 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:20,605 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:21:20,808 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:21:20,820 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-04 19:21:20,848 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:20,848 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:21:20,851 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:21:20,902 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:21:20,951 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:21:20,978 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:21:21,001 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:21:21,062 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:21:21,171 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:21:21,240 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:21:21,327 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:21:21,327 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.51 s. ]
2025-08-04 19:21:21,327 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:21,327 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:21:21,348 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 19:21:21,349 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:21,349 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:21:21,463 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:21:21,467 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:21:21,642 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-08-04 19:21:21,643 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:21,643 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:21:21,655 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 19:21:21,655 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:21:21,655 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:21:23,928 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.27 s. ]
2025-08-04 19:21:23,928 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:21:23,928 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.2 s. ]
2025-08-04 19:21:23,928 [main] INFO  com.polarion.platform.startup - ****************************************************************
