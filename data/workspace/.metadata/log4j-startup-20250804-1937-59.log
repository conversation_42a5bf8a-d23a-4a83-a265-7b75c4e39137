2025-08-04 19:37:59,556 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:37:59,557 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:37:59,557 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:37:59,557 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:37:59,557 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:37:59,557 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:37:59,557 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:38:04,918 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:38:05,063 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.144 s. ]
2025-08-04 19:38:05,063 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:38:05,111 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0482 s. ]
2025-08-04 19:38:05,158 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:38:05,337 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:38:05,570 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.02 s. ]
2025-08-04 19:38:05,651 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:05,651 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:38:05,677 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 19:38:05,677 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:05,678 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:38:05,682 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 19:38:05,682 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 19:38:05,682 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 19:38:05,682 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 19:38:05,682 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 19:38:05,682 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-04 19:38:05,689 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:38:05,819 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:38:05,908 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:38:06,374 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-08-04 19:38:06,385 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:06,385 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:38:06,593 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:38:06,602 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-04 19:38:06,625 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:06,626 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:38:06,628 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:38:06,668 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:38:06,714 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:38:06,739 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:38:06,759 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:38:06,810 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:38:06,851 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:38:06,902 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:38:06,963 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:38:06,963 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-08-04 19:38:06,963 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:06,963 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:38:06,977 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 19:38:06,977 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:06,977 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:38:07,070 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:38:07,074 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:38:07,199 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-08-04 19:38:07,202 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:07,202 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:38:07,223 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-08-04 19:38:07,223 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:07,223 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:38:09,449 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.23 s. ]
2025-08-04 19:38:09,449 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:38:09,449 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.89 s. ]
2025-08-04 19:38:09,449 [main] INFO  com.polarion.platform.startup - ****************************************************************
