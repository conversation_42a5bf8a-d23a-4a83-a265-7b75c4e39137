2025-08-04 19:38:30,590 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:38:30,590 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:38:30,590 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:38:30,590 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:38:30,590 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:38:30,590 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:30,590 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:38:36,409 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:38:36,564 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.155 s. ]
2025-08-04 19:38:36,564 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:38:36,608 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0439 s. ]
2025-08-04 19:38:36,660 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:38:36,757 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:38:37,001 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.41 s. ]
2025-08-04 19:38:37,077 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:37,077 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:38:37,098 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 19:38:37,099 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:37,099 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:38:37,103 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-04 19:38:37,103 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 19:38:37,103 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 19:38:37,103 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 19:38:37,103 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 19:38:37,103 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-08-04 19:38:37,108 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:38:37,219 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:38:37,308 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:38:37,835 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-08-04 19:38:37,845 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:37,845 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:38:38,027 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:38:38,037 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.2 s. ]
2025-08-04 19:38:38,059 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:38,059 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:38:38,062 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:38:38,105 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:38:38,147 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:38:38,171 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:38:38,189 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:38:38,236 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:38:38,281 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:38:38,303 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:38:38,338 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:38:38,338 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-08-04 19:38:38,338 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:38,338 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:38:38,356 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 19:38:38,356 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:38,356 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:38:38,456 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:38:38,458 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:38:38,558 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.2 s. ]
2025-08-04 19:38:38,559 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:38,559 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:38:38,565 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 19:38:38,565 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:38:38,565 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:38:40,897 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.33 s. ]
2025-08-04 19:38:40,898 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:38:40,898 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.3 s. ]
2025-08-04 19:38:40,898 [main] INFO  com.polarion.platform.startup - ****************************************************************
