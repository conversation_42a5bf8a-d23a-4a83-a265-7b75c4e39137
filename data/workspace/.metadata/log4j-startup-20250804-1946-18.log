2025-08-04 19:46:18,331 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:46:18,331 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:46:18,331 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:46:18,331 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:46:18,332 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:46:18,332 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:18,332 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:46:25,344 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:46:25,486 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.141 s. ]
2025-08-04 19:46:25,486 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:46:25,534 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0478 s. ]
2025-08-04 19:46:25,596 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:46:25,751 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 9 s. ]
2025-08-04 19:46:25,991 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 7.67 s. ]
2025-08-04 19:46:26,078 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:26,078 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:46:26,104 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 19:46:26,104 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:26,104 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:46:26,109 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 19:46:26,109 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-08-04 19:46:26,109 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 19:46:26,109 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-04 19:46:26,109 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-04 19:46:26,109 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 19:46:26,117 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:46:26,243 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:46:26,335 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:46:26,814 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-08-04 19:46:26,825 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:26,825 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:46:27,028 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:46:27,038 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 19:46:27,062 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:27,062 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:46:27,065 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:46:27,122 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:46:27,188 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:46:27,213 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:46:27,238 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:46:27,277 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:46:27,314 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:46:27,366 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:46:27,410 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:46:27,410 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-08-04 19:46:27,410 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:27,410 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:46:27,423 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 19:46:27,440 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:27,440 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:46:27,550 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-04 19:46:27,586 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-04 19:46:27,692 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:46:27,692 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:46:27,779 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.36 s. ]
2025-08-04 19:46:27,779 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:27,780 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:46:27,788 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 19:46:27,788 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:27,788 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:46:30,110 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.32 s. ]
2025-08-04 19:46:30,110 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:46:30,110 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.8 s. ]
2025-08-04 19:46:30,110 [main] INFO  com.polarion.platform.startup - ****************************************************************
