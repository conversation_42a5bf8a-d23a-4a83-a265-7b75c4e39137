2025-08-04 19:46:53,838 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:46:53,838 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 19:46:53,838 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:46:53,838 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 19:46:53,838 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 19:46:53,838 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:46:53,839 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 19:47:02,719 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 19:47:02,860 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.141 s. ]
2025-08-04 19:47:02,861 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 19:47:02,898 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0378 s. ]
2025-08-04 19:47:02,951 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 19:47:03,046 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-08-04 19:47:03,278 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 9.44 s. ]
2025-08-04 19:47:03,356 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:47:03,356 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 19:47:03,378 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-08-04 19:47:03,378 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:47:03,378 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 19:47:03,382 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 19:47:03,383 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 19:47:03,383 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-04 19:47:03,383 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-04 19:47:03,383 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 19:47:03,383 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-04 19:47:03,388 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 19:47:03,511 [LowLevelDataService-contextInitializer-4 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 19:47:03,627 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 19:47:04,138 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-08-04 19:47:04,157 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:47:04,157 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 19:47:04,385 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 19:47:04,396 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-08-04 19:47:04,427 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:47:04,428 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 19:47:04,431 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 19:47:04,494 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 19:47:04,541 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 19:47:04,570 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 19:47:04,609 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 19:47:04,666 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 19:47:04,706 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 19:47:04,752 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 19:47:04,801 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 19:47:04,801 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.41 s. ]
2025-08-04 19:47:04,801 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:47:04,801 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 19:47:04,816 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 19:47:04,816 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:47:04,816 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 19:47:04,920 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 19:47:04,922 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 19:47:05,041 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-04 19:47:05,042 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:47:05,042 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 19:47:05,048 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 19:47:05,048 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 19:47:05,048 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 19:47:07,731 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.68 s. ]
2025-08-04 19:47:07,732 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 19:47:07,732 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 13.9 s. ]
2025-08-04 19:47:07,732 [main] INFO  com.polarion.platform.startup - ****************************************************************
