2025-08-04 20:23:06,178 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 20:23:06,178 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 20:23:06,178 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 20:23:06,178 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 20:23:06,178 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 20:23:06,178 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:06,178 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 20:23:12,786 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 20:23:12,959 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.173 s. ]
2025-08-04 20:23:12,959 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 20:23:13,011 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0514 s. ]
2025-08-04 20:23:13,062 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 20:23:13,187 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-08-04 20:23:13,440 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 7.27 s. ]
2025-08-04 20:23:13,526 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:13,526 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 20:23:13,562 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 20:23:13,562 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:13,562 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 20:23:13,568 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 20:23:13,568 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 20:23:13,568 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-08-04 20:23:13,568 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-04 20:23:13,568 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-04 20:23:13,568 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-08-04 20:23:13,576 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 20:23:13,796 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 20:23:13,899 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 20:23:14,353 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.79 s. ]
2025-08-04 20:23:14,365 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:14,365 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 20:23:14,576 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 20:23:14,584 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-08-04 20:23:14,607 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:14,607 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 20:23:14,610 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 20:23:14,659 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 20:23:14,689 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 20:23:14,711 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-04 20:23:14,723 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 20:23:14,737 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 20:23:14,758 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 20:23:14,783 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 20:23:14,807 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 20:23:14,807 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.22 s. ]
2025-08-04 20:23:14,807 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:14,807 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 20:23:14,820 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 20:23:14,834 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:14,834 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 20:23:14,928 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-04 20:23:14,944 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-04 20:23:15,053 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 20:23:15,054 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 20:23:15,120 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.3 s. ]
2025-08-04 20:23:15,120 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:15,120 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 20:23:15,126 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 20:23:15,126 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:23:15,126 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 20:23:18,222 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.1 s. ]
2025-08-04 20:23:18,223 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 20:23:18,223 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12 s. ]
2025-08-04 20:23:18,223 [main] INFO  com.polarion.platform.startup - ****************************************************************
