2025-08-04 20:45:16,182 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 20:45:16,182 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 20:45:16,182 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 20:45:16,182 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 20:45:16,182 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 20:45:16,182 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:45:16,182 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 20:46:41,173 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 20:46:41,316 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-08-04 20:46:41,316 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 20:46:41,393 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0771 s. ]
2025-08-04 20:46:41,450 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 20:46:41,563 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-08-04 20:46:41,805 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 85.63 s. ]
2025-08-04 20:46:41,896 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:46:41,896 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 20:46:41,926 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 20:46:41,926 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:46:41,926 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 20:46:41,932 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-04 20:46:41,932 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 20:46:41,932 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 20:46:41,932 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-08-04 20:46:41,932 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 20:46:41,932 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-04 20:46:41,939 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 20:46:42,162 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-04 20:46:42,234 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-04 20:46:42,700 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-08-04 20:46:42,719 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:46:42,719 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 20:46:43,062 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 20:46:43,071 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.37 s. ]
2025-08-04 20:46:43,096 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:46:43,096 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 20:46:43,099 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 20:46:43,150 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 20:46:43,178 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-08-04 20:46:43,207 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-08-04 20:46:43,222 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-04 20:46:43,236 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-04 20:46:43,257 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 20:46:43,285 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-04 20:46:43,318 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-04 20:46:43,318 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.25 s. ]
2025-08-04 20:46:43,318 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:46:43,318 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 20:46:43,332 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 20:46:43,345 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:46:43,345 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 20:46:43,434 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-04 20:46:43,461 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-04 20:46:43,555 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 20:46:43,556 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 20:46:43,628 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.3 s. ]
2025-08-04 20:46:43,628 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:46:43,628 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 20:46:43,637 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 20:46:43,637 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 20:46:43,637 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 20:46:45,891 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.25 s. ]
2025-08-04 20:46:45,891 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 20:46:45,891 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 89.7 s. ]
2025-08-04 20:46:45,891 [main] INFO  com.polarion.platform.startup - ****************************************************************
