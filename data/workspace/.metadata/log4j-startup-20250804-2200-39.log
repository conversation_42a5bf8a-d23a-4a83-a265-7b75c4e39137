2025-08-04 22:00:39,583 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:00:39,583 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 22:00:39,583 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:00:39,583 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 22:00:39,583 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 22:00:39,583 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:00:39,583 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 22:00:57,759 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 22:00:57,928 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.168 s. ]
2025-08-04 22:00:57,928 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 22:00:57,998 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0697 s. ]
2025-08-04 22:00:58,075 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 22:00:58,184 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 17 s. ]
2025-08-04 22:00:58,440 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 18.86 s. ]
2025-08-04 22:00:58,559 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:00:58,559 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 22:00:58,607 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.17 s. ]
2025-08-04 22:00:58,607 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:00:58,607 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 22:00:58,612 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-04 22:00:58,612 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 22:00:58,612 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-08-04 22:00:58,612 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-08-04 22:00:58,612 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 22:00:58,613 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-08-04 22:00:58,620 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 22:00:58,770 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-04 22:00:58,903 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-04 22:00:59,648 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.04 s. ]
2025-08-04 22:00:59,668 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:00:59,668 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 22:00:59,899 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 22:00:59,910 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-08-04 22:00:59,945 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:00:59,945 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 22:00:59,950 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 22:01:00,058 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 22:01:00,106 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 22:01:00,128 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 22:01:00,147 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 22:01:00,172 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 22:01:00,198 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 22:01:00,227 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-04 22:01:00,257 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-04 22:01:00,257 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.35 s. ]
2025-08-04 22:01:00,257 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:01:00,257 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 22:01:00,272 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 22:01:00,294 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:01:00,294 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 22:01:00,451 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-04 22:01:00,482 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-04 22:01:00,657 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 22:01:00,658 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 22:01:00,760 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.49 s. ]
2025-08-04 22:01:00,761 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:01:00,761 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 22:01:00,770 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 22:01:00,770 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:01:00,770 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 22:01:04,067 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.3 s. ]
2025-08-04 22:01:04,068 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:01:04,068 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 24.5 s. ]
2025-08-04 22:01:04,068 [main] INFO  com.polarion.platform.startup - ****************************************************************
