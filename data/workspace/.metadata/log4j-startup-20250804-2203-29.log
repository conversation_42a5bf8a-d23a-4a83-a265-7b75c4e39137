2025-08-04 22:03:29,960 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:03:29,960 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 22:03:29,960 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:03:29,961 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 22:03:29,961 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 22:03:29,961 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:29,961 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 22:03:36,133 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 22:03:36,306 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.172 s. ]
2025-08-04 22:03:36,306 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 22:03:36,394 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0878 s. ]
2025-08-04 22:03:36,448 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 22:03:36,565 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 18 s. ]
2025-08-04 22:03:36,800 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.84 s. ]
2025-08-04 22:03:36,878 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:36,878 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 22:03:36,905 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 22:03:36,906 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:36,906 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 22:03:36,910 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 22:03:36,910 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-04 22:03:36,910 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-04 22:03:36,910 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-04 22:03:36,910 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-04 22:03:36,911 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 22:03:36,918 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 22:03:37,068 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-04 22:03:37,192 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-04 22:03:37,665 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-08-04 22:03:37,677 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:37,677 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 22:03:37,866 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 22:03:37,875 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.21 s. ]
2025-08-04 22:03:37,903 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:37,903 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 22:03:37,906 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 22:03:37,959 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 22:03:37,998 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 22:03:38,013 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 22:03:38,027 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 22:03:38,047 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 22:03:38,072 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 22:03:38,100 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-04 22:03:38,123 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-04 22:03:38,123 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.25 s. ]
2025-08-04 22:03:38,123 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:38,123 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 22:03:38,135 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 22:03:38,136 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:38,136 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 22:03:38,233 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 22:03:38,235 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 22:03:38,345 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-04 22:03:38,346 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:38,346 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 22:03:38,352 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 22:03:38,352 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:03:38,352 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 22:03:40,617 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.26 s. ]
2025-08-04 22:03:40,618 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:03:40,618 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.7 s. ]
2025-08-04 22:03:40,618 [main] INFO  com.polarion.platform.startup - ****************************************************************
