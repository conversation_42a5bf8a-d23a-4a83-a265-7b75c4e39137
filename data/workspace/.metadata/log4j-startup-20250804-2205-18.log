2025-08-04 22:05:18,833 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:05:18,833 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 22:05:18,833 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:05:18,833 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 22:05:18,834 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 22:05:18,834 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:18,834 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 22:05:26,640 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 22:05:26,785 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.145 s. ]
2025-08-04 22:05:26,785 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 22:05:26,849 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0634 s. ]
2025-08-04 22:05:26,893 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 22:05:27,001 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 17 s. ]
2025-08-04 22:05:27,237 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 8.41 s. ]
2025-08-04 22:05:27,315 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:27,315 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 22:05:27,343 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 22:05:27,343 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:27,344 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 22:05:27,350 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-08-04 22:05:27,350 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-04 22:05:27,350 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-08-04 22:05:27,350 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-08-04 22:05:27,350 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 22:05:27,351 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-08-04 22:05:27,358 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 22:05:27,583 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-04 22:05:27,667 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-04 22:05:28,155 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.81 s. ]
2025-08-04 22:05:28,165 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:28,165 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 22:05:28,363 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 22:05:28,372 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 22:05:28,398 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:28,398 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 22:05:28,401 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 22:05:28,449 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 22:05:28,502 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 22:05:28,525 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 22:05:28,541 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 22:05:28,564 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 22:05:28,601 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 22:05:28,637 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-04 22:05:28,667 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-04 22:05:28,667 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-08-04 22:05:28,668 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:28,668 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 22:05:28,681 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 22:05:28,681 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:28,681 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 22:05:28,836 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 22:05:28,842 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 22:05:29,082 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.4 s. ]
2025-08-04 22:05:29,083 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:29,083 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 22:05:29,089 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 22:05:29,089 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:05:29,089 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 22:05:31,531 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.44 s. ]
2025-08-04 22:05:31,531 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:05:31,531 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.7 s. ]
2025-08-04 22:05:31,531 [main] INFO  com.polarion.platform.startup - ****************************************************************
