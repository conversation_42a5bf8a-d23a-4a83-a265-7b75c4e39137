2025-08-04 22:18:06,400 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:18:06,400 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 22:18:06,400 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:18:06,400 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 22:18:06,400 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 22:18:06,400 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:06,401 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 22:18:55,860 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 22:18:56,003 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-08-04 22:18:56,003 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 22:18:56,055 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0521 s. ]
2025-08-04 22:18:56,125 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 22:18:56,254 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 17 s. ]
2025-08-04 22:18:56,520 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 50.12 s. ]
2025-08-04 22:18:56,615 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:56,615 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 22:18:56,639 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-04 22:18:56,639 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:56,639 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 22:18:56,646 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-04 22:18:56,646 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-08-04 22:18:56,646 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-08-04 22:18:56,646 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-08-04 22:18:56,646 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-08-04 22:18:56,646 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-04 22:18:56,658 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 22:18:56,817 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-04 22:18:56,927 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-04 22:18:57,379 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-08-04 22:18:57,398 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:57,398 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 22:18:57,607 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 22:18:57,617 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-04 22:18:57,641 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:57,641 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 22:18:57,644 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 22:18:57,681 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 22:18:57,726 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 22:18:57,746 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 22:18:57,766 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 22:18:57,796 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 22:18:57,824 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 22:18:57,856 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-04 22:18:57,883 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-04 22:18:57,884 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.27 s. ]
2025-08-04 22:18:57,884 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:57,884 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 22:18:57,899 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-04 22:18:57,899 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:57,899 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 22:18:58,006 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 22:18:58,012 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 22:18:58,240 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.34 s. ]
2025-08-04 22:18:58,241 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:58,241 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 22:18:58,248 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 22:18:58,248 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:18:58,248 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 22:19:00,672 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.42 s. ]
2025-08-04 22:19:00,672 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:19:00,672 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 54.3 s. ]
2025-08-04 22:19:00,672 [main] INFO  com.polarion.platform.startup - ****************************************************************
