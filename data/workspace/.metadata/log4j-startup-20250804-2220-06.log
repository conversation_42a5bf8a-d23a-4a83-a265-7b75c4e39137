2025-08-04 22:20:06,690 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:20:06,690 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-04 22:20:06,690 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:20:06,690 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-04 22:20:06,690 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-04 22:20:06,690 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:06,691 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-04 22:20:16,296 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-04 22:20:16,456 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.159 s. ]
2025-08-04 22:20:16,456 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-04 22:20:16,516 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0601 s. ]
2025-08-04 22:20:16,563 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-04 22:20:16,688 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 19 s. ]
2025-08-04 22:20:16,948 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 10.27 s. ]
2025-08-04 22:20:17,031 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:17,031 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-04 22:20:17,058 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-08-04 22:20:17,058 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:17,058 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-04 22:20:17,063 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-04 22:20:17,063 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-04 22:20:17,063 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-08-04 22:20:17,063 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-08-04 22:20:17,064 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-08-04 22:20:17,064 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-08-04 22:20:17,071 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-04 22:20:17,180 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-04 22:20:17,303 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-04 22:20:17,790 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-08-04 22:20:17,807 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:17,807 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-04 22:20:17,998 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-04 22:20:18,006 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-08-04 22:20:18,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:18,033 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-04 22:20:18,037 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-04 22:20:18,082 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-04 22:20:18,131 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-08-04 22:20:18,157 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-08-04 22:20:18,177 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-08-04 22:20:18,212 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-08-04 22:20:18,245 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-04 22:20:18,282 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-04 22:20:18,308 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-04 22:20:18,308 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-08-04 22:20:18,308 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:18,308 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-04 22:20:18,322 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-08-04 22:20:18,322 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:18,322 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-04 22:20:18,425 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-04 22:20:18,427 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-04 22:20:18,550 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-08-04 22:20:18,550 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:18,550 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-04 22:20:18,556 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-04 22:20:18,556 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-04 22:20:18,556 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-04 22:20:21,245 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.69 s. ]
2025-08-04 22:20:21,245 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-04 22:20:21,245 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 14.6 s. ]
2025-08-04 22:20:21,245 [main] INFO  com.polarion.platform.startup - ****************************************************************
