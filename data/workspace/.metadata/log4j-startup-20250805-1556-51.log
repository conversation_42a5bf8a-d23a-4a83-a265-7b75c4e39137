2025-08-05 15:56:51,617 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 15:56:51,617 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-05 15:56:51,617 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 15:56:51,617 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-05 15:56:51,617 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-05 15:56:51,617 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:56:51,618 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-05 15:57:17,826 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-05 15:57:18,065 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.239 s. ]
2025-08-05 15:57:18,065 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-05 15:57:18,136 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0707 s. ]
2025-08-05 15:57:18,208 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-05 15:57:18,393 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 19 s. ]
2025-08-05 15:57:18,681 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 27.07 s. ]
2025-08-05 15:57:18,783 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:57:18,783 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-05 15:57:18,823 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-08-05 15:57:18,823 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:57:18,823 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-05 15:57:18,828 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-05 15:57:18,828 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-05 15:57:18,828 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-08-05 15:57:18,828 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-08-05 15:57:18,828 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-05 15:57:18,829 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-08-05 15:57:18,838 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-08-05 15:57:19,004 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-05 15:57:19,103 [LowLevelDataService-contextInitializer-1 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-05 15:57:19,734 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.91 s. ]
2025-08-05 15:57:19,758 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:57:19,758 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-05 15:57:20,093 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-05 15:57:20,105 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.37 s. ]
2025-08-05 15:57:20,139 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:57:20,139 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-05 15:57:20,143 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-05 15:57:20,240 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-05 15:57:20,289 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-08-05 15:57:20,327 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-05 15:57:20,372 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-08-05 15:57:20,405 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-08-05 15:57:20,431 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-08-05 15:57:20,476 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-05 15:57:20,521 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-05 15:57:20,522 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.42 s. ]
2025-08-05 15:57:20,522 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:57:20,522 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-05 15:57:20,544 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-05 15:57:20,562 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:57:20,562 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-05 15:57:20,699 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-05 15:57:20,733 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-05 15:57:20,880 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-05 15:57:20,881 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-05 15:57:20,974 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.43 s. ]
2025-08-05 15:57:20,976 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:57:20,976 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-05 15:57:20,987 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-05 15:57:20,987 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 15:57:20,987 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-05 15:57:24,694 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.71 s. ]
2025-08-05 15:57:24,695 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 15:57:24,695 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 33.1 s. ]
2025-08-05 15:57:24,695 [main] INFO  com.polarion.platform.startup - ****************************************************************
