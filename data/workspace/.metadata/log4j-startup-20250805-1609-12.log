2025-08-05 16:09:12,443 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 16:09:12,443 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-05 16:09:12,443 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 16:09:12,443 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-05 16:09:12,443 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-05 16:09:12,443 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:12,444 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-05 16:09:55,770 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-05 16:09:56,089 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.318 s. ]
2025-08-05 16:09:56,089 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-05 16:09:56,173 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0843 s. ]
2025-08-05 16:09:56,262 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-05 16:09:56,417 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 20 s. ]
2025-08-05 16:09:56,698 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 44.27 s. ]
2025-08-05 16:09:56,902 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:56,902 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-05 16:09:56,935 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.24 s. ]
2025-08-05 16:09:56,936 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:56,936 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-05 16:09:56,974 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-05 16:09:56,975 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-08-05 16:09:56,975 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-08-05 16:09:56,975 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-08-05 16:09:56,975 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-08-05 16:09:56,974 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-08-05 16:09:56,990 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-08-05 16:09:57,168 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-08-05 16:09:57,288 [LowLevelDataService-contextInitializer-1 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-08-05 16:09:57,817 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.88 s. ]
2025-08-05 16:09:57,838 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:57,838 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-05 16:09:58,110 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-05 16:09:58,126 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.31 s. ]
2025-08-05 16:09:58,161 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:58,161 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-05 16:09:58,166 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-05 16:09:58,255 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-05 16:09:58,334 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-08-05 16:09:58,387 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-08-05 16:09:58,448 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-08-05 16:09:58,482 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-08-05 16:09:58,508 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-08-05 16:09:58,570 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-08-05 16:09:58,619 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-08-05 16:09:58,619 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.49 s. ]
2025-08-05 16:09:58,619 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:58,619 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-05 16:09:58,635 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-05 16:09:58,636 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:58,636 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-05 16:09:58,841 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-05 16:09:58,847 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-05 16:09:59,919 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 1.28 s. ]
2025-08-05 16:09:59,920 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:59,920 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-05 16:09:59,970 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.05 s. ]
2025-08-05 16:09:59,970 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:09:59,970 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-05 16:10:08,765 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 8.79 s. ]
2025-08-05 16:10:08,765 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 16:10:08,765 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 56.3 s. ]
2025-08-05 16:10:08,765 [main] INFO  com.polarion.platform.startup - ****************************************************************
