2025-08-05 16:58:58,080 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 16:58:58,080 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-05 16:58:58,080 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 16:58:58,082 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-05 16:58:58,082 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-05 16:58:58,082 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:58:58,083 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-05 16:59:05,916 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-05 16:59:06,069 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.153 s. ]
2025-08-05 16:59:06,069 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-05 16:59:06,152 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0822 s. ]
2025-08-05 16:59:06,295 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-05 16:59:06,455 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 22 s. ]
2025-08-05 16:59:06,770 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 8.7 s. ]
2025-08-05 16:59:06,900 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:59:06,900 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-05 16:59:06,941 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.17 s. ]
2025-08-05 16:59:06,941 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:59:06,941 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-05 16:59:06,947 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-08-05 16:59:06,947 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-08-05 16:59:06,947 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-08-05 16:59:06,947 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-08-05 16:59:06,947 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-08-05 16:59:06,947 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-08-05 16:59:06,953 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-05 16:59:07,144 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-05 16:59:07,263 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-05 16:59:07,766 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.83 s. ]
2025-08-05 16:59:07,784 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:59:07,784 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-05 16:59:08,059 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-05 16:59:08,070 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.3 s. ]
2025-08-05 16:59:08,110 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:59:08,110 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-05 16:59:08,114 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-05 16:59:08,176 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-05 16:59:08,225 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-08-05 16:59:08,276 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-08-05 16:59:08,299 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-05 16:59:08,327 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-05 16:59:08,456 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-05 16:59:08,527 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-05 16:59:08,615 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-05 16:59:08,615 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.55 s. ]
2025-08-05 16:59:08,615 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:59:08,615 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-05 16:59:08,632 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-05 16:59:08,651 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:59:08,651 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-05 16:59:08,760 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-08-05 16:59:08,788 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-08-05 16:59:09,000 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-05 16:59:09,002 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-05 16:59:09,131 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.5 s. ]
2025-08-05 16:59:09,132 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:59:09,133 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-05 16:59:09,153 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-08-05 16:59:09,153 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 16:59:09,153 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-05 16:59:13,022 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.87 s. ]
2025-08-05 16:59:13,023 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 16:59:13,023 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 14.9 s. ]
2025-08-05 16:59:13,023 [main] INFO  com.polarion.platform.startup - ****************************************************************
