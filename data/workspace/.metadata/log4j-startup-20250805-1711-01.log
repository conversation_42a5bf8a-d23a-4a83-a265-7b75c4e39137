2025-08-05 17:11:01,197 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 17:11:01,197 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-08-05 17:11:01,197 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 17:11:01,197 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-08-05 17:11:01,197 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-08-05 17:11:01,197 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:01,197 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-08-05 17:11:07,848 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-08-05 17:11:08,015 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.167 s. ]
2025-08-05 17:11:08,015 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-08-05 17:11:08,079 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0639 s. ]
2025-08-05 17:11:08,148 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-08-05 17:11:08,258 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 21 s. ]
2025-08-05 17:11:08,497 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 7.31 s. ]
2025-08-05 17:11:08,590 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:08,590 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-08-05 17:11:08,620 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-08-05 17:11:08,620 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:08,620 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-08-05 17:11:08,626 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-08-05 17:11:08,626 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-08-05 17:11:08,626 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-08-05 17:11:08,626 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-08-05 17:11:08,626 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-08-05 17:11:08,626 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-08-05 17:11:08,634 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-08-05 17:11:08,776 [LowLevelDataService-contextInitializer-5 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-08-05 17:11:08,889 [LowLevelDataService-contextInitializer-6 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-08-05 17:11:09,358 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-08-05 17:11:09,369 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:09,369 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-08-05 17:11:09,581 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-08-05 17:11:09,593 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-08-05 17:11:09,618 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:09,618 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-08-05 17:11:09,621 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-08-05 17:11:09,669 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-08-05 17:11:09,708 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-08-05 17:11:09,735 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-08-05 17:11:09,750 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-08-05 17:11:09,764 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-08-05 17:11:09,785 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-08-05 17:11:09,814 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-08-05 17:11:09,840 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-08-05 17:11:09,840 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.25 s. ]
2025-08-05 17:11:09,840 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:09,840 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-08-05 17:11:09,856 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-08-05 17:11:09,857 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:09,857 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-08-05 17:11:09,951 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-08-05 17:11:09,953 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-08-05 17:11:10,066 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-08-05 17:11:10,067 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:10,067 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-08-05 17:11:10,073 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-08-05 17:11:10,073 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-08-05 17:11:10,073 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-08-05 17:11:12,849 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.78 s. ]
2025-08-05 17:11:12,852 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-08-05 17:11:12,852 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.6 s. ]
2025-08-05 17:11:12,852 [main] INFO  com.polarion.platform.startup - ****************************************************************
