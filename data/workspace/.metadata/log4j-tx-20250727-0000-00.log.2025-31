2025-08-02 23:17:47,463 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0667 s [62% query (12x), 38% update (144x)] (221x), svn: 0.0129 s [50% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-08-02 23:17:47,608 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0534 s [45% getDir2 content (2x), 43% info (3x)] (6x)
2025-08-02 23:17:48,339 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.727 s, CPU [user: 0.114 s, system: 0.225 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0698 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0404 s [66% log2 (5x), 20% testConnection (1x)] (7x)
2025-08-02 23:17:48,339 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.727 s, CPU [user: 0.229 s, system: 0.348 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-02 23:17:48,339 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.727 s, CPU [user: 0.205 s, system: 0.289 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.113 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-02 23:17:48,340 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.727 s, CPU [user: 0.0595 s, system: 0.0993 s], Allocated memory: 7.4 MB, transactions: 0, ObjectMaps: 0.0627 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0416 s [64% log2 (5x), 24% testConnection (1x)] (7x)
2025-08-02 23:17:48,340 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.726 s, CPU [user: 0.0708 s, system: 0.0983 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0894 s [82% log2 (10x)] (13x), ObjectMaps: 0.0599 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 23:17:48,340 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.726 s, CPU [user: 0.111 s, system: 0.173 s], Allocated memory: 16.6 MB, transactions: 0, svn: 0.139 s [48% log2 (10x), 21% log (1x), 15% info (5x)] (24x), ObjectMaps: 0.0802 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-02 23:17:48,340 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.509 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.349 s [62% log2 (36x), 12% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-02 23:17:48,614 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.237 s [100% getReadConfiguration (48x)] (48x), svn: 0.103 s [86% info (18x)] (38x)
2025-08-02 23:17:49,014 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.32 s [75% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.248 s [100% getReadConfiguration (54x)] (54x)
2025-08-02 23:17:49,292 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.147 s, CPU [user: 0.0269 s, system: 0.00823 s], Allocated memory: 8.6 MB, GC: 0.015 s [100% G1 Young Generation (1x)] (1x)
2025-08-02 23:17:49,339 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.307 s [100% doFinishStartup (1x)] (1x), commit: 0.0773 s [100% Revision (1x)] (1x), Lucene: 0.0486 s [100% refresh (1x)] (1x), DB: 0.0193 s [46% update (3x), 33% query (1x), 13% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0185 s [100% objectsToInv (1x)] (1x)
2025-08-02 23:17:52,481 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.46 s [88% info (158x)] (170x)
2025-08-02 23:17:52,763 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661ad717bac41_0_661ad717bac41_0_: finished. Total: 0.272 s, CPU [user: 0.155 s, system: 0.0186 s], Allocated memory: 16.1 MB, resolve: 0.0787 s [98% User (2x)] (4x), Lucene: 0.042 s [100% search (1x)] (1x), svn: 0.0181 s [45% getLatestRevision (2x), 33% testConnection (1x), 20% getFile content (1x)] (5x)
2025-08-02 23:17:53,423 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.841 s, CPU [user: 0.008 s, system: 0.00137 s], Allocated memory: 356.2 kB, transactions: 1
2025-08-02 23:17:53,424 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.286 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.1 s [96% User (3x)] (6x), Lucene: 0.0787 s [53% search (1x), 32% add (1x)] (3x), Incremental Baseline: 0.0359 s [100% WorkItem (24x)] (24x), persistence listener: 0.0241 s [79% indexRefreshPersistenceListener (1x), 11% WorkItemActivityCreator (1x)] (7x), ObjectMaps: 0.0187 s [72% getPrimaryObjectLocation (2x), 16% getPrimaryObjectProperty (1x)] (7x), svn: 0.0181 s [45% getLatestRevision (2x), 33% testConnection (1x), 20% getFile content (1x)] (5x)
2025-08-02 23:17:53,425 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.948 s, CPU [user: 0.19 s, system: 0.0312 s], Allocated memory: 18.8 MB, transactions: 25, svn: 0.741 s [99% getDatedRevision (181x)] (183x)
2025-08-02 23:17:54,286 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ad717ba840_0_661ad717ba840_0_: finished. Total: 1.79 s, CPU [user: 0.509 s, system: 0.116 s], Allocated memory: 51.6 MB, svn: 1.25 s [63% getDatedRevision (181x), 23% getDir2 content (25x)] (328x), resolve: 0.494 s [100% Category (117x)] (117x), ObjectMaps: 0.168 s [42% getPrimaryObjectProperty (117x), 35% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-02 23:17:54,455 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ad7198a847_0_661ad7198a847_0_: finished. Total: 0.108 s, CPU [user: 0.0461 s, system: 0.0107 s], Allocated memory: 3.8 MB, resolve: 0.095 s [100% Project (6x)] (6x), svn: 0.0466 s [37% log (3x), 33% info (6x), 30% getFile content (6x)] (16x), ObjectMaps: 0.038 s [67% getPrimaryObjectProperty (6x), 21% getPrimaryObjectLocation (6x)] (25x)
2025-08-02 23:17:54,656 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ad719a5c48_0_661ad719a5c48_0_: finished. Total: 0.2 s, CPU [user: 0.0876 s, system: 0.0146 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.101 s [63% getReadConfiguration (180x), 37% getReadUserConfiguration (10x)] (190x), svn: 0.0973 s [58% info (21x), 38% getFile content (16x)] (39x), resolve: 0.0517 s [100% User (9x)] (9x), ObjectMaps: 0.0213 s [50% getPrimaryObjectProperty (8x), 28% getLastPromoted (8x), 22% getPrimaryObjectLocation (8x)] (32x)
2025-08-02 23:17:55,108 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ad719e744a_0_661ad719e744a_0_: finished. Total: 0.39 s, CPU [user: 0.141 s, system: 0.015 s], Allocated memory: 19.8 MB, svn: 0.277 s [68% getDir2 content (17x), 32% getFile content (44x)] (62x), RepositoryConfigService: 0.159 s [98% getReadConfiguration (170x)] (192x)
2025-08-02 23:17:56,483 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ad71a4904b_0_661ad71a4904b_0_: finished. Total: 1.38 s, CPU [user: 0.564 s, system: 0.0424 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.9 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.829 s [52% getFile content (412x), 48% getDir2 content (21x)] (434x)
2025-08-02 23:17:56,611 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ad71ba104c_0_661ad71ba104c_0_: finished. Total: 0.128 s, CPU [user: 0.0277 s, system: 0.00287 s], Allocated memory: 17.9 MB, svn: 0.114 s [86% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0254 s [97% getReadConfiguration (124x)] (148x)
2025-08-02 23:17:57,064 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ad71bd044e_0_661ad71bd044e_0_: finished. Total: 0.391 s, CPU [user: 0.159 s, system: 0.0116 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.259 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.249 s [57% getFile content (185x), 43% getDir2 content (21x)] (207x)
2025-08-02 23:17:57,170 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661ad71c3204f_0_661ad71c3204f_0_: finished. Total: 0.106 s, CPU [user: 0.0306 s, system: 0.00325 s], Allocated memory: 13.1 MB, svn: 0.0899 s [73% getDir2 content (18x), 27% getFile content (33x)] (52x), RepositoryConfigService: 0.0358 s [98% getReadConfiguration (128x)] (150x)
2025-08-02 23:17:57,171 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.68 s, CPU [user: 1.64 s, system: 0.226 s], Allocated memory: 1.6 GB, transactions: 11, svn: 3.06 s [39% getDir2 content (133x), 31% getFile content (865x), 26% getDatedRevision (181x)] (1224x), RepositoryConfigService: 1.55 s [95% getReadConfiguration (12165x)] (12859x), resolve: 0.642 s [77% Category (117x), 15% Project (7x)] (139x)
2025-08-02 23:17:57,171 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 3.81 s [40% getDatedRevision (362x), 32% getDir2 content (133x), 25% getFile content (865x)] (1408x), RepositoryConfigService: 1.55 s [95% getReadConfiguration (12165x)] (12859x), resolve: 0.643 s [77% Category (117x), 15% Project (7x)] (140x), ObjectMaps: 0.228 s [47% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-02 23:17:59,118 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6b5c6410-7f000001-3dbb0cb4-8ba8a699] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?type=%E4%BB%A3%E7%A0%81-review&_t=1754147873784': Total: 5.31 s, CPU [user: 0.0898 s, system: 0.0074 s], Allocated memory: 4.6 MB, transactions: 0
