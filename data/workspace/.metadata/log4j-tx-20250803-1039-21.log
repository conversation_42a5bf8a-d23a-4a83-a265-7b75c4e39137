2025-08-03 10:39:27,106 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0731 s [69% update (144x), 31% query (12x)] (221x), svn: 0.0227 s [58% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-08-03 10:39:27,249 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.042 s [63% getDir2 content (2x), 32% info (3x)] (6x)
2025-08-03 10:39:28,222 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.967 s, CPU [user: 0.142 s, system: 0.208 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.135 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0667 s [71% log2 (5x), 17% getLatestRevision (1x)] (7x)
2025-08-03 10:39:28,221 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.966 s, CPU [user: 0.111 s, system: 0.144 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.112 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.108 s [79% log2 (10x), 16% getLatestRevision (2x)] (13x)
2025-08-03 10:39:28,221 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.967 s, CPU [user: 0.252 s, system: 0.284 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.18 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-03 10:39:28,222 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.967 s, CPU [user: 0.0786 s, system: 0.0728 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.125 s [81% log2 (10x)] (13x), ObjectMaps: 0.052 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-03 10:39:28,222 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.967 s, CPU [user: 0.33 s, system: 0.375 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.179 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0505 s [69% log2 (5x), 17% testConnection (1x)] (7x)
2025-08-03 10:39:28,222 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.967 s, CPU [user: 0.0848 s, system: 0.0745 s], Allocated memory: 9.5 MB, transactions: 0, svn: 0.0949 s [37% log2 (5x), 25% info (5x), 17% log (1x), 10% getLatestRevision (2x)] (18x), ObjectMaps: 0.0552 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-03 10:39:28,223 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.712 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.464 s [67% log2 (36x), 15% getLatestRevision (9x)] (61x)
2025-08-03 10:39:28,445 [main | u:p] INFO  TXLOGGER - Tx 661b731904801_0_661b731904801_0_: finished. Total: 0.169 s, CPU [user: 0.12 s, system: 0.0135 s], Allocated memory: 21.8 MB
2025-08-03 10:39:28,644 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.352 s [100% getReadConfiguration (48x)] (48x), svn: 0.107 s [82% info (18x)] (38x)
2025-08-03 10:39:29,119 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.383 s [73% info (94x), 19% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.282 s [100% getReadConfiguration (54x)] (54x)
2025-08-03 10:39:29,374 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.111 s, CPU [user: 0.0263 s, system: 0.0075 s], Allocated memory: 8.6 MB
2025-08-03 10:39:29,420 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.279 s [100% doFinishStartup (1x)] (1x), commit: 0.0614 s [100% Revision (1x)] (1x), Lucene: 0.0349 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0224 s [100% objectsToInv (1x)] (1x), DB: 0.0147 s [45% update (3x), 34% query (1x), 12% commit (2x)] (8x)
2025-08-03 10:39:32,582 [ajp-nio-127.0.0.1-8889-exec-1 | cID:6dcc7451-7f000001-7f1e7f81-ba578af5] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754188770578': Total: 0.148 s, CPU [user: 0.0818 s, system: 0.00784 s], Allocated memory: 4.7 MB, transactions: 0
2025-08-03 10:39:32,595 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6dcc7451-7f000001-7f1e7f81-8c2c60e4] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754188770578': Total: 0.162 s, CPU [user: 0.0707 s, system: 0.00794 s], Allocated memory: 3.9 MB, transactions: 0
2025-08-03 10:39:32,847 [main | u:p | u:p] INFO  TXLOGGER - Tx 661b731d5643f_0_661b731d5643f_0_: finished. Total: 0.149 s, CPU [user: 0.103 s, system: 0.0132 s], Allocated memory: 7.4 MB, GlobalHandler: 0.0176 s [96% applyTxChanges (1x)] (4x), resolve: 0.0105 s [100% ProjectGroup (3x)] (3x)
2025-08-03 10:39:33,659 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.625 s [89% info (158x)] (170x)
2025-08-03 10:39:34,080 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661b731e4bc41_0_661b731e4bc41_0_: finished. Total: 0.4 s, CPU [user: 0.196 s, system: 0.0216 s], Allocated memory: 16.1 MB, resolve: 0.14 s [98% User (2x)] (4x), ObjectMaps: 0.0414 s [75% getPrimaryObjectProperty (1x), 17% getPrimaryObjectLocation (1x)] (6x), Lucene: 0.0224 s [100% search (1x)] (1x)
2025-08-03 10:39:34,927 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.15 s, CPU [user: 0.0103 s, system: 0.00187 s], Allocated memory: 356.2 kB, transactions: 1
2025-08-03 10:39:34,927 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.421 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.196 s [95% User (4x)] (7x), Lucene: 0.0622 s [45% add (1x), 36% search (1x)] (3x), ObjectMaps: 0.0547 s [57% getPrimaryObjectProperty (1x), 37% getPrimaryObjectLocation (3x)] (8x), Incremental Baseline: 0.0471 s [100% WorkItem (24x)] (24x), persistence listener: 0.0307 s [81% indexRefreshPersistenceListener (1x)] (7x)
2025-08-03 10:39:34,929 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.15 s, CPU [user: 0.244 s, system: 0.0386 s], Allocated memory: 19.2 MB, transactions: 26, svn: 1.02 s [99% getDatedRevision (181x)] (183x)
2025-08-03 10:39:35,843 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b731e4ac40_0_661b731e4ac40_0_: finished. Total: 2.17 s, CPU [user: 0.66 s, system: 0.152 s], Allocated memory: 51.6 MB, svn: 1.45 s [56% getDatedRevision (181x), 25% getDir2 content (25x)] (328x), resolve: 0.783 s [100% Category (117x)] (117x), ObjectMaps: 0.274 s [42% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-03 10:39:36,037 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b73207a847_0_661b73207a847_0_: finished. Total: 0.122 s, CPU [user: 0.0549 s, system: 0.0121 s], Allocated memory: 3.8 MB, resolve: 0.102 s [100% Project (6x)] (6x), svn: 0.0583 s [37% log (3x), 32% info (6x), 30% getFile content (6x)] (16x), ObjectMaps: 0.039 s [73% getPrimaryObjectProperty (6x), 17% getPrimaryObjectLocation (6x)] (25x)
2025-08-03 10:39:36,242 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b732099c48_0_661b732099c48_0_: finished. Total: 0.203 s, CPU [user: 0.0912 s, system: 0.0158 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.103 s [53% getReadConfiguration (180x), 47% getReadUserConfiguration (10x)] (190x), svn: 0.0866 s [54% info (21x), 41% getFile content (16x)] (39x), resolve: 0.0505 s [100% User (9x)] (9x), ObjectMaps: 0.0203 s [55% getPrimaryObjectProperty (8x), 25% getLastPromoted (8x), 20% getPrimaryObjectLocation (8x)] (32x)
2025-08-03 10:39:36,597 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b7320da84a_0_661b7320da84a_0_: finished. Total: 0.299 s, CPU [user: 0.0968 s, system: 0.0106 s], Allocated memory: 19.8 MB, svn: 0.238 s [73% getDir2 content (17x), 27% getFile content (44x)] (62x), RepositoryConfigService: 0.101 s [98% getReadConfiguration (170x)] (192x)
2025-08-03 10:39:37,633 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b73212584b_0_661b73212584b_0_: finished. Total: 1.03 s, CPU [user: 0.483 s, system: 0.0298 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.751 s [96% getReadConfiguration (8682x)] (9021x), svn: 0.576 s [62% getFile content (412x), 38% getDir2 content (21x)] (434x)
2025-08-03 10:39:37,801 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b73222844c_0_661b73222844c_0_: finished. Total: 0.168 s, CPU [user: 0.04 s, system: 0.00376 s], Allocated memory: 17.9 MB, svn: 0.149 s [83% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0382 s [98% getReadConfiguration (124x)] (148x)
2025-08-03 10:39:38,339 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b73226144e_0_661b73226144e_0_: finished. Total: 0.477 s, CPU [user: 0.204 s, system: 0.0146 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.335 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.294 s [62% getFile content (185x), 37% getDir2 content (21x)] (207x)
2025-08-03 10:39:38,480 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b7322d8c4f_0_661b7322d8c4f_0_: finished. Total: 0.141 s, CPU [user: 0.0341 s, system: 0.00328 s], Allocated memory: 13.1 MB, svn: 0.113 s [78% getDir2 content (18x), 22% getFile content (33x)] (52x), RepositoryConfigService: 0.0458 s [98% getReadConfiguration (128x)] (150x)
2025-08-03 10:39:38,481 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.81 s, CPU [user: 1.75 s, system: 0.252 s], Allocated memory: 1.6 GB, transactions: 11, svn: 3.07 s [37% getDir2 content (133x), 32% getFile content (865x), 27% getDatedRevision (181x)] (1224x), RepositoryConfigService: 1.47 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.937 s [84% Category (117x)] (139x), ObjectMaps: 0.333 s [47% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-03 10:39:38,481 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 4.1 s [45% getDatedRevision (362x), 28% getDir2 content (133x), 24% getFile content (865x)] (1409x), RepositoryConfigService: 1.47 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.938 s [84% Category (117x)] (140x), ObjectMaps: 0.333 s [47% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
