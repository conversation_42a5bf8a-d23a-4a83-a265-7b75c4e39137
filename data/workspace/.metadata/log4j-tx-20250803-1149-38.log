2025-08-03 11:49:44,190 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0666 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0233 s [60% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-08-03 11:49:44,332 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0402 s [64% getDir2 content (2x), 27% info (3x)] (6x)
2025-08-03 11:49:45,302 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.965 s, CPU [user: 0.0797 s, system: 0.0875 s], Allocated memory: 7.2 MB, transactions: 0, ObjectMaps: 0.0633 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-03 11:49:45,302 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.965 s, CPU [user: 0.307 s, system: 0.331 s], Allocated memory: 53.3 MB, transactions: 0, ObjectMaps: 0.217 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-03 11:49:45,302 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.965 s, CPU [user: 0.166 s, system: 0.234 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.153 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-03 11:49:45,302 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.964 s, CPU [user: 0.355 s, system: 0.405 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.16 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-03 11:49:45,302 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.964 s, CPU [user: 0.126 s, system: 0.165 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0775 s [77% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-08-03 11:49:45,302 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.965 s, CPU [user: 0.119 s, system: 0.0977 s], Allocated memory: 10.9 MB, transactions: 0, svn: 0.169 s [53% log2 (10x), 17% info (5x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.0578 s [99% getAllPrimaryObjects (2x)] (14x)
2025-08-03 11:49:45,303 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.77 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.381 s [64% log2 (36x), 13% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-08-03 11:49:45,475 [main | u:p] INFO  TXLOGGER - Tx 661b832f3cc01_0_661b832f3cc01_0_: finished. Total: 0.142 s, CPU [user: 0.109 s, system: 0.00737 s], Allocated memory: 21.8 MB
2025-08-03 11:49:45,662 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.314 s [100% getReadConfiguration (48x)] (48x), svn: 0.101 s [85% info (18x)] (38x)
2025-08-03 11:49:46,142 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.377 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.287 s [100% getReadConfiguration (54x)] (54x)
2025-08-03 11:49:46,466 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.139 s, CPU [user: 0.0289 s, system: 0.00739 s], Allocated memory: 8.6 MB
2025-08-03 11:49:46,502 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.34 s [100% doFinishStartup (1x)] (1x), commit: 0.0614 s [100% Revision (1x)] (1x), Lucene: 0.0441 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0252 s [100% objectsToInv (1x)] (1x)
2025-08-03 11:49:50,630 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.67 s [92% info (158x)] (170x)
2025-08-03 11:49:50,957 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661b83346e845_0_661b83346e845_0_: finished. Total: 0.306 s, CPU [user: 0.187 s, system: 0.0211 s], Allocated memory: 16.1 MB, resolve: 0.0934 s [97% User (2x)] (4x), Lucene: 0.0262 s [100% search (1x)] (1x), svn: 0.02 s [65% getLatestRevision (2x), 28% testConnection (1x)] (5x), ObjectMaps: 0.0164 s [55% getPrimaryObjectLocation (1x), 26% getPrimaryObjectProperty (1x)] (6x)
2025-08-03 11:49:51,776 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.00916 s, system: 0.00195 s], Allocated memory: 356.3 kB, transactions: 1
2025-08-03 11:49:51,777 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.319 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.134 s [96% User (4x)] (7x), Lucene: 0.0479 s [55% search (1x), 24% refresh (1x), 21% add (1x)] (3x), Incremental Baseline: 0.0447 s [100% WorkItem (24x)] (24x), persistence listener: 0.0294 s [77% indexRefreshPersistenceListener (1x), 13% WorkItemActivityCreator (1x)] (7x), ObjectMaps: 0.0261 s [72% getPrimaryObjectLocation (3x), 16% getPrimaryObjectProperty (1x)] (8x), svn: 0.02 s [65% getLatestRevision (2x), 28% testConnection (1x)] (5x)
2025-08-03 11:49:51,777 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.04 s, CPU [user: 0.235 s, system: 0.0356 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.926 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0632 s [71% buildBaselineSnapshots (1x), 29% buildBaseline (25x)] (26x)
2025-08-03 11:49:52,467 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b83346cc40_0_661b83346cc40_0_: finished. Total: 1.82 s, CPU [user: 0.582 s, system: 0.145 s], Allocated memory: 51.6 MB, svn: 1.14 s [55% getDatedRevision (181x), 27% getDir2 content (25x)] (328x), resolve: 0.654 s [100% Category (117x)] (117x), ObjectMaps: 0.239 s [40% getPrimaryObjectProperty (117x), 38% getPrimaryObjectLocation (117x), 22% getLastPromoted (117x)] (473x)
2025-08-03 11:49:52,643 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b833643447_0_661b833643447_0_: finished. Total: 0.117 s, CPU [user: 0.0524 s, system: 0.0119 s], Allocated memory: 3.8 MB, resolve: 0.1 s [100% Project (6x)] (6x), svn: 0.0543 s [41% log (3x), 35% info (6x), 24% getFile content (6x)] (16x), ObjectMaps: 0.0416 s [74% getPrimaryObjectProperty (6x), 16% getPrimaryObjectLocation (6x)] (25x)
2025-08-03 11:49:52,839 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b833661048_0_661b833661048_0_: finished. Total: 0.195 s, CPU [user: 0.101 s, system: 0.0175 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0955 s [52% getReadConfiguration (180x), 48% getReadUserConfiguration (10x)] (190x), svn: 0.0842 s [61% info (21x), 32% getFile content (16x)] (39x), resolve: 0.0508 s [100% User (9x)] (9x), ObjectMaps: 0.0221 s [59% getPrimaryObjectProperty (8x), 21% getPrimaryObjectLocation (8x), 20% getLastPromoted (8x)] (32x)
2025-08-03 11:49:53,260 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b8336a344a_0_661b8336a344a_0_: finished. Total: 0.35 s, CPU [user: 0.122 s, system: 0.0146 s], Allocated memory: 19.8 MB, svn: 0.273 s [71% getDir2 content (17x), 29% getFile content (44x)] (62x), RepositoryConfigService: 0.126 s [98% getReadConfiguration (170x)] (192x)
2025-08-03 11:49:54,325 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b8336fb04b_0_661b8336fb04b_0_: finished. Total: 1.06 s, CPU [user: 0.494 s, system: 0.0276 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.806 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.591 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x)
2025-08-03 11:49:54,430 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b83380544c_0_661b83380544c_0_: finished. Total: 0.105 s, CPU [user: 0.0215 s, system: 0.00217 s], Allocated memory: 17.9 MB, svn: 0.0932 s [85% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0213 s [97% getReadConfiguration (124x)] (148x)
2025-08-03 11:49:54,810 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b83382944e_0_661b83382944e_0_: finished. Total: 0.341 s, CPU [user: 0.15 s, system: 0.00972 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.231 s [95% getReadConfiguration (2787x)] (3025x), svn: 0.202 s [58% getFile content (185x), 42% getDir2 content (21x)] (207x)
2025-08-03 11:49:54,928 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661b83387e84f_0_661b83387e84f_0_: finished. Total: 0.117 s, CPU [user: 0.0348 s, system: 0.00401 s], Allocated memory: 13.1 MB, svn: 0.101 s [81% getDir2 content (18x)] (52x), RepositoryConfigService: 0.0308 s [98% getReadConfiguration (128x)] (150x)
2025-08-03 11:49:54,928 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.29 s, CPU [user: 1.64 s, system: 0.241 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.63 s [37% getDir2 content (133x), 34% getFile content (865x), 24% getDatedRevision (181x)] (1224x), RepositoryConfigService: 1.38 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.807 s [81% Category (117x)] (139x), ObjectMaps: 0.302 s [46% getPrimaryObjectProperty (131x), 34% getPrimaryObjectLocation (137x)] (536x)
2025-08-03 11:49:54,928 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 3.56 s [43% getDatedRevision (362x), 28% getDir2 content (133x), 25% getFile content (865x)] (1408x), RepositoryConfigService: 1.38 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.807 s [81% Category (117x)] (140x), ObjectMaps: 0.302 s [46% getPrimaryObjectProperty (131x), 34% getPrimaryObjectLocation (137x)] (536x)
2025-08-03 11:49:58,297 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6e0ceeed-7f000001-7403b7df-9b2fbe4d] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?_t=1754192998107': Total: 0.171 s, CPU [user: 0.0395 s, system: 0.0072 s], Allocated memory: 1.6 MB, transactions: 0
2025-08-03 11:49:58,324 [ajp-nio-127.0.0.1-8889-exec-3 | cID:6e0ceeed-7f000001-7403b7df-87865700] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?_t=1754192998107': Total: 0.198 s, CPU [user: 0.134 s, system: 0.0161 s], Allocated memory: 4.8 MB, transactions: 0
2025-08-03 11:57:28,548 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.026953125
2025-08-03 12:00:18,551 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.12314453125
2025-08-03 13:40:58,567 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.10224609375
