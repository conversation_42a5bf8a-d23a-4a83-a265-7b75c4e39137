2025-08-03 17:18:53,011 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0777 s [62% update (144x), 37% query (12x)] (221x), svn: 0.0185 s [58% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-08-03 17:18:53,161 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0407 s [61% getDir2 content (2x), 32% info (3x)] (6x)
2025-08-03 17:18:54,099 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.934 s, CPU [user: 0.0875 s, system: 0.0826 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.113 s [82% log2 (10x)] (13x), ObjectMaps: 0.049 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-03 17:18:54,102 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.934 s, CPU [user: 0.282 s, system: 0.293 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.155 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-03 17:18:54,102 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.934 s, CPU [user: 0.0722 s, system: 0.0808 s], Allocated memory: 7.2 MB, transactions: 0, ObjectMaps: 0.0522 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0477 s [74% log2 (5x), 14% getLatestRevision (1x)] (7x)
2025-08-03 17:18:54,099 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.934 s, CPU [user: 0.169 s, system: 0.219 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.105 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-03 17:18:54,102 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.934 s, CPU [user: 0.127 s, system: 0.155 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0933 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.092 s [68% log2 (10x), 26% getLatestRevision (2x)] (13x)
2025-08-03 17:18:54,102 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.934 s, CPU [user: 0.361 s, system: 0.376 s], Allocated memory: 71.1 MB, transactions: 0, ObjectMaps: 0.218 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.104 s [38% log (1x), 21% log2 (5x), 20% info (5x), 10% getLatestRevision (2x)] (18x)
2025-08-03 17:18:54,103 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.673 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.41 s [60% log2 (36x), 16% getLatestRevision (9x), 10% log (1x)] (61x)
2025-08-03 17:18:54,261 [main | u:p] INFO  TXLOGGER - Tx 661bce852d801_0_661bce852d801_0_: finished. Total: 0.125 s, CPU [user: 0.0947 s, system: 0.00568 s], Allocated memory: 21.8 MB
2025-08-03 17:18:54,430 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.268 s [100% getReadConfiguration (48x)] (48x), svn: 0.0984 s [74% info (18x), 15% getLatestRevision (1x)] (38x)
2025-08-03 17:18:54,906 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.377 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.286 s [100% getReadConfiguration (54x)] (54x)
2025-08-03 17:18:55,184 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.107 s, CPU [user: 0.00605 s, system: 0.00256 s], Allocated memory: 900.7 kB
2025-08-03 17:18:55,205 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.13 s, CPU [user: 0.021 s, system: 0.00669 s], Allocated memory: 8.6 MB
2025-08-03 17:18:55,264 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 28, PersistenceEngineListener: 0.332 s [100% doFinishStartup (1x)] (1x), commit: 0.0535 s [100% Revision (1x)] (1x), DB: 0.0403 s [55% commit (2x), 31% update (3x)] (8x), Lucene: 0.0382 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0241 s [100% objectsToInv (1x)] (1x)
2025-08-03 17:18:58,713 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.475 s [88% info (158x)] (170x)
2025-08-03 17:18:58,991 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661bce89a9c45_0_661bce89a9c45_0_: finished. Total: 0.263 s, CPU [user: 0.164 s, system: 0.0174 s], Allocated memory: 16.1 MB, resolve: 0.0816 s [95% User (2x)] (4x), svn: 0.0171 s [66% getLatestRevision (2x), 23% testConnection (1x)] (5x), Lucene: 0.0168 s [100% search (1x)] (1x), ObjectMaps: 0.015 s [47% getPrimaryObjectLocation (1x), 36% getPrimaryObjectProperty (1x)] (6x)
2025-08-03 17:18:59,758 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.953 s, CPU [user: 0.00755 s, system: 0.00174 s], Allocated memory: 356.6 kB, transactions: 1
2025-08-03 17:18:59,759 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.279 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.102 s [92% User (3x)] (6x), Incremental Baseline: 0.037 s [100% WorkItem (24x)] (24x), Lucene: 0.0329 s [51% search (1x), 36% add (1x)] (3x), persistence listener: 0.0251 s [73% indexRefreshPersistenceListener (1x), 16% WorkItemActivityCreator (1x)] (7x), ObjectMaps: 0.0189 s [58% getPrimaryObjectLocation (2x), 29% getPrimaryObjectProperty (1x)] (7x), svn: 0.0171 s [66% getLatestRevision (2x), 23% testConnection (1x)] (5x)
2025-08-03 17:18:59,760 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.05 s, CPU [user: 0.218 s, system: 0.0341 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.857 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0582 s [68% buildBaselineSnapshots (1x), 32% buildBaseline (25x)] (26x)
2025-08-03 17:19:00,414 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661bce89a8840_0_661bce89a8840_0_: finished. Total: 1.69 s, CPU [user: 0.545 s, system: 0.132 s], Allocated memory: 51.6 MB, svn: 1.05 s [58% getDatedRevision (181x), 25% getDir2 content (25x)] (328x), resolve: 0.555 s [100% Category (117x)] (117x), ObjectMaps: 0.209 s [42% getPrimaryObjectProperty (117x), 36% getPrimaryObjectLocation (117x), 22% getLastPromoted (117x)] (473x)
2025-08-03 17:19:00,715 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661bce8b71848_0_661bce8b71848_0_: finished. Total: 0.164 s, CPU [user: 0.0827 s, system: 0.0147 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0802 s [52% getReadConfiguration (180x), 48% getReadUserConfiguration (10x)] (190x), svn: 0.0734 s [57% info (21x), 36% getFile content (16x)] (39x), resolve: 0.0496 s [100% User (9x)] (9x), ObjectMaps: 0.0213 s [57% getPrimaryObjectProperty (8x), 22% getPrimaryObjectLocation (8x), 21% getLastPromoted (8x)] (32x)
2025-08-03 17:19:01,056 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661bce8ba9c4a_0_661bce8ba9c4a_0_: finished. Total: 0.28 s, CPU [user: 0.0989 s, system: 0.01 s], Allocated memory: 19.8 MB, svn: 0.213 s [69% getDir2 content (17x), 31% getFile content (44x)] (62x), RepositoryConfigService: 0.108 s [98% getReadConfiguration (170x)] (192x)
2025-08-03 17:19:02,035 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661bce8bf004b_0_661bce8bf004b_0_: finished. Total: 0.979 s, CPU [user: 0.465 s, system: 0.0262 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.731 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.532 s [67% getFile content (412x), 33% getDir2 content (21x)] (434x)
2025-08-03 17:19:02,142 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661bce8ce4c4c_0_661bce8ce4c4c_0_: finished. Total: 0.106 s, CPU [user: 0.0215 s, system: 0.00215 s], Allocated memory: 17.9 MB, svn: 0.09 s [83% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0274 s [98% getReadConfiguration (124x)] (148x)
2025-08-03 17:19:02,504 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661bce8d0a04e_0_661bce8d0a04e_0_: finished. Total: 0.32 s, CPU [user: 0.141 s, system: 0.00867 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.214 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.193 s [57% getFile content (185x), 43% getDir2 content (21x)] (207x)
2025-08-03 17:19:02,600 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.88 s, CPU [user: 1.49 s, system: 0.212 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.36 s [36% getDir2 content (133x), 34% getFile content (865x), 26% getDatedRevision (181x)] (1224x), RepositoryConfigService: 1.27 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.688 s [81% Category (117x)] (139x), ObjectMaps: 0.259 s [47% getPrimaryObjectProperty (131x), 33% getPrimaryObjectLocation (137x), 21% getLastPromoted (131x)] (536x)
2025-08-03 17:19:02,600 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 3.21 s [45% getDatedRevision (362x), 27% getDir2 content (133x), 25% getFile content (865x)] (1408x), RepositoryConfigService: 1.27 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.688 s [81% Category (117x)] (140x), ObjectMaps: 0.259 s [47% getPrimaryObjectProperty (131x), 33% getPrimaryObjectLocation (137x), 21% getLastPromoted (131x)] (536x)
2025-08-03 17:20:55,308 [ajp-nio-127.0.0.1-8889-exec-2 | cID:6f3bed0c-c0a844bd-4509be44-4c350227] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?type=123&_t=1754212855029': Total: 0.254 s, CPU [user: 0.151 s, system: 0.025 s], Allocated memory: 5.3 MB, transactions: 0
2025-08-03 17:20:55,783 [ajp-nio-127.0.0.1-8889-exec-5 | cID:6f3beec9-c0a844bd-4509be44-cc9595cf] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-version/review-versions': Total: 0.285 s, CPU [user: 0.0819 s, system: 0.0362 s], Allocated memory: 2.4 MB, transactions: 0
