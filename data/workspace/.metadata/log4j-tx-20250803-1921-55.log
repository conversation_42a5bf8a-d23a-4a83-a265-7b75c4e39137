2025-08-03 19:22:01,081 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0394 s [62% update (144x), 38% query (12x)] (221x), svn: 0.016 s [48% getLatestRevision (2x), 39% testConnection (1x)] (4x)
2025-08-03 19:22:01,199 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0251 s [55% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-03 19:22:01,931 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.727 s, CPU [user: 0.252 s, system: 0.349 s], Allocated memory: 68.7 MB, transactions: 0, ObjectMaps: 0.133 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-03 19:22:01,931 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.727 s, CPU [user: 0.0664 s, system: 0.0857 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0781 s [84% log2 (10x)] (13x), ObjectMaps: 0.0617 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-03 19:22:01,931 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.728 s, CPU [user: 0.128 s, system: 0.218 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0894 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-03 19:22:01,931 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.726 s, CPU [user: 0.104 s, system: 0.167 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.103 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0728 s [80% log2 (10x)] (13x)
2025-08-03 19:22:01,932 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.728 s, CPU [user: 0.209 s, system: 0.283 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.113 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-03 19:22:01,932 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.728 s, CPU [user: 0.0786 s, system: 0.0929 s], Allocated memory: 9.5 MB, transactions: 0, svn: 0.0841 s [44% log2 (5x), 19% log (1x), 18% info (5x)] (18x), ObjectMaps: 0.0609 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-03 19:22:01,932 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.561 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.294 s [67% log2 (36x), 12% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-08-03 19:22:02,421 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.332 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.275 s [100% getReadConfiguration (54x)] (54x)
2025-08-03 19:22:02,570 [main | u:p] INFO  TXLOGGER - Tx 661beab45442e_0_661beab45442e_0_: finished. Total: 0.12 s, CPU [user: 0.0886 s, system: 0.00687 s], Allocated memory: 21.6 MB
2025-08-03 19:22:02,764 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.3 s [100% getReadConfiguration (48x)] (48x), svn: 0.0876 s [86% info (18x)] (38x)
2025-08-03 19:22:02,988 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.117 s, CPU [user: 0.0351 s, system: 0.00859 s], Allocated memory: 9.5 MB, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-08-03 19:22:03,032 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.266 s [100% doFinishStartup (1x)] (1x), commit: 0.0688 s [100% Revision (1x)] (1x), Lucene: 0.0499 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0164 s [100% objectsToInv (1x)] (1x)
2025-08-03 19:22:05,806 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.317 s [82% info (158x)] (168x)
2025-08-03 19:22:06,017 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661beab79d444_0_661beab79d444_0_: finished. Total: 0.203 s, CPU [user: 0.115 s, system: 0.0133 s], Allocated memory: 16.1 MB, resolve: 0.053 s [94% User (2x)] (4x), Lucene: 0.0252 s [100% search (1x)] (1x), svn: 0.011 s [60% getLatestRevision (2x), 32% testConnection (1x)] (5x), ObjectMaps: 0.0107 s [51% getPrimaryObjectLocation (1x), 38% getPrimaryObjectProperty (1x)] (6x)
2025-08-03 19:22:06,554 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.675 s, CPU [user: 0.00574 s, system: 0.000988 s], Allocated memory: 357.0 kB, transactions: 1
2025-08-03 19:22:06,555 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.213 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.0672 s [90% User (3x)] (6x), Lucene: 0.0518 s [49% search (1x), 34% add (1x)] (3x), Incremental Baseline: 0.0235 s [100% WorkItem (24x)] (24x), svn: 0.0203 s [50% getLatestRevision (3x), 46% testConnection (2x)] (7x), persistence listener: 0.0152 s [74% indexRefreshPersistenceListener (1x), 12% WorkItemActivityCreator (1x)] (7x), ObjectMaps: 0.0137 s [61% getPrimaryObjectLocation (2x), 30% getPrimaryObjectProperty (1x)] (7x)
2025-08-03 19:22:06,555 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.77 s, CPU [user: 0.147 s, system: 0.0249 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.588 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0488 s [70% buildBaselineSnapshots (1x), 30% buildBaseline (25x)] (26x)
2025-08-03 19:22:06,846 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661beab79d040_0_661beab79d040_0_: finished. Total: 1.03 s, CPU [user: 0.326 s, system: 0.094 s], Allocated memory: 51.6 MB, svn: 0.586 s [46% getDatedRevision (181x), 31% getDir2 content (25x), 21% getFile content (119x)] (328x), resolve: 0.44 s [100% Category (117x)] (117x), ObjectMaps: 0.173 s [37% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 29% getLastPromoted (117x)] (473x)
2025-08-03 19:22:07,186 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661beab8d3c4a_0_661beab8d3c4a_0_: finished. Total: 0.131 s, CPU [user: 0.0467 s, system: 0.00402 s], Allocated memory: 19.8 MB, svn: 0.0941 s [65% getDir2 content (17x), 35% getFile content (44x)] (62x), RepositoryConfigService: 0.0519 s [98% getReadConfiguration (170x)] (192x)
2025-08-03 19:22:07,902 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661beab8f484b_0_661beab8f484b_0_: finished. Total: 0.715 s, CPU [user: 0.343 s, system: 0.0193 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.485 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.397 s [55% getFile content (412x), 45% getDir2 content (21x)] (434x)
2025-08-03 19:22:08,081 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661beab9a784c_0_661beab9a784c_0_: finished. Total: 0.178 s, CPU [user: 0.0311 s, system: 0.00511 s], Allocated memory: 18.2 MB, svn: 0.167 s [88% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0278 s [97% getReadConfiguration (124x)] (148x)
2025-08-03 19:22:08,375 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661beab9dd84e_0_661beab9dd84e_0_: finished. Total: 0.256 s, CPU [user: 0.107 s, system: 0.00633 s], Allocated memory: 390.8 MB, RepositoryConfigService: 0.169 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.151 s [53% getFile content (185x), 47% getDir2 content (21x)] (207x)
2025-08-03 19:22:08,433 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.62 s, CPU [user: 0.981 s, system: 0.147 s], Allocated memory: 1.7 GB, transactions: 11, svn: 1.56 s [46% getDir2 content (133x), 34% getFile content (865x), 17% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.832 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.513 s [86% Category (117x)] (139x), ObjectMaps: 0.207 s [39% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 28% getLastPromoted (131x)] (536x)
2025-08-03 19:22:08,433 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 2.14 s [39% getDatedRevision (362x), 33% getDir2 content (133x), 24% getFile content (865x)] (1407x), RepositoryConfigService: 0.832 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.513 s [86% Category (117x)] (140x), ObjectMaps: 0.207 s [39% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 28% getLastPromoted (131x)] (536x)
