2025-08-04 09:42:51,104 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0463 s [61% query (12x), 39% update (144x)] (221x), svn: 0.0151 s [65% getLatestRevision (2x), 26% testConnection (1x)] (4x)
2025-08-04 09:42:51,216 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.037 s [57% getDir2 content (2x), 37% info (3x)] (6x)
2025-08-04 09:42:51,933 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.714 s, CPU [user: 0.188 s, system: 0.315 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 09:42:51,933 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.714 s, CPU [user: 0.115 s, system: 0.254 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.103 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 09:42:51,933 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.714 s, CPU [user: 0.0542 s, system: 0.102 s], Allocated memory: 7.2 MB, transactions: 0, ObjectMaps: 0.058 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 09:42:51,933 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.0926 s, system: 0.186 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.0921 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0478 s [74% log2 (10x), 16% getLatestRevision (2x)] (13x)
2025-08-04 09:42:51,933 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.714 s, CPU [user: 0.23 s, system: 0.375 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 09:42:51,934 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.0732 s, system: 0.0993 s], Allocated memory: 10.9 MB, transactions: 0, svn: 0.0973 s [51% log2 (10x), 14% info (5x), 14% log (1x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.06 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 09:42:51,934 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.555 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.227 s [61% log2 (36x), 15% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-04 09:42:52,198 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.192 s [76% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.15 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 09:42:52,413 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.181 s [100% getReadConfiguration (48x)] (48x), svn: 0.0659 s [87% info (18x)] (38x)
2025-08-04 09:42:52,603 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.1 s, CPU [user: 0.024 s, system: 0.00841 s], Allocated memory: 8.5 MB
2025-08-04 09:42:52,624 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.21 s [100% doFinishStartup (1x)] (1x), commit: 0.0544 s [100% Revision (1x)] (1x), Lucene: 0.0262 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0153 s [100% objectsToInv (1x)] (1x)
2025-08-04 09:42:54,718 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.242 s [88% info (158x)] (168x)
2025-08-04 09:42:54,722 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 1, notification worker: 0.000411 s [52% BuildActivityCreator (1x), 48% TestRunActivityCreator (1x)] (2x), EHCache: 0.0000449 s [100% GET (3x)] (3x)
2025-08-04 09:42:54,968 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cafbe01044_0_661cafbe01044_0_: finished. Total: 0.243 s, CPU [user: 0.133 s, system: 0.0151 s], Allocated memory: 16.1 MB, resolve: 0.0538 s [95% User (2x)] (4x), Lucene: 0.0227 s [100% search (1x)] (1x)
2025-08-04 09:42:55,455 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.636 s, CPU [user: 0.00603 s, system: 0.00159 s], Allocated memory: 357.6 kB, transactions: 1
2025-08-04 09:42:55,456 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.254 s [98% RevisionActivityCreator (2x)] (4x), resolve: 0.0693 s [93% User (3x)] (6x), Lucene: 0.0375 s [60% search (1x), 26% add (1x)] (3x), Incremental Baseline: 0.0316 s [100% WorkItem (24x)] (24x)
2025-08-04 09:42:55,456 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.751 s, CPU [user: 0.138 s, system: 0.0241 s], Allocated memory: 19.3 MB, transactions: 26, svn: 0.564 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0393 s [67% buildBaselineSnapshots (1x), 33% buildBaseline (25x)] (26x)
2025-08-04 09:42:55,776 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cafbe00c43_0_661cafbe00c43_0_: finished. Total: 1.05 s, CPU [user: 0.32 s, system: 0.0954 s], Allocated memory: 51.6 MB, svn: 0.595 s [50% getDatedRevision (181x), 29% getDir2 content (25x), 20% getFile content (119x)] (328x), resolve: 0.398 s [100% Category (117x)] (117x), ObjectMaps: 0.153 s [40% getPrimaryObjectProperty (117x), 35% getPrimaryObjectLocation (117x), 25% getLastPromoted (117x)] (473x)
2025-08-04 09:42:55,952 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cafbf19848_0_661cafbf19848_0_: finished. Total: 0.106 s, CPU [user: 0.0536 s, system: 0.00827 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0492 s [54% getReadConfiguration (180x), 46% getReadUserConfiguration (10x)] (190x), svn: 0.0455 s [60% info (21x), 35% getFile content (16x)] (39x), resolve: 0.0301 s [100% User (9x)] (9x), ObjectMaps: 0.0137 s [47% getPrimaryObjectProperty (8x), 35% getLastPromoted (8x)] (32x)
2025-08-04 09:42:56,120 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cafbf3c84a_0_661cafbf3c84a_0_: finished. Total: 0.134 s, CPU [user: 0.0444 s, system: 0.00469 s], Allocated memory: 19.8 MB, svn: 0.0883 s [68% getDir2 content (17x), 32% getFile content (44x)] (62x), RepositoryConfigService: 0.0603 s [99% getReadConfiguration (170x)] (192x), GC: 0.013 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 09:42:56,843 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cafbf5e04b_0_661cafbf5e04b_0_: finished. Total: 0.722 s, CPU [user: 0.34 s, system: 0.0265 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.485 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.398 s [53% getFile content (412x), 47% getDir2 content (21x)] (434x)
2025-08-04 09:42:57,171 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cafc02d04e_0_661cafc02d04e_0_: finished. Total: 0.223 s, CPU [user: 0.0929 s, system: 0.0054 s], Allocated memory: 390.8 MB, RepositoryConfigService: 0.139 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.137 s [51% getDir2 content (21x), 49% getFile content (185x)] (207x)
2025-08-04 09:42:57,227 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.51 s, CPU [user: 0.943 s, system: 0.152 s], Allocated memory: 1.7 GB, transactions: 11, svn: 1.45 s [42% getDir2 content (133x), 34% getFile content (865x), 20% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.809 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.469 s [85% Category (117x)] (139x), ObjectMaps: 0.181 s [43% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 25% getLastPromoted (131x)] (536x)
2025-08-04 09:42:57,228 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 2.02 s [42% getDatedRevision (362x), 30% getDir2 content (133x), 24% getFile content (865x)] (1409x), RepositoryConfigService: 0.809 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.469 s [85% Category (117x)] (140x), ObjectMaps: 0.181 s [43% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 25% getLastPromoted (131x)] (536x)
