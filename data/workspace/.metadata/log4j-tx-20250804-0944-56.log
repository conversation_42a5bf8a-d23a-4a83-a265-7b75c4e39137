2025-08-04 09:45:01,280 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0391 s [60% update (144x), 40% query (12x)] (221x), svn: 0.0114 s [59% getLatestRevision (2x), 31% testConnection (1x)] (4x)
2025-08-04 09:45:01,377 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0251 s [59% getDir2 content (2x), 33% info (3x)] (6x)
2025-08-04 09:45:02,081 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.118 s, system: 0.217 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0757 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 09:45:02,081 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.204 s, system: 0.284 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 09:45:02,081 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.0587 s, system: 0.0909 s], Allocated memory: 7.5 MB, transactions: 0, ObjectMaps: 0.0459 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0377 s [82% log2 (5x)] (7x)
2025-08-04 09:45:02,081 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.243 s, system: 0.342 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 09:45:02,081 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.7 s, CPU [user: 0.107 s, system: 0.163 s], Allocated memory: 16.9 MB, transactions: 0, ObjectMaps: 0.0869 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0833 s [46% log2 (10x), 16% info (5x), 15% log (1x), 14% getLatestRevision (3x)] (24x)
2025-08-04 09:45:02,081 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.0625 s, system: 0.0978 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0684 s [84% log2 (10x)] (13x), ObjectMaps: 0.0542 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 09:45:02,081 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.502 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.255 s [63% log2 (36x), 16% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-08-04 09:45:02,287 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.173 s [100% getReadConfiguration (48x)] (48x), svn: 0.066 s [83% info (18x)] (38x)
2025-08-04 09:45:02,548 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.199 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.148 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 09:45:02,750 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.105 s, CPU [user: 0.0349 s, system: 0.00913 s], Allocated memory: 9.7 MB
2025-08-04 09:45:02,773 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.212 s [100% doFinishStartup (1x)] (1x), commit: 0.0605 s [100% Revision (1x)] (1x), Lucene: 0.0326 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0135 s [100% objectsToInv (1x)] (1x)
2025-08-04 09:45:04,893 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.329 s [90% info (158x)] (168x)
2025-08-04 09:45:05,164 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cb03d21044_0_661cb03d21044_0_: finished. Total: 0.263 s, CPU [user: 0.118 s, system: 0.0134 s], Allocated memory: 17.5 MB, resolve: 0.106 s [98% User (2x)] (4x), GC: 0.025 s [100% G1 Young Generation (1x)] (1x), ObjectMaps: 0.0229 s [81% getPrimaryObjectLocation (1x)] (6x), svn: 0.017 s [65% getLatestRevision (2x), 29% testConnection (1x)] (5x), Lucene: 0.0137 s [100% search (1x)] (1x)
2025-08-04 09:45:05,596 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.634 s, CPU [user: 0.0053 s, system: 0.00126 s], Allocated memory: 356.8 kB, transactions: 1
2025-08-04 09:45:05,596 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.275 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.12 s [97% User (3x)] (6x), ObjectMaps: 0.0266 s [84% getPrimaryObjectLocation (2x)] (7x), Incremental Baseline: 0.023 s [100% WorkItem (24x)] (24x), Lucene: 0.0182 s [75% search (1x), 25% refresh (1x)] (2x), svn: 0.017 s [65% getLatestRevision (2x), 29% testConnection (1x)] (5x)
2025-08-04 09:45:05,596 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.71 s, CPU [user: 0.134 s, system: 0.0223 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.536 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0686 s [82% buildBaselineSnapshots (1x)] (26x)
