2025-08-04 09:45:35,434 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0413 s [72% update (144x), 28% query (12x)] (221x), svn: 0.0116 s [63% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-08-04 09:45:35,529 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0254 s [62% getDir2 content (2x), 30% info (3x)] (6x)
2025-08-04 09:45:36,216 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.22 s, system: 0.35 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 09:45:36,217 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.183 s, system: 0.296 s], Allocated memory: 53.4 MB, transactions: 0, ObjectMaps: 0.101 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 09:45:36,217 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.0678 s, system: 0.123 s], Allocated memory: 9.5 MB, transactions: 0, ObjectMaps: 0.0682 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0676 s [30% log2 (5x), 28% info (5x), 16% log (1x), 12% getLatestRevision (2x)] (18x)
2025-08-04 09:45:36,216 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.062 s, system: 0.123 s], Allocated memory: 8.6 MB, transactions: 0, ObjectMaps: 0.0693 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0651 s [82% log2 (10x)] (13x)
2025-08-04 09:45:36,217 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.0828 s, system: 0.179 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.0655 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0486 s [74% log2 (10x), 16% getLatestRevision (2x)] (13x)
2025-08-04 09:45:36,217 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.685 s, CPU [user: 0.103 s, system: 0.233 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0658 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 09:45:36,217 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.49 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.241 s [61% log2 (36x), 14% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-04 09:45:36,453 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.202 s [100% getReadConfiguration (48x)] (48x), svn: 0.0716 s [80% info (18x)] (38x)
2025-08-04 09:45:36,812 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.291 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.213 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 09:45:37,025 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.109 s, CPU [user: 0.0378 s, system: 0.00844 s], Allocated memory: 10.0 MB
2025-08-04 09:45:37,064 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.236 s [100% doFinishStartup (1x)] (1x), commit: 0.0744 s [100% Revision (1x)] (1x), Lucene: 0.036 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.014 s [100% objectsToInv (1x)] (1x)
2025-08-04 09:45:39,337 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.363 s [92% info (158x)] (168x)
2025-08-04 09:45:39,537 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cb05ec4844_0_661cb05ec4844_0_: finished. Total: 0.19 s, CPU [user: 0.121 s, system: 0.0127 s], Allocated memory: 16.2 MB, resolve: 0.0535 s [95% User (2x)] (4x), Lucene: 0.0219 s [100% search (1x)] (1x), svn: 0.0106 s [58% getLatestRevision (2x), 34% testConnection (1x)] (5x)
2025-08-04 09:45:39,984 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.571 s, CPU [user: 0.00434 s, system: 0.0012 s], Allocated memory: 287.2 kB, transactions: 1
2025-08-04 09:45:39,984 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.569 s, CPU [user: 0.0028 s, system: 0.000867 s], Allocated memory: 206.2 kB, transactions: 1
2025-08-04 09:45:39,984 [DBHistoryCreator-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.57 s, CPU [user: 0.00316 s, system: 0.00108 s], Allocated memory: 226.6 kB, transactions: 1
2025-08-04 09:45:39,984 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 32, notification worker: 0.204 s [96% RevisionActivityCreator (2x)] (6x), resolve: 0.0667 s [93% User (3x)] (6x), Lucene: 0.0434 s [50% search (1x), 25% add (1x), 24% refresh (3x)] (5x), Incremental Baseline: 0.0288 s [100% WorkItem (24x)] (24x), persistence listener: 0.0229 s [79% indexRefreshPersistenceListener (1x), 10% WorkItemActivityCreator (1x)] (7x), ObjectMaps: 0.0122 s [66% getPrimaryObjectLocation (2x), 22% getPrimaryObjectProperty (1x)] (7x), svn: 0.0106 s [58% getLatestRevision (2x), 34% testConnection (1x)] (5x)
2025-08-04 09:45:39,984 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.657 s, CPU [user: 0.14 s, system: 0.0237 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.502 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0397 s [68% buildBaselineSnapshots (1x), 32% buildBaseline (25x)] (26x)
2025-08-04 09:45:40,293 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb05ec4440_0_661cb05ec4440_0_: finished. Total: 0.947 s, CPU [user: 0.333 s, system: 0.0931 s], Allocated memory: 51.6 MB, svn: 0.58 s [48% getDatedRevision (181x), 32% getDir2 content (25x)] (328x), resolve: 0.379 s [100% Category (117x)] (117x), ObjectMaps: 0.148 s [45% getPrimaryObjectProperty (117x), 32% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 09:45:40,534 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb05fcac48_0_661cb05fcac48_0_: finished. Total: 0.138 s, CPU [user: 0.0681 s, system: 0.0126 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0671 s [51% getReadConfiguration (180x), 49% getReadUserConfiguration (10x)] (190x), svn: 0.0617 s [58% info (21x), 36% getFile content (16x)] (39x), resolve: 0.0402 s [100% User (9x)] (9x), ObjectMaps: 0.0174 s [55% getPrimaryObjectProperty (8x), 24% getLastPromoted (8x), 21% getPrimaryObjectLocation (8x)] (32x)
2025-08-04 09:45:40,776 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb05ffc84a_0_661cb05ffc84a_0_: finished. Total: 0.182 s, CPU [user: 0.0611 s, system: 0.00617 s], Allocated memory: 19.8 MB, svn: 0.143 s [68% getDir2 content (17x), 32% getFile content (44x)] (62x), RepositoryConfigService: 0.068 s [99% getReadConfiguration (170x)] (192x)
2025-08-04 09:45:41,475 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb0602a04b_0_661cb0602a04b_0_: finished. Total: 0.699 s, CPU [user: 0.323 s, system: 0.0265 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.521 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.372 s [63% getFile content (412x), 37% getDir2 content (21x)] (434x)
2025-08-04 09:45:41,846 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb060f344e_0_661cb060f344e_0_: finished. Total: 0.264 s, CPU [user: 0.108 s, system: 0.00734 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.178 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.166 s [57% getFile content (185x), 43% getDir2 content (21x)] (207x)
2025-08-04 09:45:41,911 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.57 s, CPU [user: 1.01 s, system: 0.164 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.54 s [40% getDir2 content (133x), 36% getFile content (865x), 18% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.928 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.479 s [79% Category (117x), 12% Project (7x)] (139x), ObjectMaps: 0.187 s [49% getPrimaryObjectProperty (131x), 30% getPrimaryObjectLocation (137x), 21% getLastPromoted (131x)] (536x)
2025-08-04 09:45:41,912 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 2.05 s [38% getDatedRevision (362x), 30% getDir2 content (133x), 27% getFile content (865x)] (1409x), RepositoryConfigService: 0.928 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.48 s [79% Category (117x), 12% Project (7x)] (140x), ObjectMaps: 0.187 s [49% getPrimaryObjectProperty (131x), 30% getPrimaryObjectLocation (137x), 21% getLastPromoted (131x)] (536x)
