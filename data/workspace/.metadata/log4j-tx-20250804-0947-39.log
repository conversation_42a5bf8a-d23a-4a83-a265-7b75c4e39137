2025-08-04 09:47:43,916 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0396 s [64% update (144x), 35% query (12x)] (221x), svn: 0.0146 s [63% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-08-04 09:47:44,018 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0302 s [59% getDir2 content (2x), 32% info (3x)] (6x)
2025-08-04 09:47:44,692 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.672 s, CPU [user: 0.0661 s, system: 0.12 s], Allocated memory: 7.4 MB, transactions: 0, ObjectMaps: 0.0749 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 09:47:44,692 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.672 s, CPU [user: 0.115 s, system: 0.221 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0716 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 09:47:44,692 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.672 s, CPU [user: 0.195 s, system: 0.284 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.107 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 09:47:44,692 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.671 s, CPU [user: 0.0651 s, system: 0.11 s], Allocated memory: 8.6 MB, transactions: 0, ObjectMaps: 0.0678 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0614 s [81% log2 (10x)] (13x)
2025-08-04 09:47:44,692 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.671 s, CPU [user: 0.0916 s, system: 0.166 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.0733 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.05 s [71% log2 (10x), 19% getLatestRevision (2x)] (13x)
2025-08-04 09:47:44,692 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.671 s, CPU [user: 0.242 s, system: 0.342 s], Allocated memory: 70.8 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0624 s [27% log (1x), 25% log2 (5x), 23% info (5x), 13% getLatestRevision (2x)] (18x)
2025-08-04 09:47:44,693 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.513 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.233 s [60% log2 (36x), 14% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-04 09:47:44,910 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.188 s [100% getReadConfiguration (48x)] (48x), svn: 0.0678 s [88% info (18x)] (38x)
2025-08-04 09:47:45,195 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.226 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.166 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 09:47:45,393 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.101 s, CPU [user: 0.039 s, system: 0.00956 s], Allocated memory: 10.5 MB
2025-08-04 09:47:45,402 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.192 s [100% doFinishStartup (1x)] (1x), commit: 0.0478 s [100% Revision (1x)] (1x), Lucene: 0.0247 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0133 s [100% objectsToInv (1x)] (1x), DB: 0.0123 s [54% query (1x), 31% update (3x)] (8x)
2025-08-04 09:49:32,765 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.544 s [48% info (158x), 47% getLatestRevision (36x)] (204x), PullingJob: 0.256 s [100% collectChanges (35x)] (35x)
2025-08-04 09:49:33,010 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cb142bc445_0_661cb142bc445_0_: finished. Total: 0.224 s, CPU [user: 0.131 s, system: 0.0172 s], Allocated memory: 16.2 MB, resolve: 0.0697 s [98% User (2x)] (4x), ObjectMaps: 0.0161 s [49% getPrimaryObjectLocation (1x), 35% getPrimaryObjectProperty (1x)] (6x), Lucene: 0.0157 s [100% search (1x)] (1x), svn: 0.013 s [68% getLatestRevision (2x), 24% testConnection (1x)] (5x)
2025-08-04 09:49:33,543 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.696 s, CPU [user: 0.00659 s, system: 0.00178 s], Allocated memory: 356.8 kB, transactions: 1
2025-08-04 09:49:33,544 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.235 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.0821 s [97% User (3x)] (6x), Incremental Baseline: 0.0327 s [100% WorkItem (24x)] (24x), persistence listener: 0.0284 s [94% indexRefreshPersistenceListener (1x)] (7x), Lucene: 0.0216 s [73% search (1x), 27% refresh (1x)] (2x), ObjectMaps: 0.0187 s [56% getPrimaryObjectLocation (2x), 30% getPrimaryObjectProperty (1x)] (7x), svn: 0.0184 s [78% getLatestRevision (3x), 17% testConnection (1x)] (6x)
2025-08-04 09:49:33,545 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.779 s, CPU [user: 0.146 s, system: 0.029 s], Allocated memory: 19.3 MB, transactions: 26, svn: 0.625 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0406 s [70% buildBaselineSnapshots (1x), 30% buildBaseline (25x)] (26x)
2025-08-04 09:49:33,987 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb142b9440_0_661cb142b9440_0_: finished. Total: 1.21 s, CPU [user: 0.361 s, system: 0.103 s], Allocated memory: 51.2 MB, svn: 0.74 s [55% getDatedRevision (181x), 26% getDir2 content (25x)] (328x), resolve: 0.435 s [100% Category (117x)] (117x), ObjectMaps: 0.158 s [42% getPrimaryObjectProperty (117x), 36% getPrimaryObjectLocation (117x), 22% getLastPromoted (117x)] (473x)
2025-08-04 09:49:34,234 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb14406848_0_661cb14406848_0_: finished. Total: 0.128 s, CPU [user: 0.0592 s, system: 0.0109 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0633 s [56% getReadConfiguration (180x), 44% getReadUserConfiguration (10x)] (190x), svn: 0.0587 s [57% info (21x), 35% getFile content (16x)] (39x), resolve: 0.0384 s [100% User (9x)] (9x), ObjectMaps: 0.0187 s [53% getPrimaryObjectProperty (8x), 27% getPrimaryObjectLocation (8x)] (32x)
2025-08-04 09:49:34,492 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb1443444a_0_661cb1443444a_0_: finished. Total: 0.203 s, CPU [user: 0.0582 s, system: 0.00712 s], Allocated memory: 19.8 MB, svn: 0.166 s [76% getDir2 content (17x), 24% getFile content (44x)] (62x), RepositoryConfigService: 0.0587 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 09:49:35,528 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb1446744b_0_661cb1446744b_0_: finished. Total: 1.04 s, CPU [user: 0.411 s, system: 0.0268 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.831 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.548 s [73% getFile content (412x), 27% getDir2 content (21x)] (434x)
2025-08-04 09:49:35,871 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb1458784e_0_661cb1458784e_0_: finished. Total: 0.225 s, CPU [user: 0.0886 s, system: 0.00605 s], Allocated memory: 384.4 MB, svn: 0.146 s [53% getFile content (185x), 47% getDir2 content (21x)] (207x), RepositoryConfigService: 0.143 s [96% getReadConfiguration (2787x)] (3025x)
2025-08-04 09:49:35,936 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.17 s, CPU [user: 1.1 s, system: 0.171 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.9 s [39% getFile content (865x), 36% getDir2 content (133x), 22% getDatedRevision (181x)] (1224x), RepositoryConfigService: 1.19 s [95% getReadConfiguration (12165x)] (12859x), resolve: 0.542 s [80% Category (117x)] (139x), ObjectMaps: 0.201 s [47% getPrimaryObjectProperty (131x), 33% getPrimaryObjectLocation (137x)] (536x)
2025-08-04 09:49:35,936 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 2.52 s [41% getDatedRevision (362x), 29% getFile content (865x), 27% getDir2 content (133x)] (1407x), RepositoryConfigService: 1.19 s [95% getReadConfiguration (12165x)] (12859x), resolve: 0.542 s [80% Category (117x)] (140x), ObjectMaps: 0.201 s [47% getPrimaryObjectProperty (131x), 33% getPrimaryObjectLocation (137x)] (536x)
2025-08-04 09:49:49,538 [ajp-nio-127.0.0.1-8889-exec-8 | cID:72c5337c-c0a844bd-54793fdd-052f7ce8] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=default-project&_t=1754272183151': Total: 6.37 s, CPU [user: 0.0969 s, system: 0.0167 s], Allocated memory: 5.0 MB, transactions: 0
2025-08-04 09:49:50,144 [ajp-nio-127.0.0.1-8889-exec-7 | cID:72c5337c-c0a844bd-54793fdd-75f471b1] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=default-project&_t=1754272183151': Total: 6.98 s, CPU [user: 0.0275 s, system: 0.00722 s], Allocated memory: 1.6 MB, transactions: 0
2025-08-04 09:56:45,137 [ajp-nio-127.0.0.1-8889-exec-9 | cID:72cb2c39-c0a844bd-54793fdd-95d70393] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=default-project&_t=1754272574388': Total: 30.6 s, CPU [user: 0.41 s, system: 0.0129 s], Allocated memory: 572.2 kB, transactions: 0
2025-08-04 09:56:46,025 [ajp-nio-127.0.0.1-8889-exec-10 | cID:72cb2c39-c0a844bd-54793fdd-e7eef8fb] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=default-project&_t=1754272574388': Total: 31.5 s, CPU [user: 0.00826 s, system: 0.00769 s], Allocated memory: 373.4 kB, transactions: 0
