2025-08-04 10:02:41,105 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0414 s [64% update (144x), 36% query (12x)] (221x), svn: 0.0143 s [69% getLatestRevision (2x), 23% testConnection (1x)] (4x)
2025-08-04 10:02:41,220 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0276 s [62% getDir2 content (2x), 32% info (3x)] (6x)
2025-08-04 10:02:41,935 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.0635 s, system: 0.0883 s], Allocated memory: 7.5 MB, transactions: 0, ObjectMaps: 0.0569 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 10:02:41,935 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.264 s, system: 0.337 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.139 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 10:02:41,935 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.211 s, system: 0.267 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.114 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 10:02:41,935 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.132 s, system: 0.202 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0828 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 10:02:41,935 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.077 s, system: 0.0943 s], Allocated memory: 8.9 MB, transactions: 0, svn: 0.0786 s [85% log2 (10x)] (13x), ObjectMaps: 0.0451 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 10:02:41,935 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.11 s, system: 0.15 s], Allocated memory: 16.7 MB, transactions: 0, svn: 0.0899 s [38% log2 (10x), 22% getLatestRevision (3x), 16% info (5x), 14% log (1x)] (24x), ObjectMaps: 0.0693 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 10:02:41,936 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.508 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.258 s [61% log2 (36x), 17% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-04 10:02:42,162 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.189 s [100% getReadConfiguration (48x)] (48x), svn: 0.0655 s [85% info (18x)] (38x)
2025-08-04 10:02:42,555 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.312 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.222 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 10:02:42,778 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.122 s, CPU [user: 0.0256 s, system: 0.00773 s], Allocated memory: 8.5 MB, GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 10:02:42,790 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 27, PersistenceEngineListener: 0.218 s [100% doFinishStartup (1x)] (1x), commit: 0.0574 s [100% Revision (1x)] (1x), Lucene: 0.0308 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0136 s [100% objectsToInv (1x)] (1x)
2025-08-04 10:02:45,071 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.305 s [89% info (158x)] (168x)
2025-08-04 10:02:45,341 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cb44875844_0_661cb44875844_0_: finished. Total: 0.262 s, CPU [user: 0.142 s, system: 0.0157 s], Allocated memory: 17.5 MB, resolve: 0.0607 s [98% User (2x)] (4x), hasLinkedResourcesQueryExpander: 0.0398 s [100% expand (1x)] (2x), Lucene: 0.0227 s [100% search (1x)] (1x), GC: 0.02 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 10:02:45,874 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.697 s, CPU [user: 0.00696 s, system: 0.00151 s], Allocated memory: 356.1 kB, transactions: 1
2025-08-04 10:02:45,875 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.275 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.0745 s [96% User (3x)] (6x), Lucene: 0.0489 s [46% search (1x), 32% add (1x), 22% refresh (1x)] (3x), hasLinkedResourcesQueryExpander: 0.0398 s [100% expand (1x)] (2x), Incremental Baseline: 0.0302 s [100% WorkItem (24x)] (24x), svn: 0.0159 s [50% getLatestRevision (3x), 44% testConnection (2x)] (7x), persistence listener: 0.0154 s [81% indexRefreshPersistenceListener (1x)] (7x)
2025-08-04 10:02:45,876 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.809 s, CPU [user: 0.153 s, system: 0.024 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.601 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0449 s [67% buildBaselineSnapshots (1x), 33% buildBaseline (25x)] (26x)
2025-08-04 10:02:46,193 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb44875c45_0_661cb44875c45_0_: finished. Total: 1.11 s, CPU [user: 0.327 s, system: 0.0893 s], Allocated memory: 51.1 MB, svn: 0.655 s [45% getDatedRevision (181x), 38% getDir2 content (25x)] (328x), resolve: 0.361 s [100% Category (117x)] (117x), ObjectMaps: 0.139 s [40% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 25% getLastPromoted (117x)] (473x)
2025-08-04 10:02:46,381 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb449a0048_0_661cb449a0048_0_: finished. Total: 0.108 s, CPU [user: 0.0541 s, system: 0.00859 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0497 s [53% getReadUserConfiguration (10x), 47% getReadConfiguration (180x)] (190x), svn: 0.0434 s [59% info (21x), 35% getFile content (16x)] (39x), resolve: 0.0334 s [100% User (9x)] (9x), ObjectMaps: 0.0179 s [41% getPrimaryObjectProperty (8x), 39% getLastPromoted (8x), 21% getPrimaryObjectLocation (8x)] (32x)
2025-08-04 10:02:46,618 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb449c484a_0_661cb449c484a_0_: finished. Total: 0.199 s, CPU [user: 0.071 s, system: 0.00924 s], Allocated memory: 19.6 MB, svn: 0.144 s [64% getDir2 content (17x), 36% getFile content (44x)] (62x), RepositoryConfigService: 0.0875 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 10:02:47,357 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb449f684b_0_661cb449f684b_0_: finished. Total: 0.739 s, CPU [user: 0.359 s, system: 0.0175 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.546 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.388 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-08-04 10:02:47,770 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb44ace84e_0_661cb44ace84e_0_: finished. Total: 0.287 s, CPU [user: 0.125 s, system: 0.00753 s], Allocated memory: 389.0 MB, RepositoryConfigService: 0.196 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.173 s [58% getFile content (185x), 42% getDir2 content (21x)] (207x)
2025-08-04 10:02:47,835 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.76 s, CPU [user: 1.05 s, system: 0.146 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.62 s [42% getDir2 content (133x), 36% getFile content (865x), 18% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.964 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.438 s [82% Category (117x)] (139x), ObjectMaps: 0.171 s [43% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 25% getLastPromoted (131x)] (536x)
2025-08-04 10:02:47,835 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 2.22 s [40% getDatedRevision (362x), 31% getDir2 content (133x), 26% getFile content (865x)] (1407x), RepositoryConfigService: 0.964 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.438 s [82% Category (117x)] (140x), ObjectMaps: 0.171 s [43% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 25% getLastPromoted (131x)] (536x)
2025-08-04 10:03:56,963 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 2.09 s, CPU [user: 0.00128 s, system: 0.000525 s], Allocated memory: 140.5 kB, transactions: 0, PullingJob: 2.09 s [100% collectChanges (1x)] (1x)
2025-08-04 10:04:10,634 [ajp-nio-127.0.0.1-8889-exec-2 | cID:72d21a88-7f000001-417a303b-817dd6b7] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754273028710': Total: 21.9 s, CPU [user: 2.7 s, system: 0.0359 s], Allocated memory: 13.3 MB, transactions: 0, svn: 3.96 s [100% testConnection (1x)] (1x)
2025-08-04 10:04:13,432 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.538 s, CPU [user: 0.00108 s, system: 0.00018 s], Allocated memory: 130.3 kB, transactions: 0, PullingJob: 0.537 s [100% collectChanges (1x)] (1x)
2025-08-04 10:04:13,433 [ajp-nio-127.0.0.1-8889-exec-3 | cID:72d21a88-7f000001-417a303b-a0710562] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754273028710': Total: 24.7 s, CPU [user: 0.031 s, system: 0.0154 s], Allocated memory: 2.7 MB, transactions: 0, svn: 1.53 s [100% testConnection (1x)] (1x)
