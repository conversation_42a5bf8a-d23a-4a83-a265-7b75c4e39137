2025-08-04 10:07:36,950 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0447 s [59% update (144x), 41% query (12x)] (221x), svn: 0.0125 s [53% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-08-04 10:07:37,056 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0271 s [59% getDir2 content (2x), 33% info (3x)] (6x)
2025-08-04 10:07:37,800 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.74 s, CPU [user: 0.109 s, system: 0.253 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0918 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 10:07:37,800 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.741 s, CPU [user: 0.0574 s, system: 0.11 s], Allocated memory: 7.4 MB, transactions: 0, ObjectMaps: 0.0732 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 10:07:37,800 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.74 s, CPU [user: 0.231 s, system: 0.374 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 10:07:37,800 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.74 s, CPU [user: 0.204 s, system: 0.318 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.116 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 10:07:37,800 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.74 s, CPU [user: 0.0603 s, system: 0.114 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0646 s [81% log2 (10x)] (13x), ObjectMaps: 0.0571 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 10:07:37,800 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.74 s, CPU [user: 0.115 s, system: 0.199 s], Allocated memory: 16.6 MB, transactions: 0, svn: 0.101 s [39% log2 (10x), 21% info (5x), 16% log (1x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.0994 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 10:07:37,800 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.558 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.245 s [59% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-04 10:07:38,029 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.197 s [100% getReadConfiguration (48x)] (48x), svn: 0.0611 s [86% info (18x)] (38x)
2025-08-04 10:07:38,300 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.212 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.16 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 10:07:38,507 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.111 s, CPU [user: 0.0406 s, system: 0.0111 s], Allocated memory: 9.7 MB
2025-08-04 10:07:38,518 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.205 s [100% doFinishStartup (1x)] (1x), commit: 0.0619 s [100% Revision (1x)] (1x), Lucene: 0.0263 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0145 s [100% objectsToInv (1x)] (1x)
2025-08-04 10:07:40,751 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.345 s [90% info (158x)] (168x)
2025-08-04 10:07:41,020 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cb56937443_0_661cb56937443_0_: finished. Total: 0.254 s, CPU [user: 0.145 s, system: 0.0145 s], Allocated memory: 16.2 MB, resolve: 0.0742 s [98% User (2x)] (4x), Lucene: 0.0199 s [100% search (1x)] (1x), svn: 0.0138 s [46% testConnection (1x), 46% getLatestRevision (2x)] (5x), planQueryExpander: 0.0134 s [100% expand (1x)] (2x)
2025-08-04 10:07:41,526 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.673 s, CPU [user: 0.00673 s, system: 0.00155 s], Allocated memory: 356.2 kB, transactions: 1
2025-08-04 10:07:41,526 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.271 s [96% RevisionActivityCreator (2x)] (6x), resolve: 0.0872 s [96% User (3x)] (6x), Lucene: 0.043 s [46% search (1x), 35% add (1x)] (3x), Incremental Baseline: 0.0309 s [100% WorkItem (24x)] (24x), persistence listener: 0.0234 s [78% indexRefreshPersistenceListener (1x), 12% WorkItemActivityCreator (1x)] (7x), ObjectMaps: 0.0154 s [68% getPrimaryObjectLocation (2x), 16% getLastPromoted (1x)] (7x), svn: 0.0138 s [46% testConnection (1x), 46% getLatestRevision (2x)] (5x)
2025-08-04 10:07:41,527 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.78 s, CPU [user: 0.148 s, system: 0.0241 s], Allocated memory: 18.8 MB, transactions: 25, svn: 0.603 s [99% getDatedRevision (181x)] (183x)
2025-08-04 10:07:41,962 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb56936440_0_661cb56936440_0_: finished. Total: 1.2 s, CPU [user: 0.372 s, system: 0.0981 s], Allocated memory: 51.6 MB, svn: 0.758 s [52% getDatedRevision (181x), 30% getDir2 content (25x)] (328x), resolve: 0.405 s [100% Category (117x)] (117x), ObjectMaps: 0.15 s [44% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 22% getLastPromoted (117x)] (473x)
2025-08-04 10:07:42,146 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb56a74448_0_661cb56a74448_0_: finished. Total: 0.112 s, CPU [user: 0.0587 s, system: 0.0091 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0528 s [51% getReadConfiguration (180x), 49% getReadUserConfiguration (10x)] (190x), svn: 0.0492 s [58% info (21x), 35% getFile content (16x)] (39x), resolve: 0.0319 s [100% User (9x)] (9x), ObjectMaps: 0.0147 s [58% getPrimaryObjectProperty (8x), 23% getLastPromoted (8x)] (32x)
2025-08-04 10:07:42,317 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb56a98c4a_0_661cb56a98c4a_0_: finished. Total: 0.138 s, CPU [user: 0.0529 s, system: 0.00447 s], Allocated memory: 19.7 MB, svn: 0.101 s [68% getDir2 content (17x), 32% getFile content (44x)] (62x), RepositoryConfigService: 0.0523 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 10:07:42,996 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb56abb44b_0_661cb56abb44b_0_: finished. Total: 0.678 s, CPU [user: 0.321 s, system: 0.0248 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.488 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.365 s [60% getFile content (412x), 40% getDir2 content (21x)] (434x)
2025-08-04 10:07:43,395 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb56b7e44e_0_661cb56b7e44e_0_: finished. Total: 0.297 s, CPU [user: 0.126 s, system: 0.00781 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.198 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.183 s [55% getFile content (185x), 45% getDir2 content (21x)] (207x)
2025-08-04 10:07:43,470 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.71 s, CPU [user: 1.03 s, system: 0.157 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.65 s [40% getDir2 content (133x), 33% getFile content (865x), 24% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.872 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.473 s [86% Category (117x)] (139x), ObjectMaps: 0.176 s [47% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-04 10:07:43,470 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 2.26 s [44% getDatedRevision (362x), 29% getDir2 content (133x), 24% getFile content (865x)] (1409x), RepositoryConfigService: 0.872 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.473 s [86% Category (117x)] (140x), ObjectMaps: 0.176 s [47% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-04 10:07:51,661 [ajp-nio-127.0.0.1-8889-exec-2 | cID:72d5ce7b-7f000001-39c1b088-8f23d1cd] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/health': Total: 0.242 s, CPU [user: 0.157 s, system: 0.0207 s], Allocated memory: 37.6 MB, transactions: 0, PolarionAuthenticator: 0.241 s [100% authenticate (1x)] (1x)
