2025-08-04 10:11:16,993 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0445 s [65% update (144x), 35% query (12x)] (221x), svn: 0.0124 s [65% getLatestRevision (2x), 26% testConnection (1x)] (4x)
2025-08-04 10:11:17,101 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0303 s [58% getDir2 content (2x), 34% info (3x)] (6x)
2025-08-04 10:11:17,784 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.679 s, CPU [user: 0.185 s, system: 0.27 s], Allocated memory: 53.3 MB, transactions: 0, ObjectMaps: 0.107 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 10:11:17,784 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.679 s, CPU [user: 0.218 s, system: 0.331 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 10:11:17,784 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.68 s, CPU [user: 0.056 s, system: 0.0839 s], Allocated memory: 7.2 MB, transactions: 0, ObjectMaps: 0.0527 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0357 s [72% log2 (5x), 14% getLatestRevision (1x)] (7x)
2025-08-04 10:11:17,784 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.679 s, CPU [user: 0.0697 s, system: 0.0998 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0882 s [81% log2 (10x)] (13x), ObjectMaps: 0.0629 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 10:11:17,784 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.679 s, CPU [user: 0.104 s, system: 0.214 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0741 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 10:11:17,784 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.679 s, CPU [user: 0.099 s, system: 0.162 s], Allocated memory: 16.6 MB, transactions: 0, svn: 0.104 s [39% log2 (10x), 20% getLatestRevision (3x), 18% info (5x), 15% log (1x)] (24x), ObjectMaps: 0.0804 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 10:11:17,785 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.496 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.289 s [61% log2 (36x), 16% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-04 10:11:17,980 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.166 s [100% getReadConfiguration (48x)] (48x), svn: 0.0534 s [89% info (18x)] (38x)
2025-08-04 10:11:18,227 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.188 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.144 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 10:11:18,431 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.106 s, CPU [user: 0.0247 s, system: 0.00962 s], Allocated memory: 8.6 MB
2025-08-04 10:11:18,442 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.2 s [100% doFinishStartup (1x)] (1x), commit: 0.0527 s [100% Revision (1x)] (1x), Lucene: 0.0321 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0133 s [100% objectsToInv (1x)] (1x)
2025-08-04 10:11:20,589 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.264 s [90% info (158x)] (168x)
2025-08-04 10:11:20,764 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cb63fe6045_0_661cb63fe6045_0_: finished. Total: 0.163 s, CPU [user: 0.108 s, system: 0.0113 s], Allocated memory: 16.2 MB, resolve: 0.0448 s [95% User (2x)] (4x), Lucene: 0.0127 s [100% search (1x)] (1x), hasLinkedResourcesQueryExpander: 0.0115 s [100% expand (1x)] (2x)
2025-08-04 10:11:21,238 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.584 s, CPU [user: 0.00588 s, system: 0.0016 s], Allocated memory: 356.8 kB, transactions: 1
2025-08-04 10:11:21,239 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.172 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.0569 s [93% User (3x)] (6x), Incremental Baseline: 0.0297 s [100% WorkItem (24x)] (24x), persistence listener: 0.0184 s [82% indexRefreshPersistenceListener (1x)] (7x), Lucene: 0.0174 s [73% search (1x), 27% refresh (1x)] (2x), hasLinkedResourcesQueryExpander: 0.0115 s [100% expand (1x)] (2x), ObjectMaps: 0.0112 s [66% getPrimaryObjectLocation (2x), 23% getPrimaryObjectProperty (1x)] (7x), svn: 0.00994 s [60% getLatestRevision (2x), 30% testConnection (1x)] (5x)
2025-08-04 10:11:21,239 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.651 s, CPU [user: 0.133 s, system: 0.0203 s], Allocated memory: 18.8 MB, transactions: 25, svn: 0.519 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0386 s [76% buildBaselineSnapshots (1x), 24% buildBaseline (25x)] (26x)
2025-08-04 10:11:21,505 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb63fe4040_0_661cb63fe4040_0_: finished. Total: 0.912 s, CPU [user: 0.284 s, system: 0.0798 s], Allocated memory: 51.2 MB, svn: 0.532 s [46% getDatedRevision (181x), 34% getDir2 content (25x), 19% getFile content (119x)] (328x), resolve: 0.323 s [100% Category (117x)] (117x), ObjectMaps: 0.121 s [44% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 10:11:21,672 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb640d8c48_0_661cb640d8c48_0_: finished. Total: 0.1 s, CPU [user: 0.0501 s, system: 0.00803 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0485 s [53% getReadConfiguration (180x), 47% getReadUserConfiguration (10x)] (190x), svn: 0.043 s [59% info (21x), 36% getFile content (16x)] (39x), resolve: 0.0281 s [100% User (9x)] (9x), ObjectMaps: 0.0121 s [54% getPrimaryObjectProperty (8x), 27% getLastPromoted (8x)] (32x)
2025-08-04 10:11:21,826 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb640fac4a_0_661cb640fac4a_0_: finished. Total: 0.119 s, CPU [user: 0.0416 s, system: 0.00378 s], Allocated memory: 19.7 MB, svn: 0.0942 s [79% getDir2 content (17x), 21% getFile content (44x)] (62x), RepositoryConfigService: 0.0322 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 10:11:22,424 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb6411884b_0_661cb6411884b_0_: finished. Total: 0.598 s, CPU [user: 0.31 s, system: 0.0124 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.455 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.296 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-08-04 10:11:22,749 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cb641c644e_0_661cb641c644e_0_: finished. Total: 0.228 s, CPU [user: 0.0957 s, system: 0.00552 s], Allocated memory: 386.8 MB, svn: 0.145 s [51% getDir2 content (21x), 49% getFile content (185x)] (207x), RepositoryConfigService: 0.141 s [96% getReadConfiguration (2787x)] (3025x)
2025-08-04 10:11:22,830 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.24 s, CPU [user: 0.882 s, system: 0.122 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.31 s [43% getDir2 content (133x), 34% getFile content (865x), 19% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.753 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.391 s [83% Category (117x)] (139x), ObjectMaps: 0.146 s [47% getPrimaryObjectProperty (131x), 30% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-04 10:11:22,830 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 1.83 s [41% getDatedRevision (362x), 31% getDir2 content (133x), 25% getFile content (865x)] (1409x), RepositoryConfigService: 0.753 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.392 s [83% Category (117x)] (140x), ObjectMaps: 0.146 s [47% getPrimaryObjectProperty (131x), 30% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-04 10:11:41,361 [ajp-nio-127.0.0.1-8889-exec-5 | cID:72d92a33-7f000001-6ab8b279-86d7fb6f] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754273486112': Total: 9.85 s, CPU [user: 0.0539 s, system: 0.0185 s], Allocated memory: 3.4 MB, transactions: 2
2025-08-04 10:11:42,896 [ajp-nio-127.0.0.1-8889-exec-4 | cID:72d92a32-7f000001-6ab8b279-69dacd7f] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754273486112': Total: 11.4 s, CPU [user: 0.039 s, system: 0.012 s], Allocated memory: 2.7 MB, transactions: 2
2025-08-04 10:25:38,756 [ajp-nio-127.0.0.1-8889-exec-9 | cID:72e54e77-7f000001-6ab8b279-270fbd06] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754274287151': Total: 51.5 s, CPU [user: 0.0073 s, system: 0.0272 s], Allocated memory: 286.6 kB, transactions: 0
2025-08-04 10:25:41,762 [ajp-nio-127.0.0.1-8889-exec-1 | cID:72e5fe7e-7f000001-6ab8b279-f28ad143] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754274332251': Total: 9.48 s, CPU [user: 0.00488 s, system: 0.00806 s], Allocated memory: 287.8 kB, transactions: 0
2025-08-04 10:25:52,922 [ajp-nio-127.0.0.1-8889-exec-10 | cID:72e5fe7b-7f000001-6ab8b279-5b0abe40] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754274332250': Total: 20.6 s, CPU [user: 0.00621 s, system: 0.0114 s], Allocated memory: 286.2 kB, transactions: 0
2025-08-04 10:25:53,480 [ajp-nio-127.0.0.1-8889-exec-3 | cID:72e64629-7f000001-6ab8b279-8ac24b12] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754274350621': Total: 2.85 s, CPU [user: 0.00507 s, system: 0.00295 s], Allocated memory: 285.6 kB, transactions: 0
