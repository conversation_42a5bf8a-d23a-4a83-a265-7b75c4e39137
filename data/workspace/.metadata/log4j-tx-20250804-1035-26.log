2025-08-04 10:35:32,263 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0855 s [71% update (144x), 29% query (12x)] (221x), svn: 0.0134 s [61% getLatestRevision (2x), 31% testConnection (1x)] (4x)
2025-08-04 10:35:32,379 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0387 s [49% info (3x), 44% getDir2 content (2x)] (6x)
2025-08-04 10:35:33,151 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.768 s, CPU [user: 0.062 s, system: 0.104 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0818 s [85% log2 (10x)] (13x), ObjectMaps: 0.0614 s [96% getAllPrimaryObjects (2x)] (14x)
2025-08-04 10:35:33,151 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.768 s, CPU [user: 0.234 s, system: 0.372 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.142 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 10:35:33,151 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.768 s, CPU [user: 0.0975 s, system: 0.187 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.0934 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0793 s [58% log2 (10x), 35% getLatestRevision (2x)] (13x)
2025-08-04 10:35:33,151 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.2 s, system: 0.308 s], Allocated memory: 53.4 MB, transactions: 0, ObjectMaps: 0.112 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 10:35:33,151 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.114 s, system: 0.242 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0847 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0407 s [47% log2 (5x), 40% testConnection (1x)] (7x)
2025-08-04 10:35:33,151 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.0778 s, system: 0.106 s], Allocated memory: 9.5 MB, transactions: 0, svn: 0.0767 s [31% log2 (5x), 21% info (5x), 21% log (1x), 12% getLatestRevision (2x)] (18x), ObjectMaps: 0.0683 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 10:35:33,152 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.562 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.319 s [57% log2 (36x), 18% getLatestRevision (9x), 14% testConnection (6x)] (61x)
2025-08-04 10:35:33,315 [main | u:p] INFO  TXLOGGER - Tx 661cbbca75c01_0_661cbbca75c01_0_: finished. Total: 0.105 s, CPU [user: 0.0784 s, system: 0.00385 s], Allocated memory: 21.8 MB
2025-08-04 10:35:33,438 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.22 s [100% getReadConfiguration (48x)] (48x), svn: 0.0681 s [86% info (18x)] (38x)
2025-08-04 10:35:33,728 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.227 s [73% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.174 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 10:35:33,948 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.111 s, CPU [user: 0.0231 s, system: 0.00622 s], Allocated memory: 8.6 MB, GC: 0.012 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 10:35:33,974 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.233 s [100% doFinishStartup (1x)] (1x), commit: 0.0586 s [100% Revision (1x)] (1x), Lucene: 0.0368 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0165 s [100% objectsToInv (1x)] (1x)
2025-08-04 10:35:36,163 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.355 s [89% info (158x)] (168x)
2025-08-04 10:35:36,169 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 2, persistence listener: 0.00919 s [79% indexRefreshPersistenceListener (1x), 13% WorkItemActivityCreator (1x)] (7x), notification worker: 0.00166 s [45% PlanActivityCreator (1x), 40% TestRunActivityCreator (1x)] (3x), EHCache: 0.000465 s [100% GET (7x)] (7x)
2025-08-04 10:35:36,392 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cbbcd59c44_0_661cbbcd59c44_0_: finished. Total: 0.224 s, CPU [user: 0.124 s, system: 0.0146 s], Allocated memory: 16.2 MB, resolve: 0.0539 s [98% User (2x)] (4x), Lucene: 0.0132 s [100% search (1x)] (1x)
2025-08-04 10:35:36,852 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.613 s, CPU [user: 0.00618 s, system: 0.00133 s], Allocated memory: 356.9 kB, transactions: 1
2025-08-04 10:35:36,852 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.232 s [99% RevisionActivityCreator (2x)] (3x), resolve: 0.0695 s [95% User (3x)] (6x), Incremental Baseline: 0.0244 s [100% WorkItem (24x)] (24x), Lucene: 0.018 s [74% search (1x), 26% refresh (1x)] (2x), ObjectMaps: 0.0138 s [72% getPrimaryObjectLocation (2x), 20% getPrimaryObjectProperty (1x)] (7x)
2025-08-04 10:35:36,853 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.693 s, CPU [user: 0.129 s, system: 0.0207 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.546 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0349 s [68% buildBaselineSnapshots (1x), 32% buildBaseline (25x)] (26x)
2025-08-04 10:35:37,181 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cbbcd5a445_0_661cbbcd5a445_0_: finished. Total: 1.01 s, CPU [user: 0.296 s, system: 0.0826 s], Allocated memory: 51.6 MB, svn: 0.599 s [50% getDatedRevision (181x), 32% getDir2 content (25x)] (328x), resolve: 0.321 s [100% Category (117x)] (117x), ObjectMaps: 0.116 s [43% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 24% getLastPromoted (117x)] (473x)
2025-08-04 10:35:37,373 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cbbce5fc47_0_661cbbce5fc47_0_: finished. Total: 0.158 s, CPU [user: 0.0359 s, system: 0.00969 s], Allocated memory: 3.8 MB, resolve: 0.144 s [100% Project (6x)] (6x), svn: 0.0565 s [48% log (3x), 30% getFile content (6x), 22% info (6x)] (16x), ObjectMaps: 0.0468 s [69% getPrimaryObjectProperty (6x), 20% getLastPromoted (6x)] (25x)
2025-08-04 10:35:37,562 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cbbce87448_0_661cbbce87448_0_: finished. Total: 0.188 s, CPU [user: 0.0732 s, system: 0.0175 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.108 s [55% getReadUserConfiguration (10x), 45% getReadConfiguration (180x)] (190x), svn: 0.0799 s [69% info (21x), 27% getFile content (16x)] (39x), resolve: 0.0421 s [100% User (9x)] (9x), GC: 0.021 s [100% G1 Young Generation (1x)] (1x), ObjectMaps: 0.0179 s [56% getPrimaryObjectProperty (8x), 26% getLastPromoted (8x)] (32x)
2025-08-04 10:35:37,815 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cbbcec104a_0_661cbbcec104a_0_: finished. Total: 0.211 s, CPU [user: 0.0672 s, system: 0.00996 s], Allocated memory: 19.8 MB, svn: 0.157 s [67% getDir2 content (17x), 33% getFile content (44x)] (62x), RepositoryConfigService: 0.0865 s [99% getReadConfiguration (170x)] (192x)
2025-08-04 10:35:38,549 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cbbcef5c4b_0_661cbbcef5c4b_0_: finished. Total: 0.734 s, CPU [user: 0.326 s, system: 0.0346 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.538 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.39 s [62% getFile content (412x), 38% getDir2 content (21x)] (434x)
2025-08-04 10:35:38,932 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cbbcfca84e_0_661cbbcfca84e_0_: finished. Total: 0.266 s, CPU [user: 0.11 s, system: 0.00712 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.171 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.164 s [53% getFile content (185x), 47% getDir2 content (21x)] (207x)
2025-08-04 10:35:38,992 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.83 s, CPU [user: 0.986 s, system: 0.172 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.64 s [41% getDir2 content (133x), 34% getFile content (865x), 18% getDatedRevision (181x)] (1224x), RepositoryConfigService: 0.983 s [91% getReadConfiguration (12165x)] (12859x), resolve: 0.509 s [63% Category (117x), 28% Project (7x)] (139x), ObjectMaps: 0.181 s [51% getPrimaryObjectProperty (131x), 26% getPrimaryObjectLocation (137x), 23% getLastPromoted (131x)] (536x)
2025-08-04 10:35:38,993 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 2.19 s [38% getDatedRevision (362x), 31% getDir2 content (133x), 26% getFile content (865x)] (1409x), RepositoryConfigService: 0.983 s [91% getReadConfiguration (12165x)] (12859x), resolve: 0.509 s [63% Category (117x), 28% Project (7x)] (140x), ObjectMaps: 0.181 s [51% getPrimaryObjectProperty (131x), 26% getPrimaryObjectLocation (137x), 23% getLastPromoted (131x)] (536x)
2025-08-04 10:38:31,120 [ajp-nio-127.0.0.1-8889-exec-2 | cID:72f1df9e-7f000001-2d7345d0-33ed4648] INFO  TXLOGGER - Summary for 'servlet /polarion/': Total: 0.305 s, CPU [user: 0.142 s, system: 0.0614 s], Allocated memory: 34.4 MB, transactions: 0, PolarionAuthenticator: 0.289 s [100% authenticate (1x)] (1x)
2025-08-04 10:38:34,104 [ajp-nio-127.0.0.1-8889-exec-4 | cID:72f1ec11-7f000001-2d7345d0-ea87f25b] INFO  TXLOGGER - Summary for 'servlet /polarion/j_security_check': Total: 0.102 s, CPU [user: 0.0318 s, system: 0.0132 s], Allocated memory: 1.9 MB, transactions: 2, PolarionAuthenticator: 0.101 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0221 s [100% getReadConfiguration (1x)] (1x), svn: 0.0151 s [82% testConnection (1x)] (3x)
2025-08-04 10:38:35,223 [ajp-nio-127.0.0.1-8889-exec-4 | cID:72f1f00d-7f000001-2d7345d0-a0c4359c] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.201 s, CPU [user: 0.126 s, system: 0.0267 s], Allocated memory: 66.6 MB, transactions: 1, RPC: 0.117 s [41% decodeRequest (1x), 36% encodeResponse (1x), 23% writeResponse (1x)] (4x), PortalDataService: 0.0607 s [100% getInitData (1x)] (1x), RepositoryConfigService: 0.0265 s [100% getReadConfiguration (10x)] (15x), svn: 0.0243 s [50% info (1x), 50% getDir2 content (1x)] (3x)
2025-08-04 10:38:36,427 [ajp-nio-127.0.0.1-8889-exec-1 | cID:72f1f4c7-7f000001-2d7345d0-003a76cc | u:admin] INFO  TXLOGGER - Tx 661cbc7d34453_0_661cbc7d34453_0_: finished. Total: 0.185 s, CPU [user: 0.0718 s, system: 0.0189 s], Allocated memory: 15.9 MB, svn: 0.065 s [40% info (7x), 31% getFile content (4x), 21% getDir2 content (2x)] (15x), RepositoryConfigService: 0.0436 s [92% getReadConfiguration (42x)] (81x), resolve: 0.0393 s [97% RichPage (3x)] (8x), ObjectMaps: 0.0116 s [31% getPrimaryObjectLocation (5x), 30% getLastPromoted (1x), 20% getPrimaryObjectProperty (1x)] (9x)
2025-08-04 10:38:36,435 [ajp-nio-127.0.0.1-8889-exec-1 | cID:72f1f4c7-7f000001-2d7345d0-003a76cc] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.203 s, CPU [user: 0.085 s, system: 0.0227 s], Allocated memory: 18.7 MB, transactions: 1, PortalDataService: 0.186 s [100% requestPortalSite (1x)] (1x), svn: 0.065 s [40% info (7x), 31% getFile content (4x), 21% getDir2 content (2x)] (15x), RepositoryConfigService: 0.0436 s [92% getReadConfiguration (42x)] (81x), resolve: 0.0393 s [97% RichPage (3x)] (8x), RPC: 0.0155 s [50% decodeRequest (1x), 45% encodeResponse (1x)] (4x), ObjectMaps: 0.0116 s [31% getPrimaryObjectLocation (5x), 30% getLastPromoted (1x), 20% getPrimaryObjectProperty (1x)] (9x)
2025-08-04 10:38:37,859 [ajp-nio-127.0.0.1-8889-exec-7 | cID:72f1fa23-7f000001-2d7345d0-09a3ae8c] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.256 s, CPU [user: 0.141 s, system: 0.0172 s], Allocated memory: 27.6 MB, transactions: 1, RPC: 0.249 s [99% decodeRequest (1x)] (4x), GC: 0.041 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 10:46:48,926 [ajp-nio-127.0.0.1-8889-exec-8 | cID:72f978be-7f000001-2d7345d0-7de9361e] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754275608704': Total: 0.158 s, CPU [user: 0.0328 s, system: 0.0482 s], Allocated memory: 1.7 MB, transactions: 0, svn: 0.0235 s [84% testConnection (1x)] (2x)
2025-08-04 10:48:49,989 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.11 s, CPU [user: 0.00179 s, system: 0.00183 s], Allocated memory: 131.0 kB, transactions: 0, PullingJob: 0.044 s [100% collectChanges (1x)] (1x), svn: 0.0439 s [100% getLatestRevision (1x)] (1x)
2025-08-04 10:54:29,873 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.521 s, CPU [user: 0.00216 s, system: 0.00498 s], Allocated memory: 129.8 kB, transactions: 0, PullingJob: 0.517 s [100% collectChanges (1x)] (1x), svn: 0.514 s [100% getLatestRevision (1x)] (1x)
2025-08-04 11:06:54,276 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.128 s, CPU [user: 0.00225 s, system: 0.0144 s], Allocated memory: 129.8 kB, transactions: 0, PullingJob: 0.0685 s [100% collectChanges (1x)] (1x), svn: 0.0472 s [100% getLatestRevision (1x)] (1x)
2025-08-04 11:22:14,686 [ajp-nio-127.0.0.1-8889-exec-7 | cID:7319e883-7f000001-2d7345d0-b9899139] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.154 s, CPU [user: 0.0394 s, system: 0.0369 s], Allocated memory: 52.3 MB, transactions: 1, RPC: 0.121 s [71% encodeResponse (1x), 28% writeResponse (1x)] (4x), PortalDataService: 0.03 s [100% getInitData (1x)] (1x)
2025-08-04 11:22:14,893 [ajp-nio-127.0.0.1-8889-exec-8 | cID:7319e97b-7f000001-2d7345d0-2aea0880 | u:admin] INFO  TXLOGGER - Tx 661cc67a5f85b_0_661cc67a5f85b_0_: finished. Total: 0.111 s, CPU [user: 0.0304 s, system: 0.0198 s], Allocated memory: 2.8 MB, RepositoryConfigService: 0.0525 s [100% getReadConfiguration (13x)] (18x), svn: 0.0311 s [87% getFile content (4x)] (6x)
2025-08-04 11:22:14,900 [ajp-nio-127.0.0.1-8889-exec-8 | cID:7319e97b-7f000001-2d7345d0-2aea0880] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.12 s, CPU [user: 0.0347 s, system: 0.0214 s], Allocated memory: 4.1 MB, transactions: 1, PortalDataService: 0.111 s [100% requestPortalSite (1x)] (1x), RepositoryConfigService: 0.0525 s [100% getReadConfiguration (13x)] (18x), svn: 0.0311 s [87% getFile content (4x)] (6x)
2025-08-04 11:22:15,883 [ajp-nio-127.0.0.1-8889-exec-8 | cID:7319ecf3-7f000001-2d7345d0-f868ac2b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.215 s, CPU [user: 0.0146 s, system: 0.00631 s], Allocated memory: 1.4 MB, transactions: 0
2025-08-04 11:22:15,883 [ajp-nio-127.0.0.1-8889-exec-9 | cID:7319ecf4-7f000001-2d7345d0-3f53ec8a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1754277735470': Total: 0.214 s, CPU [user: 0.015 s, system: 0.00609 s], Allocated memory: 1.0 MB, transactions: 0
2025-08-04 11:22:15,883 [ajp-nio-127.0.0.1-8889-exec-10 | cID:7319ecde-7f000001-2d7345d0-7cbb3390] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.236 s, CPU [user: 0.0834 s, system: 0.0338 s], Allocated memory: 9.4 MB, transactions: 0
2025-08-04 11:22:15,974 [ajp-nio-127.0.0.1-8889-exec-4 | cID:7319ecf5-7f000001-2d7345d0-dec6a63e | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1754277735472] INFO  TXLOGGER - Tx 661cc67b6c062_0_661cc67b6c062_0_: finished. Total: 0.118 s, CPU [user: 0.044 s, system: 0.0156 s], Allocated memory: 5.5 MB, svn: 0.0234 s [100% getFile content (2x)] (3x)
2025-08-04 11:22:15,983 [ajp-nio-127.0.0.1-8889-exec-4 | cID:7319ecf5-7f000001-2d7345d0-dec6a63e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754277735472': Total: 0.314 s, CPU [user: 0.0593 s, system: 0.0207 s], Allocated memory: 7.5 MB, transactions: 1, RepositoryConfigService: 0.119 s [100% getReadConfiguration (1x)] (1x), svn: 0.0234 s [100% getFile content (2x)] (3x)
2025-08-04 11:22:16,037 [ajp-nio-127.0.0.1-8889-exec-1 | cID:7319ecf5-7f000001-2d7345d0-61efe006] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754277735471': Total: 0.368 s, CPU [user: 0.0413 s, system: 0.00946 s], Allocated memory: 5.4 MB, transactions: 1, RepositoryConfigService: 0.152 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 11:22:20,553 [ajp-nio-127.0.0.1-8889-exec-8 | cID:7319fa27-7f000001-2d7345d0-2ef45fcf] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.51 s, CPU [user: 0.153 s, system: 0.042 s], Allocated memory: 8.7 MB, transactions: 0
2025-08-04 11:22:21,210 [ajp-nio-127.0.0.1-8889-exec-9 | cID:7319fa27-7f000001-2d7345d0-ac45af05 | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661cc67ecf864_0_661cc67ecf864_0_: finished. Total: 1.88 s, CPU [user: 0.654 s, system: 0.151 s], Allocated memory: 235.3 MB, ObjectEnum Document: 1.69 s [100% available (6x)] (6x), resolve: 1.37 s [99% Module (97x)] (802x), svn: 0.742 s [39% info (92x), 31% getFile content (46x), 30% getDir2 content (46x)] (185x), ModulePageParser: 0.324 s [100% parsePage (46x)] (46x), DB: 0.214 s [58% query (194x), 23% update (194x)] (5917x), GlobalHandler: 0.102 s [88% applyTxChanges (2x)] (1192x)
2025-08-04 11:22:21,211 [ajp-nio-127.0.0.1-8889-exec-9 | cID:7319fa27-7f000001-2d7345d0-ac45af05] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 2.16 s, CPU [user: 0.772 s, system: 0.186 s], Allocated memory: 258.1 MB, transactions: 2, ObjectEnum Document: 1.69 s [100% available (6x)] (6x), resolve: 1.37 s [99% Module (97x)] (803x), svn: 0.742 s [39% info (92x), 31% getFile content (46x), 30% getDir2 content (46x)] (185x), ModulePageParser: 0.324 s [100% parsePage (46x)] (46x), DB: 0.214 s [58% query (194x), 23% update (194x)] (5917x)
2025-08-04 11:22:26,218 [ajp-nio-127.0.0.1-8889-exec-1 | cID:731a0027-7f000001-2d7345d0-b12c955f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/03f6f70a-e94a-42aa-9998-d3eadda1e9dd/metadata': Total: 5.64 s, CPU [user: 0.083 s, system: 0.0114 s], Allocated memory: 9.0 MB, transactions: 0
2025-08-04 11:30:32,018 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.331 s, CPU [user: 0.00203 s, system: 0.0115 s], Allocated memory: 129.7 kB, transactions: 0, PullingJob: 0.294 s [100% collectChanges (1x)] (1x), svn: 0.276 s [100% getLatestRevision (1x)] (1x)
2025-08-04 11:48:36,068 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.195 s, CPU [user: 0.00203 s, system: 0.00625 s], Allocated memory: 129.9 kB, transactions: 0, PullingJob: 0.163 s [100% collectChanges (1x)] (1x), svn: 0.163 s [100% getLatestRevision (1x)] (1x)
