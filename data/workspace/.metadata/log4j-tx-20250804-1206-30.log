2025-08-04 12:06:35,983 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0572 s [59% update (144x), 41% query (12x)] (221x), svn: 0.0152 s [61% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-08-04 12:06:36,111 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0345 s [65% getDir2 content (2x), 29% info (3x)] (6x)
2025-08-04 12:06:37,141 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.02 s, CPU [user: 0.267 s, system: 0.292 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.254 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 12:06:37,141 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.02 s, CPU [user: 0.14 s, system: 0.205 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.103 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 12:06:37,141 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1.02 s, CPU [user: 0.317 s, system: 0.359 s], Allocated memory: 68.7 MB, transactions: 0, ObjectMaps: 0.147 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 12:06:37,141 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1.02 s, CPU [user: 0.0612 s, system: 0.0831 s], Allocated memory: 7.1 MB, transactions: 0, svn: 0.0673 s [84% log2 (5x)] (7x), ObjectMaps: 0.0637 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 12:06:37,141 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1.02 s, CPU [user: 0.135 s, system: 0.164 s], Allocated memory: 16.9 MB, transactions: 0, svn: 0.163 s [46% log2 (10x), 21% log (1x), 16% info (5x)] (24x), ObjectMaps: 0.0974 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 12:06:37,141 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1.02 s, CPU [user: 0.0753 s, system: 0.0924 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.12 s [78% log2 (10x), 13% testConnection (1x)] (13x), ObjectMaps: 0.0519 s [99% getAllPrimaryObjects (2x)] (14x)
2025-08-04 12:06:37,142 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.718 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.449 s [64% log2 (36x), 11% testConnection (6x), 10% getLatestRevision (9x)] (61x)
2025-08-04 12:06:37,275 [main | u:p] INFO  TXLOGGER - Tx 661cd0a25bc01_0_661cd0a25bc01_0_: finished. Total: 0.107 s, CPU [user: 0.0828 s, system: 0.00514 s], Allocated memory: 21.8 MB
2025-08-04 12:06:37,515 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.306 s [100% getReadConfiguration (48x)] (48x), svn: 0.133 s [80% info (18x)] (38x)
2025-08-04 12:06:38,006 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.335 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.269 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 12:06:38,252 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.131 s, CPU [user: 0.0395 s, system: 0.0107 s], Allocated memory: 9.9 MB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 12:06:38,287 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.264 s [100% doFinishStartup (1x)] (1x), commit: 0.0752 s [100% Revision (1x)] (1x), Lucene: 0.0313 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0158 s [100% objectsToInv (1x)] (1x), DB: 0.0143 s [59% update (3x), 23% query (1x)] (8x)
2025-08-04 12:06:40,930 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.463 s [89% info (158x)] (168x)
2025-08-04 12:06:41,198 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cd0a611c41_0_661cd0a611c41_0_: finished. Total: 0.229 s, CPU [user: 0.127 s, system: 0.0151 s], Allocated memory: 17.5 MB, resolve: 0.0638 s [96% User (2x)] (4x), Lucene: 0.0198 s [100% search (1x)] (1x), svn: 0.0171 s [64% getLatestRevision (2x), 28% testConnection (1x)] (5x)
2025-08-04 12:06:41,939 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.88 s, CPU [user: 0.00643 s, system: 0.00152 s], Allocated memory: 356.6 kB, transactions: 1
2025-08-04 12:06:41,939 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.245 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.0776 s [94% User (3x)] (6x), svn: 0.0461 s [56% getLatestRevision (3x), 40% testConnection (2x)] (7x), Incremental Baseline: 0.041 s [100% WorkItem (24x)] (24x), Lucene: 0.0395 s [50% search (1x), 34% add (1x)] (3x), PullingJob: 0.0292 s [100% collectChanges (1x)] (1x), persistence listener: 0.0144 s [90% indexRefreshPersistenceListener (1x)] (7x), ObjectMaps: 0.0138 s [66% getPrimaryObjectLocation (2x), 25% getPrimaryObjectProperty (1x)] (7x)
2025-08-04 12:06:41,940 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.01 s, CPU [user: 0.173 s, system: 0.0293 s], Allocated memory: 19.5 MB, transactions: 26, svn: 0.777 s [98% getDatedRevision (181x)] (183x)
2025-08-04 12:06:42,536 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd0a612845_0_661cd0a612845_0_: finished. Total: 1.57 s, CPU [user: 0.442 s, system: 0.116 s], Allocated memory: 54.5 MB, svn: 0.993 s [56% getDatedRevision (181x), 28% getDir2 content (25x)] (328x), resolve: 0.522 s [100% Category (117x)] (117x), ObjectMaps: 0.205 s [39% getPrimaryObjectLocation (117x), 39% getPrimaryObjectProperty (117x), 22% getLastPromoted (117x)] (473x)
2025-08-04 12:06:42,769 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd0a7b344b_0_661cd0a7b344b_0_: finished. Total: 0.131 s, CPU [user: 0.0579 s, system: 0.0114 s], Allocated memory: 8.2 MB, RepositoryConfigService: 0.0583 s [50% getReadUserConfiguration (10x), 50% getReadConfiguration (180x)] (190x), svn: 0.0529 s [59% info (21x), 33% getFile content (16x)] (39x), resolve: 0.0425 s [100% User (9x)] (9x), ObjectMaps: 0.0269 s [72% getPrimaryObjectProperty (8x), 15% getLastPromoted (8x)] (32x)
2025-08-04 12:06:42,894 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd0a7d444c_0_661cd0a7d444c_0_: finished. Total: 0.125 s, CPU [user: 0.045 s, system: 0.00476 s], Allocated memory: 5.4 MB, svn: 0.0857 s [76% info (29x), 24% getFile content (13x)] (43x), RepositoryConfigService: 0.0599 s [63% getReadConfiguration (54x), 37% getExistingPrefixes (9x)] (77x)
2025-08-04 12:06:43,034 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd0a7f384d_0_661cd0a7f384d_0_: finished. Total: 0.139 s, CPU [user: 0.039 s, system: 0.00392 s], Allocated memory: 20.6 MB, svn: 0.113 s [53% getDir2 content (13x), 32% info (35x)] (93x), RepositoryConfigService: 0.0413 s [69% getReadConfiguration (170x), 31% getExistingPrefixes (11x)] (192x)
2025-08-04 12:06:43,229 [ajp-nio-127.0.0.1-8889-exec-2 | cID:734299ac-c613f903-2a946570-19b12ef8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.9 s, CPU [user: 0.611 s, system: 0.0629 s], Allocated memory: 62.7 MB, transactions: 3, PolarionAuthenticator: 0.455 s [100% authenticate (1x)] (1x)
2025-08-04 12:06:44,096 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd0a81684e_0_661cd0a81684e_0_: finished. Total: 1.06 s, CPU [user: 0.397 s, system: 0.0267 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.889 s [65% getReadConfiguration (8682x), 35% getExistingPrefixes (129x)] (9021x), svn: 0.689 s [45% info (226x), 41% getFile content (412x)] (656x)
2025-08-04 12:06:44,199 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd0a92004f_0_661cd0a92004f_0_: finished. Total: 0.103 s, CPU [user: 0.0234 s, system: 0.00235 s], Allocated memory: 18.8 MB, svn: 0.0923 s [49% getDir2 content (14x), 38% info (37x)] (81x), RepositoryConfigService: 0.0327 s [56% getReadConfiguration (124x), 44% getExistingPrefixes (12x)] (148x)
2025-08-04 12:06:44,635 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd0a946851_0_661cd0a946851_0_: finished. Total: 0.385 s, CPU [user: 0.144 s, system: 0.0102 s], Allocated memory: 389.6 MB, RepositoryConfigService: 0.284 s [56% getReadConfiguration (2787x), 44% getExistingPrefixes (89x)] (3025x), svn: 0.27 s [48% info (152x), 28% getFile content (185x), 24% getDir2 content (17x)] (355x)
2025-08-04 12:06:44,713 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.75 s, CPU [user: 1.22 s, system: 0.189 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.45 s [27% info (579x), 25% getDir2 content (110x), 24% getFile content (864x), 22% getDatedRevision (181x)] (1750x), RepositoryConfigService: 1.43 s [63% getReadConfiguration (12165x), 35% getExistingPrefixes (271x)] (12859x), resolve: 0.612 s [85% Category (117x)] (139x), ObjectMaps: 0.251 s [44% getPrimaryObjectProperty (130x), 35% getPrimaryObjectLocation (136x), 20% getLastPromoted (130x)] (532x)
2025-08-04 12:06:44,714 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 65, svn: 3.25 s [40% getDatedRevision (362x), 21% info (582x), 19% getDir2 content (110x)] (1946x), RepositoryConfigService: 1.51 s [64% getReadConfiguration (12167x), 34% getExistingPrefixes (271x)] (12861x), resolve: 0.641 s [81% Category (117x)] (142x), PolarionAuthenticator: 0.455 s [100% authenticate (1x)] (1x), ObjectMaps: 0.257 s [44% getPrimaryObjectProperty (131x), 35% getPrimaryObjectLocation (137x), 20% getLastPromoted (131x)] (536x)
2025-08-04 12:06:47,704 [ajp-nio-127.0.0.1-8889-exec-3 | cID:7342a128-c613f903-2a946570-caaf75f6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/504d4ab5-391e-4568-960f-fca44fa0f2e9/metadata': Total: 4.46 s, CPU [user: 0.104 s, system: 0.0101 s], Allocated memory: 9.7 MB, transactions: 0
2025-08-04 12:07:20,835 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.619287109375
2025-08-04 12:07:30,839 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.423779296875
2025-08-04 12:07:40,836 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.266357421875
2025-08-04 12:07:50,840 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.12509765625
2025-08-04 12:08:00,837 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.007470703125
