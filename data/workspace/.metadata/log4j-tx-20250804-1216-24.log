2025-08-04 12:16:30,178 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0547 s [64% update (144x), 36% query (12x)] (221x), svn: 0.0146 s [55% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-08-04 12:16:30,295 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.037 s [57% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-04 12:16:31,011 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.058 s, system: 0.0985 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0603 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 12:16:31,011 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.206 s, system: 0.286 s], Allocated memory: 53.5 MB, transactions: 0, ObjectMaps: 0.11 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 12:16:31,011 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.0731 s, system: 0.0956 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0734 s [81% log2 (10x)] (13x), ObjectMaps: 0.0562 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 12:16:31,011 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.0979 s, system: 0.169 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0833 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0625 s [75% log2 (10x), 17% getLatestRevision (2x)] (13x)
2025-08-04 12:16:31,011 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.246 s, system: 0.347 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.122 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 12:16:31,011 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.148 s, system: 0.225 s], Allocated memory: 26.2 MB, transactions: 0, ObjectMaps: 0.0942 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0804 s [33% log2 (5x), 26% info (5x), 18% log (1x), 10% getLatestRevision (2x)] (18x)
2025-08-04 12:16:31,011 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.526 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.284 s [62% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-04 12:16:31,260 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.207 s [100% getReadConfiguration (48x)] (48x), svn: 0.0853 s [80% info (18x)] (38x)
2025-08-04 12:16:31,655 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.311 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.231 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 12:16:31,886 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.117 s, CPU [user: 0.0283 s, system: 0.00758 s], Allocated memory: 8.6 MB, GC: 0.013 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 12:16:31,913 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.235 s [100% doFinishStartup (1x)] (1x), commit: 0.0679 s [100% Revision (1x)] (1x), Lucene: 0.0323 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0159 s [100% objectsToInv (1x)] (1x)
2025-08-04 12:16:34,397 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.298 s [88% info (158x)] (168x)
2025-08-04 12:16:34,757 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cd2e999041_0_661cd2e999041_0_: finished. Total: 0.353 s, CPU [user: 0.156 s, system: 0.0214 s], Allocated memory: 17.5 MB, resolve: 0.117 s [99% User (2x)] (4x), planQueryExpander: 0.0423 s [100% expand (1x)] (2x), GC: 0.033 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.0282 s [100% search (1x)] (1x), ObjectMaps: 0.0208 s [58% getPrimaryObjectLocation (1x), 34% getPrimaryObjectProperty (1x)] (6x), svn: 0.0203 s [51% getLatestRevision (2x), 44% testConnection (1x)] (5x)
2025-08-04 12:16:35,387 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.874 s, CPU [user: 0.00665 s, system: 0.00148 s], Allocated memory: 356.7 kB, transactions: 1
2025-08-04 12:16:35,389 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.361 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.134 s [98% User (3x)] (6x), Incremental Baseline: 0.0446 s [100% WorkItem (24x)] (24x), planQueryExpander: 0.0423 s [100% expand (1x)] (2x), Lucene: 0.0372 s [76% search (1x), 24% refresh (1x)] (2x), svn: 0.0309 s [52% getLatestRevision (3x), 44% testConnection (2x)] (7x), ObjectMaps: 0.0262 s [67% getPrimaryObjectLocation (2x), 27% getPrimaryObjectProperty (1x)] (7x)
2025-08-04 12:16:35,390 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1 s, CPU [user: 0.165 s, system: 0.0312 s], Allocated memory: 18.8 MB, transactions: 25, svn: 0.765 s [99% getDatedRevision (181x)] (183x)
2025-08-04 12:16:35,735 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd2e998840_0_661cd2e998840_0_: finished. Total: 1.33 s, CPU [user: 0.384 s, system: 0.114 s], Allocated memory: 51.2 MB, svn: 0.756 s [42% getDatedRevision (181x), 38% getDir2 content (25x), 19% getFile content (119x)] (328x), resolve: 0.517 s [100% Category (117x)] (117x), ObjectMaps: 0.201 s [40% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 25% getLastPromoted (117x)] (473x)
2025-08-04 12:16:35,930 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd2eafbc48_0_661cd2eafbc48_0_: finished. Total: 0.107 s, CPU [user: 0.054 s, system: 0.0105 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0496 s [52% getReadUserConfiguration (10x), 48% getReadConfiguration (180x)] (190x), svn: 0.0447 s [56% info (21x), 36% getFile content (16x)] (39x), resolve: 0.0326 s [100% User (9x)] (9x), ObjectMaps: 0.0157 s [57% getPrimaryObjectProperty (8x), 24% getLastPromoted (8x)] (32x)
2025-08-04 12:16:36,111 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd2eb2104a_0_661cd2eb2104a_0_: finished. Total: 0.139 s, CPU [user: 0.0539 s, system: 0.0055 s], Allocated memory: 19.8 MB, svn: 0.0989 s [67% getDir2 content (17x), 33% getFile content (44x)] (62x), RepositoryConfigService: 0.0548 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 12:16:36,950 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd2eb43c4b_0_661cd2eb43c4b_0_: finished. Total: 0.838 s, CPU [user: 0.385 s, system: 0.028 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.598 s [96% getReadConfiguration (8682x)] (9021x), svn: 0.437 s [64% getFile content (412x), 35% getDir2 content (21x)] (434x), GC: 0.044 s [100% G1 Young Generation (4x)] (4x)
2025-08-04 12:16:37,618 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd2ec3804e_0_661cd2ec3804e_0_: finished. Total: 0.53 s, CPU [user: 0.206 s, system: 0.018 s], Allocated memory: 387.4 MB, RepositoryConfigService: 0.391 s [95% getReadConfiguration (2787x)] (3025x), svn: 0.318 s [67% getFile content (185x), 33% getDir2 content (21x)] (207x)
2025-08-04 12:16:37,719 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd2ecbc84f_0_661cd2ecbc84f_0_: finished. Total: 0.101 s, CPU [user: 0.0211 s, system: 0.00259 s], Allocated memory: 13.2 MB, svn: 0.0908 s [83% getDir2 content (18x)] (52x), RepositoryConfigService: 0.0223 s [98% getReadConfiguration (128x)] (150x)
2025-08-04 12:16:37,720 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.32 s, CPU [user: 1.2 s, system: 0.194 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.92 s [42% getDir2 content (133x), 39% getFile content (865x)] (1224x), RepositoryConfigService: 1.19 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.599 s [86% Category (117x)] (139x), ObjectMaps: 0.237 s [43% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 24% getLastPromoted (131x)] (536x)
2025-08-04 12:16:37,720 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 61, svn: 2.68 s [40% getDatedRevision (362x), 30% getDir2 content (133x), 28% getFile content (865x)] (1407x), RepositoryConfigService: 1.19 s [94% getReadConfiguration (12165x)] (12859x), resolve: 0.599 s [86% Category (117x)] (140x), ObjectMaps: 0.237 s [43% getPrimaryObjectProperty (131x), 32% getPrimaryObjectLocation (137x), 24% getLastPromoted (131x)] (536x)
2025-08-04 12:16:48,812 [ajp-nio-127.0.0.1-8889-exec-2 | cID:734bd614-c0a844bd-25b4d4f7-c4fa7c39] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 2.2 s, CPU [user: 0.553 s, system: 0.111 s], Allocated memory: 65.5 MB, transactions: 3, PolarionAuthenticator: 0.559 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.121 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 12:16:53,245 [ajp-nio-127.0.0.1-8889-exec-3 | cID:734bdebb-c0a844bd-25b4d4f7-25227523] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/2bfb3fa7-2d45-48da-900d-ce663506287b/metadata': Total: 4.42 s, CPU [user: 0.137 s, system: 0.0187 s], Allocated memory: 10.8 MB, transactions: 0
