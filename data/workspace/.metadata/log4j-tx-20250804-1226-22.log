2025-08-04 12:26:27,558 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0384 s [61% update (144x), 39% query (12x)] (221x), svn: 0.0166 s [46% getLatestRevision (2x), 38% testConnection (1x)] (4x)
2025-08-04 12:26:27,678 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0359 s [57% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-04 12:26:28,430 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.749 s, CPU [user: 0.264 s, system: 0.345 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.133 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 12:26:28,430 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.75 s, CPU [user: 0.13 s, system: 0.223 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0782 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 12:26:28,431 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.75 s, CPU [user: 0.229 s, system: 0.29 s], Allocated memory: 53.3 MB, transactions: 0, ObjectMaps: 0.126 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 12:26:28,431 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.749 s, CPU [user: 0.0716 s, system: 0.104 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0746 s [80% log2 (10x), 14% getLatestRevision (2x)] (13x), ObjectMaps: 0.0575 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 12:26:28,431 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.749 s, CPU [user: 0.0612 s, system: 0.0948 s], Allocated memory: 7.4 MB, transactions: 0, ObjectMaps: 0.0535 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 12:26:28,431 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.748 s, CPU [user: 0.128 s, system: 0.174 s], Allocated memory: 16.7 MB, transactions: 0, svn: 0.0995 s [40% log2 (10x), 20% info (5x), 15% log (1x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.0898 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 12:26:28,431 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.539 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.267 s [61% log2 (36x), 14% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-04 12:26:28,708 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.235 s [100% getReadConfiguration (48x)] (48x), svn: 0.083 s [79% info (18x), 8% getLatestRevision (1x)] (38x)
2025-08-04 12:26:29,077 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.288 s [78% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.228 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 12:26:29,306 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.115 s, CPU [user: 0.0263 s, system: 0.00729 s], Allocated memory: 8.6 MB, GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 12:26:29,333 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 27, PersistenceEngineListener: 0.241 s [100% doFinishStartup (1x)] (1x), commit: 0.0661 s [100% Revision (1x)] (1x), Lucene: 0.0339 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0161 s [100% objectsToInv (1x)] (1x)
2025-08-04 12:26:32,444 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.439 s [90% info (158x)] (170x)
2025-08-04 12:26:32,928 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cd531b0845_0_661cd531b0845_0_: finished. Total: 0.413 s, CPU [user: 0.153 s, system: 0.0202 s], Allocated memory: 16.4 MB, resolve: 0.0764 s [96% User (2x)] (4x), outlineNumberQueryExpander: 0.063 s [100% expand (1x)] (2x), GC: 0.058 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.0434 s [100% search (1x)] (1x)
2025-08-04 12:26:33,226 [ajp-nio-127.0.0.1-8889-exec-2 | cID:7354c722-c0a844bd-6cc86309-b3810369] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 0.612 s, CPU [user: 0.327 s, system: 0.0483 s], Allocated memory: 42.8 MB, transactions: 2, PolarionAuthenticator: 0.609 s [100% authenticate (1x)] (1x)
2025-08-04 12:26:33,559 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.86 s, CPU [user: 0.00767 s, system: 0.00224 s], Allocated memory: 355.7 kB, transactions: 1
2025-08-04 12:26:33,561 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 32, PolarionAuthenticator: 0.609 s [100% authenticate (1x)] (1x), notification worker: 0.424 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.193 s [98% User (5x)] (8x), Lucene: 0.0874 s [50% search (1x), 26% add (1x), 24% refresh (1x)] (3x), ObjectMaps: 0.0868 s [95% getPrimaryObjectLocation (3x)] (8x), persistence listener: 0.0777 s [59% indexRefreshPersistenceListener (1x), 34% WorkItemActivityCreator (1x)] (7x), outlineNumberQueryExpander: 0.063 s [100% expand (1x)] (2x), Incremental Baseline: 0.0367 s [100% WorkItem (24x)] (24x)
2025-08-04 12:26:33,563 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.87 s, CPU [user: 0.178 s, system: 0.0297 s], Allocated memory: 19.5 MB, transactions: 26, svn: 0.723 s [96% getDatedRevision (181x)] (183x), Lucene: 0.0717 s [72% buildBaselineSnapshots (1x), 28% buildBaseline (25x)] (26x)
2025-08-04 12:26:34,010 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd531ae440_0_661cd531ae440_0_: finished. Total: 1.5 s, CPU [user: 0.394 s, system: 0.107 s], Allocated memory: 51.4 MB, svn: 0.934 s [45% getDatedRevision (181x), 39% getDir2 content (25x)] (328x), resolve: 0.471 s [100% Category (117x)] (117x), ObjectMaps: 0.171 s [44% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 12:26:34,202 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd5333c04a_0_661cd5333c04a_0_: finished. Total: 0.106 s, CPU [user: 0.0538 s, system: 0.00825 s], Allocated memory: 8.6 MB, RepositoryConfigService: 0.0527 s [54% getReadConfiguration (180x), 46% getReadUserConfiguration (10x)] (190x), svn: 0.0452 s [58% info (21x), 37% getFile content (16x)] (39x), resolve: 0.0277 s [100% User (9x)] (9x), ObjectMaps: 0.0141 s [52% getPrimaryObjectProperty (8x), 26% getLastPromoted (8x), 22% getPrimaryObjectLocation (8x)] (32x)
2025-08-04 12:26:34,446 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd5336a04c_0_661cd5336a04c_0_: finished. Total: 0.165 s, CPU [user: 0.0549 s, system: 0.00507 s], Allocated memory: 20.7 MB, svn: 0.138 s [56% getDir2 content (13x), 31% info (35x)] (93x), RepositoryConfigService: 0.0407 s [68% getReadConfiguration (170x), 32% getExistingPrefixes (11x)] (192x)
2025-08-04 12:26:35,476 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd5339384d_0_661cd5339384d_0_: finished. Total: 1.03 s, CPU [user: 0.396 s, system: 0.0232 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.783 s [64% getReadConfiguration (8682x), 36% getExistingPrefixes (129x)] (9021x), svn: 0.676 s [46% info (226x), 36% getFile content (412x)] (656x)
2025-08-04 12:26:36,007 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd534b7c50_0_661cd534b7c50_0_: finished. Total: 0.391 s, CPU [user: 0.146 s, system: 0.00974 s], Allocated memory: 393.9 MB, RepositoryConfigService: 0.289 s [54% getReadConfiguration (2787x), 46% getExistingPrefixes (89x)] (3025x), svn: 0.279 s [49% info (152x), 27% getFile content (185x), 24% getDir2 content (17x)] (355x)
2025-08-04 12:26:36,112 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd53519c51_0_661cd53519c51_0_: finished. Total: 0.105 s, CPU [user: 0.0263 s, system: 0.00316 s], Allocated memory: 14.3 MB, svn: 0.0953 s [54% getDir2 content (14x), 32% info (37x)] (85x), RepositoryConfigService: 0.0302 s [65% getReadConfiguration (128x), 35% getExistingPrefixes (12x)] (150x)
2025-08-04 12:26:36,113 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.63 s, CPU [user: 1.18 s, system: 0.17 s], Allocated memory: 1.7 GB, transactions: 11, svn: 2.38 s [32% getDir2 content (110x), 27% info (580x), 22% getFile content (865x)] (1753x), RepositoryConfigService: 1.29 s [61% getReadConfiguration (12165x), 37% getExistingPrefixes (271x)] (12859x), resolve: 0.552 s [85% Category (117x)] (139x), ObjectMaps: 0.207 s [47% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-04 12:26:36,113 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 3.11 s [36% getDatedRevision (362x), 24% getDir2 content (110x), 21% info (580x)] (1937x), RepositoryConfigService: 1.29 s [61% getReadConfiguration (12165x), 37% getExistingPrefixes (271x)] (12859x), resolve: 0.553 s [85% Category (117x)] (140x), ObjectMaps: 0.207 s [47% getPrimaryObjectProperty (131x), 31% getPrimaryObjectLocation (137x), 22% getLastPromoted (131x)] (536x)
2025-08-04 12:26:50,124 [ajp-nio-127.0.0.1-8889-exec-2 | cID:73550aaa-c0a844bd-6cc86309-7b545141] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.225 s, CPU [user: 0.156 s, system: 0.0302 s], Allocated memory: 65.6 MB, transactions: 1, RPC: 0.148 s [52% encodeResponse (1x), 31% decodeRequest (1x)] (4x), PortalDataService: 0.0644 s [100% getInitData (1x)] (1x), RepositoryConfigService: 0.0197 s [100% getReadConfiguration (10x)] (15x), svn: 0.0143 s [50% getDir2 content (1x), 50% info (1x)] (3x)
2025-08-04 12:26:50,324 [ajp-nio-127.0.0.1-8889-exec-3 | cID:73550bb1-c0a844bd-6cc86309-31d99274 | u:admin] INFO  TXLOGGER - Tx 661cd542edc54_0_661cd542edc54_0_: finished. Total: 0.156 s, CPU [user: 0.0716 s, system: 0.015 s], Allocated memory: 16.3 MB, RepositoryConfigService: 0.0621 s [96% getReadConfiguration (13x)] (18x), svn: 0.0518 s [38% getFile content (7x), 36% info (8x), 15% getDir2 content (1x)] (18x)
2025-08-04 12:26:50,337 [ajp-nio-127.0.0.1-8889-exec-3 | cID:73550bb1-c0a844bd-6cc86309-31d99274] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.175 s, CPU [user: 0.085 s, system: 0.0184 s], Allocated memory: 19.5 MB, transactions: 1, PortalDataService: 0.157 s [100% requestPortalSite (1x)] (1x), RepositoryConfigService: 0.0621 s [96% getReadConfiguration (13x)] (18x), svn: 0.0518 s [38% getFile content (7x), 36% info (8x), 15% getDir2 content (1x)] (18x), RPC: 0.0161 s [70% encodeResponse (1x), 24% decodeRequest (1x)] (4x)
2025-08-04 12:26:50,742 [ajp-nio-127.0.0.1-8889-exec-6 | cID:73550d8f-c0a844bd-6cc86309-f3375773] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.102 s, CPU [user: 0.0172 s, system: 0.00479 s], Allocated memory: 1.6 MB, transactions: 0
2025-08-04 12:26:50,742 [ajp-nio-127.0.0.1-8889-exec-2 | cID:73550d8d-c0a844bd-6cc86309-945de189] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.104 s, CPU [user: 0.0522 s, system: 0.0121 s], Allocated memory: 9.4 MB, transactions: 0
2025-08-04 12:26:50,779 [ajp-nio-127.0.0.1-8889-exec-3 | cID:73550d8f-c0a844bd-6cc86309-8481c4a1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1754281610595': Total: 0.14 s, CPU [user: 0.0318 s, system: 0.00629 s], Allocated memory: 1.9 MB, transactions: 0
2025-08-04 12:26:50,823 [ajp-nio-127.0.0.1-8889-exec-7 | cID:73550d90-c0a844bd-6cc86309-052a361b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754281610597': Total: 0.182 s, CPU [user: 0.024 s, system: 0.00475 s], Allocated memory: 4.7 MB, transactions: 1, RepositoryConfigService: 0.0975 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 12:26:50,823 [ajp-nio-127.0.0.1-8889-exec-4 | cID:73550d90-c0a844bd-6cc86309-59b07531] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754281610596': Total: 0.183 s, CPU [user: 0.0593 s, system: 0.0117 s], Allocated memory: 8.5 MB, transactions: 1, RepositoryConfigService: 0.083 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 12:26:54,760 [ajp-nio-127.0.0.1-8889-exec-10 | cID:73551873-c0a844bd-6cc86309-039241c0 | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661cd54655c5c_0_661cd54655c5c_0_: finished. Total: 1.1 s, CPU [user: 0.47 s, system: 0.0725 s], Allocated memory: 235.7 MB, ObjectEnum Document: 1.02 s [100% available (6x)] (6x), resolve: 0.791 s [99% Module (97x)] (802x), svn: 0.459 s [50% info (92x), 33% getDir2 content (46x)] (186x), ModulePageParser: 0.176 s [100% parsePage (46x)] (46x), DB: 0.143 s [56% query (194x), 24% update (194x), 20% commit (194x)] (5917x)
2025-08-04 12:26:54,762 [ajp-nio-127.0.0.1-8889-exec-10 | cID:73551873-c0a844bd-6cc86309-039241c0] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.33 s, CPU [user: 0.567 s, system: 0.0896 s], Allocated memory: 258.7 MB, transactions: 2, ObjectEnum Document: 1.02 s [100% available (6x)] (6x), resolve: 0.791 s [99% Module (97x)] (803x), svn: 0.459 s [50% info (92x), 33% getDir2 content (46x)] (186x), ModulePageParser: 0.176 s [100% parsePage (46x)] (46x), DB: 0.143 s [56% query (194x), 24% update (194x), 20% commit (194x)] (5917x)
2025-08-04 12:26:54,773 [ajp-nio-127.0.0.1-8889-exec-5 | cID:73551873-c0a844bd-6cc86309-bdc23f89] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.35 s, CPU [user: 0.156 s, system: 0.0184 s], Allocated memory: 10.3 MB, transactions: 0
2025-08-04 12:26:59,339 [ajp-nio-127.0.0.1-8889-exec-6 | cID:73551dbe-c0a844bd-6cc86309-6bcee5d8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/34ef103c-2d67-4f6b-b5c2-3858b73a0a89/metadata': Total: 4.56 s, CPU [user: 0.103 s, system: 0.0125 s], Allocated memory: 9.8 MB, transactions: 0
