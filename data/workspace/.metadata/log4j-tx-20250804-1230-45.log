2025-08-04 12:30:51,162 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.124 s [82% update (144x)] (221x), svn: 0.0116 s [45% getLatestRevision (2x), 39% testConnection (1x)] (4x)
2025-08-04 12:30:51,302 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0328 s [61% getDir2 content (2x), 32% info (3x)] (6x)
2025-08-04 12:30:52,041 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.221 s, system: 0.289 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 12:30:52,041 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.131 s, system: 0.226 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0864 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 12:30:52,041 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.264 s, system: 0.351 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.13 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 12:30:52,042 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.105 s, system: 0.171 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.0874 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0607 s [73% log2 (10x), 19% getLatestRevision (2x)] (13x)
2025-08-04 12:30:52,041 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.0709 s, system: 0.0977 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0726 s [82% log2 (10x)] (13x), ObjectMaps: 0.0616 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 12:30:52,042 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.0759 s, system: 0.101 s], Allocated memory: 9.7 MB, transactions: 0, svn: 0.0773 s [30% log2 (5x), 27% info (5x), 21% log (1x), 11% getLatestRevision (2x)] (18x), ObjectMaps: 0.0508 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 12:30:52,043 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.533 s [100% getAllPrimaryObjects (8x)] (62x), svn: 0.271 s [61% log2 (36x), 15% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-08-04 12:30:52,179 [main | u:p] INFO  TXLOGGER - Tx 661cd62f2a401_0_661cd62f2a401_0_: finished. Total: 0.105 s, CPU [user: 0.0848 s, system: 0.00425 s], Allocated memory: 21.8 MB
2025-08-04 12:30:52,311 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.22 s [100% getReadConfiguration (48x)] (48x), svn: 0.0776 s [80% info (18x)] (38x)
2025-08-04 12:30:52,690 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.297 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.223 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 12:30:52,939 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.145 s, CPU [user: 0.0372 s, system: 0.011 s], Allocated memory: 9.5 MB
2025-08-04 12:30:52,974 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.269 s [100% doFinishStartup (1x)] (1x), commit: 0.0759 s [100% Revision (1x)] (1x), Lucene: 0.0488 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0151 s [100% objectsToInv (1x)] (1x), DB: 0.0144 s [53% update (3x), 23% query (1x), 14% commit (2x)] (8x)
2025-08-04 12:30:55,632 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.384 s [90% info (158x)] (168x)
2025-08-04 12:30:55,640 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, EHCache: 0.000031 s [100% GET (1x)] (1x)
2025-08-04 12:30:56,022 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cd632a7842_0_661cd632a7842_0_: finished. Total: 0.375 s, CPU [user: 0.144 s, system: 0.0183 s], Allocated memory: 16.2 MB, Lucene: 0.0704 s [100% search (1x)] (1x), resolve: 0.0693 s [95% User (2x)] (4x)
2025-08-04 12:30:56,395 [Activities-Bulk-Publisher] INFO  TXLOGGER - Summary: Total: 0.165 s, CPU [user: 0.0123 s, system: 0.00679 s], Allocated memory: 2.1 MB, transactions: 0, Lucene: 0.155 s [100% add (1x)] (1x)
2025-08-04 12:30:56,762 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.945 s, CPU [user: 0.00905 s, system: 0.00171 s], Allocated memory: 356.2 kB, transactions: 1
2025-08-04 12:30:56,763 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.396 s [97% RevisionActivityCreator (2x)] (6x), Lucene: 0.241 s [64% add (1x), 29% search (1x)] (3x), resolve: 0.104 s [93% User (3x)] (6x), persistence listener: 0.0305 s [69% indexRefreshPersistenceListener (1x), 21% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.027 s [100% WorkItem (24x)] (24x), svn: 0.0264 s [51% getLatestRevision (3x), 41% testConnection (2x)] (7x), ObjectMaps: 0.0214 s [70% getPrimaryObjectLocation (2x), 14% getPrimaryObjectProperty (1x)] (7x)
2025-08-04 12:30:56,763 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.14 s, CPU [user: 0.173 s, system: 0.0275 s], Allocated memory: 19.2 MB, transactions: 26, svn: 0.815 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0645 s [81% buildBaselineSnapshots (1x)] (26x)
2025-08-04 12:30:57,204 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd632a8c45_0_661cd632a8c45_0_: finished. Total: 1.55 s, CPU [user: 0.397 s, system: 0.107 s], Allocated memory: 51.6 MB, svn: 0.933 s [43% getDatedRevision (181x), 37% getDir2 content (25x)] (328x), resolve: 0.581 s [100% Category (117x)] (117x), ObjectMaps: 0.259 s [37% getPrimaryObjectLocation (117x), 32% getPrimaryObjectProperty (117x), 31% getLastPromoted (117x)] (473x)
2025-08-04 12:30:57,437 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd63448848_0_661cd63448848_0_: finished. Total: 0.123 s, CPU [user: 0.0593 s, system: 0.0103 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0584 s [54% getReadConfiguration (180x), 46% getReadUserConfiguration (10x)] (190x), svn: 0.053 s [55% info (21x), 38% getFile content (16x)] (39x), resolve: 0.0337 s [100% User (9x)] (9x), ObjectMaps: 0.0166 s [54% getPrimaryObjectProperty (8x), 24% getLastPromoted (8x), 22% getPrimaryObjectLocation (8x)] (32x)
2025-08-04 12:30:57,678 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd6347244a_0_661cd6347244a_0_: finished. Total: 0.197 s, CPU [user: 0.0673 s, system: 0.00781 s], Allocated memory: 19.8 MB, svn: 0.145 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), RepositoryConfigService: 0.0896 s [99% getReadConfiguration (170x)] (192x)
2025-08-04 12:30:58,553 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd634a384b_0_661cd634a384b_0_: finished. Total: 0.874 s, CPU [user: 0.391 s, system: 0.0233 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.662 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.496 s [69% getFile content (412x), 31% getDir2 content (21x)] (434x)
2025-08-04 12:30:59,129 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd635a1c4e_0_661cd635a1c4e_0_: finished. Total: 0.433 s, CPU [user: 0.163 s, system: 0.0161 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.329 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.258 s [69% getFile content (185x), 31% getDir2 content (21x)] (207x)
2025-08-04 12:30:59,247 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cd6360e44f_0_661cd6360e44f_0_: finished. Total: 0.118 s, CPU [user: 0.032 s, system: 0.00486 s], Allocated memory: 13.1 MB, svn: 0.0939 s [60% getDir2 content (18x), 40% getFile content (33x)] (52x), RepositoryConfigService: 0.0571 s [99% getReadConfiguration (128x)] (150x)
2025-08-04 12:30:59,247 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.6 s, CPU [user: 1.22 s, system: 0.186 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.17 s [39% getDir2 content (133x), 39% getFile content (865x), 19% getDatedRevision (181x)] (1224x), RepositoryConfigService: 1.26 s [95% getReadConfiguration (12165x)] (12859x), resolve: 0.672 s [86% Category (117x)] (139x), ObjectMaps: 0.298 s [36% getPrimaryObjectProperty (131x), 35% getPrimaryObjectLocation (137x), 29% getLastPromoted (131x)] (536x)
2025-08-04 12:30:59,248 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 62, svn: 2.99 s [40% getDatedRevision (362x), 29% getDir2 content (133x), 28% getFile content (865x)] (1408x), RepositoryConfigService: 1.26 s [95% getReadConfiguration (12165x)] (12859x), resolve: 0.672 s [86% Category (117x)] (140x), ObjectMaps: 0.298 s [36% getPrimaryObjectProperty (131x), 35% getPrimaryObjectLocation (137x), 29% getLastPromoted (131x)] (536x)
2025-08-04 12:31:00,998 [ajp-nio-127.0.0.1-8889-exec-2 | cID:7358d883-c0a844bd-7d2edd41-d0d55bb0] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.79 s, CPU [user: 0.423 s, system: 0.0513 s], Allocated memory: 65.4 MB, transactions: 3, PolarionAuthenticator: 0.404 s [100% authenticate (1x)] (1x)
2025-08-04 12:31:05,538 [ajp-nio-127.0.0.1-8889-exec-3 | cID:7358df92-c0a844bd-7d2edd41-8ff7c781] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/98c0f380-5c45-44f7-a410-0dac39b00f07/metadata': Total: 4.53 s, CPU [user: 0.13 s, system: 0.0133 s], Allocated memory: 10.7 MB, transactions: 0
