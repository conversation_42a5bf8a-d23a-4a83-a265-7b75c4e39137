2025-08-04 15:14:49,131 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0751 s [66% update (144x), 34% query (12x)] (221x), svn: 0.0137 s [44% testConnection (1x), 42% getLatestRevision (2x)] (4x)
2025-08-04 15:14:49,292 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0388 s [57% getDir2 content (2x), 31% info (3x)] (6x)
2025-08-04 15:14:50,253 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.956 s, CPU [user: 0.0928 s, system: 0.103 s], Allocated memory: 10.7 MB, transactions: 0, ObjectMaps: 0.0895 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0849 s [82% log2 (10x)] (13x)
2025-08-04 15:14:50,253 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.956 s, CPU [user: 0.161 s, system: 0.228 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 15:14:50,253 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.957 s, CPU [user: 0.283 s, system: 0.313 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.154 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 15:14:50,253 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.956 s, CPU [user: 0.0909 s, system: 0.0928 s], Allocated memory: 9.4 MB, transactions: 0, svn: 0.137 s [35% log (1x), 31% log2 (5x), 17% info (5x)] (18x), ObjectMaps: 0.0721 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 15:14:50,253 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.955 s, CPU [user: 0.108 s, system: 0.138 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.11 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0876 s [78% log2 (10x), 17% getLatestRevision (2x)] (13x)
2025-08-04 15:14:50,253 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.956 s, CPU [user: 0.313 s, system: 0.366 s], Allocated memory: 70.5 MB, transactions: 0, ObjectMaps: 0.132 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 15:14:50,254 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.674 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.382 s [59% log2 (36x), 12% getLatestRevision (9x), 12% log (1x)] (61x)
2025-08-04 15:14:50,531 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.221 s [100% getReadConfiguration (48x)] (48x), svn: 0.0892 s [84% info (18x)] (38x)
2025-08-04 15:14:50,905 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.271 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.209 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 15:14:51,114 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.101 s, CPU [user: 0.0418 s, system: 0.0108 s], Allocated memory: 10.1 MB
2025-08-04 15:14:51,138 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.217 s [100% doFinishStartup (1x)] (1x), commit: 0.0562 s [100% Revision (1x)] (1x), Lucene: 0.0315 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0158 s [100% objectsToInv (1x)] (1x)
2025-08-04 15:14:53,632 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.389 s [91% info (158x)] (168x)
2025-08-04 15:14:53,903 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cfbba17c44_0_661cfbba17c44_0_: finished. Total: 0.239 s, CPU [user: 0.144 s, system: 0.0161 s], Allocated memory: 18.4 MB, resolve: 0.067 s [63% User (2x), 35% Project (1x)] (5x), svn: 0.0209 s [36% getLatestRevision (2x), 23% log (1x), 22% testConnection (1x)] (8x), ObjectMaps: 0.0175 s [53% getPrimaryObjectProperty (2x), 36% getPrimaryObjectLocation (2x)] (11x), Lucene: 0.017 s [100% search (1x)] (1x)
2025-08-04 15:14:54,463 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.71 s, CPU [user: 0.00631 s, system: 0.00143 s], Allocated memory: 313.1 kB, transactions: 1
2025-08-04 15:14:54,464 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.251 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.0814 s [66% User (3x), 29% Project (1x)] (7x), persistence listener: 0.0382 s [64% TestRunActivityCreator (1x), 27% indexRefreshPersistenceListener (1x)] (7x), Incremental Baseline: 0.0332 s [100% WorkItem (25x)] (25x), svn: 0.0319 s [42% getLatestRevision (3x), 30% testConnection (2x), 15% log (1x)] (10x), Lucene: 0.0301 s [56% search (1x), 29% add (1x)] (3x), ObjectMaps: 0.0205 s [46% getPrimaryObjectProperty (2x), 46% getPrimaryObjectLocation (3x)] (12x)
2025-08-04 15:14:54,465 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.837 s, CPU [user: 0.161 s, system: 0.0245 s], Allocated memory: 19.6 MB, transactions: 27, svn: 0.634 s [98% getDatedRevision (181x)] (183x), Lucene: 0.045 s [73% buildBaselineSnapshots (1x), 27% buildBaseline (26x)] (27x)
2025-08-04 15:14:54,825 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfbba18045_0_661cfbba18045_0_: finished. Total: 1.16 s, CPU [user: 0.342 s, system: 0.0894 s], Allocated memory: 51.2 MB, svn: 0.7 s [47% getDatedRevision (181x), 33% getDir2 content (25x)] (328x), resolve: 0.389 s [100% Category (117x)] (117x), ObjectMaps: 0.135 s [42% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 24% getLastPromoted (117x)] (472x)
2025-08-04 15:14:55,015 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfbbb4c848_0_661cfbbb4c848_0_: finished. Total: 0.116 s, CPU [user: 0.0589 s, system: 0.00919 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0539 s [52% getReadConfiguration (180x), 48% getReadUserConfiguration (10x)] (190x), svn: 0.0482 s [57% info (21x), 35% getFile content (16x)] (39x), resolve: 0.0339 s [100% User (9x)] (9x), ObjectMaps: 0.0175 s [56% getPrimaryObjectProperty (8x), 26% getLastPromoted (8x)] (32x)
2025-08-04 15:14:55,210 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfbbb73c4a_0_661cfbbb73c4a_0_: finished. Total: 0.154 s, CPU [user: 0.0573 s, system: 0.00518 s], Allocated memory: 19.7 MB, svn: 0.11 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), RepositoryConfigService: 0.0684 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 15:14:56,035 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfbbb9a84b_0_661cfbbb9a84b_0_: finished. Total: 0.825 s, CPU [user: 0.388 s, system: 0.0205 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.638 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.417 s [69% getFile content (412x), 31% getDir2 content (21x)] (434x)
2025-08-04 15:14:56,175 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfbbc6904c_0_661cfbbc6904c_0_: finished. Total: 0.139 s, CPU [user: 0.0255 s, system: 0.00438 s], Allocated memory: 18.1 MB, svn: 0.129 s [92% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0167 s [97% getReadConfiguration (124x)] (148x)
2025-08-04 15:14:56,507 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfbbc9344e_0_661cfbbc9344e_0_: finished. Total: 0.302 s, CPU [user: 0.126 s, system: 0.00895 s], Allocated memory: 388.0 MB, RepositoryConfigService: 0.201 s [96% getReadConfiguration (2788x)] (3026x), svn: 0.186 s [55% getFile content (186x), 45% getDir2 content (21x)] (208x)
2025-08-04 15:14:56,587 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.93 s, CPU [user: 1.08 s, system: 0.15 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.73 s [41% getDir2 content (133x), 37% getFile content (865x), 19% getDatedRevision (181x)] (1222x), RepositoryConfigService: 1.05 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.463 s [84% Category (117x)] (139x), ObjectMaps: 0.171 s [45% getPrimaryObjectProperty (130x), 32% getPrimaryObjectLocation (136x), 23% getLastPromoted (130x)] (531x)
2025-08-04 15:14:56,587 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 2.36 s [40% getDatedRevision (362x), 30% getDir2 content (133x), 27% getFile content (865x)] (1405x), RepositoryConfigService: 1.05 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.463 s [84% Category (117x)] (140x), ObjectMaps: 0.171 s [45% getPrimaryObjectProperty (130x), 32% getPrimaryObjectLocation (136x), 23% getLastPromoted (130x)] (531x)
2025-08-04 15:15:19,537 [ajp-nio-127.0.0.1-8889-exec-2 | cID:73ef4a63-c0a844bd-4b0f130f-4eb01eb0 | u:admin | PUT:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test] INFO  TXLOGGER - Tx 661cfbd328c52_0_661cfbd328c52_0_: finished. Total: 0.206 s, CPU [user: 0.0367 s, system: 0.0143 s], Allocated memory: 5.4 MB, svn: 0.134 s [96% endActivity (1x)] (5x), PullingJob Listeners: 0.0502 s [97% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0485 s [99% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0482 s [99% objectsCreated (1x)] (3x), Lucene: 0.0108 s [100% add (1x)] (1x), DB: 0.0105 s [62% update (4x), 18% query (1x), 11% commit (2x)] (10x), processed revs: [290, 290 (delay 0s)], write: true
2025-08-04 15:15:19,554 [ajp-nio-127.0.0.1-8889-exec-2 | cID:73ef4a63-c0a844bd-4b0f130f-4eb01eb0] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test': Total: 0.798 s, CPU [user: 0.333 s, system: 0.101 s], Allocated memory: 61.8 MB, transactions: 3, PolarionAuthenticator: 0.388 s [100% authenticate (1x)] (1x), svn: 0.163 s [79% endActivity (1x), 10% testConnection (1x)] (10x), PullingJob Listeners: 0.0502 s [97% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0485 s [99% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0482 s [99% objectsCreated (1x)] (3x)
2025-08-04 15:15:19,673 [DBNormalHistoryCreator | u:p] INFO  TXLOGGER - Tx command #0: finished. Total: 0.128 s, CPU [user: 0.0153 s, system: 0.00655 s], Allocated memory: 8.5 MB, svn: 0.0442 s [78% getLatestRevision (2x), 22% testConnection (1x)] (3x), Lucene: 0.0433 s [100% refresh (1x)] (1x), DB: 0.0207 s [74% update (2x), 22% execute (1x)] (4x)
2025-08-04 15:15:19,793 [ajp-nio-127.0.0.1-8889-exec-4 | cID:73ef4dab-c0a844bd-4b0f130f-dd3e2aa6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754290577593': Total: 0.195 s, CPU [user: 0.0419 s, system: 0.00879 s], Allocated memory: 4.1 MB, transactions: 1, RepositoryConfigService: 0.146 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 15:15:19,794 [ajp-nio-127.0.0.1-8889-exec-3 | cID:73ef4da3-c0a844bd-4b0f130f-3764354d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754290577592': Total: 0.206 s, CPU [user: 0.073 s, system: 0.0124 s], Allocated memory: 5.5 MB, transactions: 1, RepositoryConfigService: 0.0992 s [100% getReadConfiguration (1x)] (1x), svn: 0.0253 s [66% testConnection (1x), 26% getFile content (2x)] (5x)
2025-08-04 15:15:22,848 [ajp-nio-127.0.0.1-8889-exec-1 | cID:73ef54d8-c0a844bd-4b0f130f-125314b5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.41 s, CPU [user: 0.145 s, system: 0.0292 s], Allocated memory: 9.9 MB, transactions: 0
2025-08-04 15:15:23,021 [ajp-nio-127.0.0.1-8889-exec-10 | cID:73ef54da-c0a844bd-4b0f130f-a16a91d1 | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661cfbd56d45b_0_661cfbd56d45b_0_: finished. Total: 1.37 s, CPU [user: 0.527 s, system: 0.11 s], Allocated memory: 240.3 MB, ObjectEnum Document: 1.26 s [100% available (6x)] (6x), resolve: 0.987 s [99% Module (97x)] (802x), svn: 0.566 s [49% info (92x), 33% getDir2 content (46x)] (185x), ModulePageParser: 0.198 s [100% parsePage (46x)] (46x), DB: 0.158 s [52% query (194x), 26% update (194x), 22% commit (194x)] (5917x)
2025-08-04 15:15:23,023 [ajp-nio-127.0.0.1-8889-exec-10 | cID:73ef54da-c0a844bd-4b0f130f-a16a91d1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.59 s, CPU [user: 0.622 s, system: 0.127 s], Allocated memory: 264.9 MB, transactions: 3, ObjectEnum Document: 1.26 s [100% available (6x)] (6x), resolve: 0.987 s [99% Module (97x)] (803x), svn: 0.575 s [49% info (92x), 33% getDir2 content (46x)] (189x), ModulePageParser: 0.198 s [100% parsePage (46x)] (46x), DB: 0.158 s [52% query (194x), 26% update (194x), 22% commit (194x)] (5917x)
2025-08-04 15:15:27,669 [ajp-nio-127.0.0.1-8889-exec-5 | cID:73ef5a69-c0a844bd-4b0f130f-286a4008] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/f281c2f7-8f64-406d-b25d-f23fea2ed38d/metadata': Total: 4.81 s, CPU [user: 0.102 s, system: 0.0123 s], Allocated memory: 9.7 MB, transactions: 0
2025-08-04 15:17:07,863 [ajp-nio-127.0.0.1-8889-exec-3 | cID:73f0f3a2-c0a844bd-4b0f130f-23d9c5f2 | u:admin | PUT:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test] INFO  TXLOGGER - Tx 661cfc3cf945d_0_661cfc3cf945d_0_: finished. Total: 0.176 s, CPU [user: 0.0172 s, system: 0.0157 s], Allocated memory: 2.9 MB, svn: 0.111 s [92% endActivity (1x)] (5x), PullingJob Listeners: 0.0416 s [97% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.04 s [100% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.04 s [98% objectsCreated (1x)] (3x), DB: 0.0234 s [62% query (1x), 30% update (4x)] (10x), SubterraURITable: 0.0221 s [100% addIfNotExistsDB (1x)] (1x), processed revs: [291, 291 (delay 0s)], write: true
2025-08-04 15:17:07,864 [ajp-nio-127.0.0.1-8889-exec-3 | cID:73f0f3a2-c0a844bd-4b0f130f-23d9c5f2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test': Total: 0.246 s, CPU [user: 0.036 s, system: 0.0412 s], Allocated memory: 6.4 MB, transactions: 1, svn: 0.128 s [79% endActivity (1x), 15% info (2x)] (7x), PullingJob Listeners: 0.0416 s [97% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.04 s [100% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.04 s [98% objectsCreated (1x)] (3x), DB: 0.0234 s [62% query (1x), 30% update (4x)] (10x), SubterraURITable: 0.0221 s [100% addIfNotExistsDB (1x)] (1x)
2025-08-04 15:17:08,036 [ajp-nio-127.0.0.1-8889-exec-7 | cID:73f0f4d4-c0a844bd-4b0f130f-5e1ab39b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754290577595': Total: 0.111 s, CPU [user: 0.0308 s, system: 0.00879 s], Allocated memory: 4.0 MB, transactions: 1, RepositoryConfigService: 0.0943 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 15:17:11,261 [ajp-nio-127.0.0.1-8889-exec-2 | cID:73f10177-c0a844bd-4b0f130f-c0c43c79] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test/execute': Total: 0.101 s, CPU [user: 0.0393 s, system: 0.0208 s], Allocated memory: 5.4 MB, transactions: 0
2025-08-04 15:17:12,193 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661cfc4086c65_0_661cfc4086c65_0_: finished. Total: 0.869 s, CPU [user: 0.0999 s, system: 0.0384 s], Allocated memory: 101.9 MB, ObjectEnum Document: 0.84 s [100% available (6x)] (6x), DB: 0.291 s [61% query (194x), 20% update (194x)] (5917x), resolve: 0.0952 s [95% Module (97x)] (802x), GlobalHandler: 0.0842 s [100% get (1242x)] (1244x), EHCache: 0.0824 s [100% GET (802x)] (802x)
2025-08-04 15:17:16,189 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661cfc452e067_0_661cfc452e067_0_: finished. Total: 0.101 s, CPU [user: 0.0392 s, system: 0.00746 s], Allocated memory: 5.0 MB, resolve: 0.0605 s [100% WorkItem (1x)] (1x), svn: 0.0404 s [46% info (4x), 14% testConnection (1x), 13% getDir2 content (1x), 12% log (1x)] (10x), RepositoryConfigService: 0.0145 s [52% getExistingPrefixes (1x), 48% getReadConfiguration (13x)] (14x), ObjectMaps: 0.0111 s [61% getPrimaryObjectProperty (1x), 25% getPrimaryObjectLocation (1x)] (4x)
2025-08-04 15:19:55,257 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 164 s, CPU [user: 0.691 s, system: 0.107 s], Allocated memory: 118.4 MB, transactions: 3, SynchronizationTask: 159 s [100% Update FEISHU-test items (1x)] (10x)
2025-08-04 15:20:09,619 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.1 s, CPU [user: 0.002 s, system: 0.00628 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.0969 s [100% collectChanges (1x)] (1x), svn: 0.0966 s [100% getLatestRevision (1x)] (1x)
