2025-08-04 15:20:34,856 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0621 s [72% update (144x), 28% query (12x)] (221x), svn: 0.012 s [46% getLatestRevision (2x), 40% testConnection (1x)] (4x)
2025-08-04 15:20:34,960 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0325 s [62% getDir2 content (2x), 30% info (3x)] (6x)
2025-08-04 15:20:35,653 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.69 s, CPU [user: 0.217 s, system: 0.352 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.118 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 15:20:35,653 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.69 s, CPU [user: 0.19 s, system: 0.292 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.13 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 15:20:35,653 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.69 s, CPU [user: 0.0456 s, system: 0.103 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0562 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 15:20:35,653 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.691 s, CPU [user: 0.0779 s, system: 0.162 s], Allocated memory: 14.5 MB, transactions: 0, ObjectMaps: 0.0681 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0475 s [77% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-08-04 15:20:35,653 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.0603 s, system: 0.104 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0692 s [80% log2 (10x)] (13x), ObjectMaps: 0.0525 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 15:20:35,653 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.69 s, CPU [user: 0.129 s, system: 0.221 s], Allocated memory: 26.1 MB, transactions: 0, svn: 0.0879 s [30% info (5x), 27% log (1x), 23% log2 (5x), 8% getLatestRevision (2x)] (18x), ObjectMaps: 0.0757 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 15:20:35,654 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.5 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.266 s [57% log2 (36x), 12% getLatestRevision (9x), 10% info (5x), 10% testConnection (6x)] (61x)
2025-08-04 15:20:35,892 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.203 s [100% getReadConfiguration (48x)] (48x), svn: 0.0734 s [85% info (18x)] (38x)
2025-08-04 15:20:36,189 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.224 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.172 s [100% getReadConfiguration (94x)] (94x)
2025-08-04 15:20:36,412 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.106 s, CPU [user: 0.0441 s, system: 0.00889 s], Allocated memory: 10.8 MB
2025-08-04 15:20:36,492 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.276 s [100% doFinishStartup (1x)] (1x), DB: 0.0508 s [53% update (45x), 17% query (20x), 17% execute (15x)] (122x), Lucene: 0.0416 s [100% refresh (2x)] (2x), commit: 0.0413 s [73% Revision (1x), 27% BuildArtifact (1x)] (2x), SubterraURITable: 0.0237 s [100% addIfNotExistsDB (20x)] (20x), resolve: 0.0192 s [82% BuildArtifact (11x)] (13x), GlobalHandler: 0.0142 s [90% put (13x)] (26x)
2025-08-04 15:20:38,741 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.294 s [91% info (158x)] (168x)
2025-08-04 15:20:38,957 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661cfd0b16845_0_661cfd0b16845_0_: finished. Total: 0.21 s, CPU [user: 0.121 s, system: 0.0151 s], Allocated memory: 17.1 MB, resolve: 0.069 s [53% User (2x), 44% Project (1x)] (5x), Lucene: 0.0255 s [100% search (1x)] (1x), svn: 0.0224 s [30% getLatestRevision (2x), 21% log (1x), 21% testConnection (1x), 18% info (1x)] (8x), ObjectMaps: 0.0194 s [47% getPrimaryObjectLocation (2x), 44% getPrimaryObjectProperty (2x)] (11x)
2025-08-04 15:20:39,443 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.629 s, CPU [user: 0.00503 s, system: 0.00147 s], Allocated memory: 501.1 kB, transactions: 1
2025-08-04 15:20:39,443 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.628 s, CPU [user: 0.00401 s, system: 0.0016 s], Allocated memory: 308.9 kB, transactions: 1
2025-08-04 15:20:39,444 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 73, notification worker: 0.229 s [95% RevisionActivityCreator (18x)] (54x), resolve: 0.0836 s [60% User (3x), 37% Project (1x)] (7x), Lucene: 0.053 s [48% search (1x), 33% add (1x)] (4x), Incremental Baseline: 0.0227 s [100% WorkItem (24x)] (24x), ObjectMaps: 0.0226 s [55% getPrimaryObjectLocation (3x), 38% getPrimaryObjectProperty (2x)] (12x), svn: 0.0224 s [30% getLatestRevision (2x), 21% log (1x), 21% testConnection (1x), 18% info (1x)] (8x), Full Baseline: 0.0176 s [100% WorkItem (2x)] (2x), persistence listener: 0.0124 s [85% indexRefreshPersistenceListener (9x)] (63x)
2025-08-04 15:20:39,444 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.706 s, CPU [user: 0.148 s, system: 0.0212 s], Allocated memory: 19.1 MB, transactions: 26, svn: 0.549 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0511 s [84% buildBaselineSnapshots (1x)] (27x)
2025-08-04 15:20:39,762 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfd0b16040_0_661cfd0b16040_0_: finished. Total: 1.02 s, CPU [user: 0.275 s, system: 0.0767 s], Allocated memory: 51.6 MB, svn: 0.604 s [49% getDatedRevision (181x), 33% getDir2 content (25x)] (328x), resolve: 0.303 s [100% Category (117x)] (117x), ObjectMaps: 0.108 s [42% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 24% getLastPromoted (117x)] (472x)
2025-08-04 15:20:40,181 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfd0c48872_0_661cfd0c48872_0_: finished. Total: 0.211 s, CPU [user: 0.0604 s, system: 0.00857 s], Allocated memory: 19.8 MB, svn: 0.173 s [80% getDir2 content (17x)] (62x), RepositoryConfigService: 0.056 s [98% getReadConfiguration (170x)] (192x), GC: 0.025 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 15:20:40,815 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfd0c7d473_0_661cfd0c7d473_0_: finished. Total: 0.633 s, CPU [user: 0.318 s, system: 0.0244 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.453 s [96% getReadConfiguration (8682x)] (9021x), svn: 0.314 s [60% getFile content (412x), 40% getDir2 content (21x)] (434x)
2025-08-04 15:20:41,141 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661cfd0d33076_0_661cfd0d33076_0_: finished. Total: 0.233 s, CPU [user: 0.0935 s, system: 0.00602 s], Allocated memory: 384.4 MB, svn: 0.148 s [51% getFile content (186x), 48% getDir2 content (21x)] (208x), RepositoryConfigService: 0.147 s [97% getReadConfiguration (2788x)] (3026x)
2025-08-04 15:20:41,212 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.47 s, CPU [user: 0.887 s, system: 0.137 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.47 s [45% getDir2 content (133x), 31% getFile content (865x), 20% getDatedRevision (181x)] (1222x), RepositoryConfigService: 0.777 s [93% getReadConfiguration (12166x)] (12860x), resolve: 0.37 s [82% Category (117x)] (139x), ObjectMaps: 0.137 s [45% getPrimaryObjectProperty (130x), 32% getPrimaryObjectLocation (136x), 23% getLastPromoted (130x)] (531x)
2025-08-04 15:20:41,212 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 63, svn: 2.03 s [41% getDatedRevision (362x), 33% getDir2 content (133x), 23% getFile content (865x)] (1407x), RepositoryConfigService: 0.777 s [93% getReadConfiguration (12166x)] (12860x), resolve: 0.37 s [82% Category (117x)] (140x), ObjectMaps: 0.137 s [45% getPrimaryObjectProperty (130x), 32% getPrimaryObjectLocation (136x), 23% getLastPromoted (130x)] (531x)
2025-08-04 15:21:23,666 [ajp-nio-127.0.0.1-8889-exec-2 | cID:73f4d3c1-c0a844bd-272193ee-61af5d29 | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661cfd35a787e_0_661cfd35a787e_0_: finished. Total: 1.33 s, CPU [user: 0.572 s, system: 0.0892 s], Allocated memory: 241.2 MB, ObjectEnum Document: 1.15 s [100% available (6x)] (6x), resolve: 0.885 s [99% Module (97x)] (802x), svn: 0.457 s [48% info (92x), 33% getDir2 content (46x)] (185x), ModulePageParser: 0.228 s [100% parsePage (46x)] (46x), DB: 0.151 s [53% query (194x), 28% update (194x)] (5917x), GlobalHandler: 0.126 s [94% applyTxChanges (2x)] (1193x), RepositoryConfigService: 0.0682 s [100% getReadConfiguration (463x)] (464x)
2025-08-04 15:21:23,676 [ajp-nio-127.0.0.1-8889-exec-2 | cID:73f4d3c1-c0a844bd-272193ee-61af5d29] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 2.07 s, CPU [user: 0.742 s, system: 0.162 s], Allocated memory: 278.8 MB, transactions: 5, ObjectEnum Document: 1.15 s [100% available (6x)] (6x), resolve: 0.887 s [99% Module (97x)] (804x), svn: 0.476 s [46% info (92x), 32% getDir2 content (46x), 21% getFile content (49x)] (191x), PolarionAuthenticator: 0.431 s [100% authenticate (1x)] (1x), ModulePageParser: 0.228 s [100% parsePage (46x)] (46x), DB: 0.151 s [53% query (194x), 28% update (194x)] (5917x), GlobalHandler: 0.127 s [93% applyTxChanges (2x)] (1200x)
2025-08-04 15:21:28,151 [ajp-nio-127.0.0.1-8889-exec-3 | cID:73f4d3c1-c0a844bd-272193ee-d320704a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 6.55 s, CPU [user: 0.376 s, system: 0.0835 s], Allocated memory: 56.7 MB, transactions: 3, PolarionAuthenticator: 0.431 s [100% authenticate (1x)] (1x)
2025-08-04 15:21:44,043 [ajp-nio-127.0.0.1-8889-exec-5 | cID:73f5173a-c0a844bd-272193ee-38595f2c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 5.17 s, CPU [user: 0.0234 s, system: 0.0406 s], Allocated memory: 2.8 MB, transactions: 0
2025-08-04 15:22:10,401 [ajp-nio-127.0.0.1-8889-exec-10 | cID:73f58ead-c0a844bd-272193ee-fdbeded2 | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661cfd63c5c80_0_661cfd63c5c80_0_: finished. Total: 0.841 s, CPU [user: 0.111 s, system: 0.067 s], Allocated memory: 104.4 MB, ObjectEnum Document: 0.728 s [100% available (6x)] (6x), DB: 0.221 s [49% query (194x), 28% update (194x), 23% commit (194x)] (5917x), resolve: 0.107 s [91% Module (97x)] (802x), GlobalHandler: 0.0958 s [100% get (1242x)] (1244x), EHCache: 0.092 s [100% GET (802x)] (802x)
2025-08-04 15:22:10,403 [ajp-nio-127.0.0.1-8889-exec-10 | cID:73f58ead-c0a844bd-272193ee-fdbeded2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 0.949 s, CPU [user: 0.134 s, system: 0.0865 s], Allocated memory: 107.0 MB, transactions: 2, ObjectEnum Document: 0.728 s [100% available (6x)] (6x), DB: 0.221 s [49% query (194x), 28% update (194x), 23% commit (194x)] (5917x), resolve: 0.107 s [91% Module (97x)] (803x), GlobalHandler: 0.096 s [100% get (1245x)] (1247x), EHCache: 0.0921 s [100% GET (803x)] (803x)
2025-08-04 15:22:14,663 [ajp-nio-127.0.0.1-8889-exec-1 | cID:73f58eb6-c0a844bd-272193ee-cc4792fa] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 5.2 s, CPU [user: 0.0211 s, system: 0.016 s], Allocated memory: 2.5 MB, transactions: 0
2025-08-04 15:23:27,874 [ajp-nio-127.0.0.1-8889-exec-4 | cID:73f61d0b-c0a844bd-272193ee-bf6037b6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 42 s, CPU [user: 0.441 s, system: 0.0539 s], Allocated memory: 3.1 MB, transactions: 0
2025-08-04 15:23:32,656 [ajp-nio-127.0.0.1-8889-exec-3 | cID:73f6c11d-c0a844bd-272193ee-78186d73] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/16985b68-736f-4166-ad1f-0a8fff55082f/metadata': Total: 4.75 s, CPU [user: 0.0922 s, system: 0.0221 s], Allocated memory: 9.9 MB, transactions: 0
2025-08-04 15:24:10,309 [ajp-nio-127.0.0.1-8889-exec-5 | cID:73f76593-c0a844bd-272193ee-e9ab3150 | u:admin | PUT:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test] INFO  TXLOGGER - Tx 661cfdd978082_0_661cfdd978082_0_: finished. Total: 0.227 s, CPU [user: 0.0426 s, system: 0.024 s], Allocated memory: 5.3 MB, svn: 0.132 s [94% endActivity (1x)] (5x), PullingJob Listeners: 0.0458 s [96% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0439 s [100% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0436 s [99% objectsCreated (1x)] (3x), DB: 0.021 s [79% update (4x), 10% execute (1x)] (10x), SubterraURITable: 0.0175 s [100% addIfNotExistsDB (1x)] (1x), processed revs: [292, 292 (delay 0s)], write: true
2025-08-04 15:24:10,311 [ajp-nio-127.0.0.1-8889-exec-5 | cID:73f76593-c0a844bd-272193ee-e9ab3150] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test': Total: 0.307 s, CPU [user: 0.0689 s, system: 0.0525 s], Allocated memory: 9.2 MB, transactions: 1, svn: 0.15 s [82% endActivity (1x)] (8x), PullingJob Listeners: 0.0458 s [96% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0439 s [100% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0436 s [99% objectsCreated (1x)] (3x), DB: 0.021 s [79% update (4x), 10% execute (1x)] (10x), SubterraURITable: 0.0175 s [100% addIfNotExistsDB (1x)] (1x)
2025-08-04 15:24:12,596 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661cfddbb148a_0_661cfddbb148a_0_: finished. Total: 0.24 s, CPU [user: 0.056 s, system: 0.0289 s], Allocated memory: 39.0 MB, ObjectEnum Document: 0.168 s [100% available (6x)] (6x), DB: 0.129 s [50% query (194x), 26% update (194x), 24% commit (194x)] (5917x), resolve: 0.0139 s [61% Category (696x), 38% Module (97x)] (802x)
2025-08-04 15:24:48,256 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 35.9 s, CPU [user: 0.368 s, system: 0.0808 s], Allocated memory: 54.5 MB, transactions: 3, SynchronizationTask: 28.7 s [99% Update FEISHU-test items (1x)] (10x)
2025-08-04 15:25:22,683 [ajp-nio-127.0.0.1-8889-exec-4 | cID:73f880ed-c0a844bd-272193ee-f484e957] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/fileContent.jsp?url=WBSdev%2F.polarion%2Fsynchronizer%2Fconfiguration.xml': Total: 0.142 s, CPU [user: 0.0458 s, system: 0.0335 s], Allocated memory: 4.0 MB, transactions: 1
