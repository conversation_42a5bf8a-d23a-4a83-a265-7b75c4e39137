2025-08-04 15:59:35,995 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0416 s [55% update (144x), 45% query (12x)] (221x), svn: 0.0207 s [56% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-08-04 15:59:36,095 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0296 s [57% getDir2 content (2x), 34% info (3x)] (6x)
2025-08-04 15:59:36,919 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.82 s, CPU [user: 0.0653 s, system: 0.0766 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0961 s [76% log2 (10x), 19% getLatestRevision (2x)] (13x), ObjectMaps: 0.0638 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 15:59:36,919 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.819 s, CPU [user: 0.129 s, system: 0.214 s], Allocated memory: 26.3 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0883 s [25% log (1x), 25% log2 (5x), 24% info (5x), 15% getLatestRevision (2x)] (18x)
2025-08-04 15:59:36,919 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.82 s, CPU [user: 0.091 s, system: 0.145 s], Allocated memory: 14.6 MB, transactions: 0, ObjectMaps: 0.0929 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0676 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-08-04 15:59:36,919 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.82 s, CPU [user: 0.259 s, system: 0.345 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.177 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 15:59:36,919 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.819 s, CPU [user: 0.0482 s, system: 0.0879 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0768 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0596 s [81% log2 (5x)] (7x)
2025-08-04 15:59:36,919 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.82 s, CPU [user: 0.197 s, system: 0.272 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.131 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 15:59:36,920 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.644 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.344 s [62% log2 (36x), 16% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-08-04 15:59:37,197 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.212 s [100% getReadConfiguration (48x)] (48x), svn: 0.0747 s [86% info (18x)] (38x)
2025-08-04 15:59:37,552 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.281 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.211 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 15:59:37,782 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.119 s, CPU [user: 0.0216 s, system: 0.00661 s], Allocated memory: 8.5 MB
2025-08-04 15:59:37,843 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.275 s [100% doFinishStartup (1x)] (1x), commit: 0.0694 s [100% Revision (1x)] (1x), Lucene: 0.0468 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0168 s [100% objectsToInv (1x)] (1x)
2025-08-04 15:59:40,195 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.338 s [89% info (158x)] (168x)
2025-08-04 15:59:40,446 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661d05f9ac842_0_661d05f9ac842_0_: finished. Total: 0.235 s, CPU [user: 0.129 s, system: 0.0154 s], Allocated memory: 17.2 MB, resolve: 0.0746 s [66% User (2x), 31% Project (1x)] (5x), svn: 0.0227 s [37% getLatestRevision (2x), 27% testConnection (1x), 19% log (1x)] (8x), ObjectMaps: 0.0185 s [46% getPrimaryObjectLocation (2x), 42% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.0184 s [100% search (1x)] (1x)
2025-08-04 15:59:41,019 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.732 s, CPU [user: 0.00596 s, system: 0.00156 s], Allocated memory: 322.5 kB, transactions: 1
2025-08-04 15:59:41,019 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.246 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.0972 s [73% User (3x), 24% Project (1x)] (7x), svn: 0.0311 s [42% getLatestRevision (3x), 31% testConnection (2x), 14% log (1x)] (10x), ObjectMaps: 0.0293 s [66% getPrimaryObjectLocation (3x), 27% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0273 s [100% WorkItem (24x)] (24x), persistence listener: 0.0271 s [84% indexRefreshPersistenceListener (1x)] (7x), Lucene: 0.023 s [80% search (1x)] (2x)
2025-08-04 15:59:41,020 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.832 s, CPU [user: 0.165 s, system: 0.0252 s], Allocated memory: 19.6 MB, transactions: 27, svn: 0.63 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0675 s [81% buildBaselineSnapshots (1x)] (27x)
2025-08-04 15:59:41,352 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d05f9aa840_0_661d05f9aa840_0_: finished. Total: 1.15 s, CPU [user: 0.337 s, system: 0.0945 s], Allocated memory: 51.2 MB, svn: 0.671 s [46% getDatedRevision (181x), 33% getDir2 content (25x), 19% getFile content (119x)] (328x), resolve: 0.419 s [100% Category (117x)] (117x), ObjectMaps: 0.149 s [44% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (472x)
2025-08-04 15:59:41,533 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d05fadd848_0_661d05fadd848_0_: finished. Total: 0.102 s, CPU [user: 0.0489 s, system: 0.00854 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0451 s [50% getReadConfiguration (180x), 50% getReadUserConfiguration (10x)] (190x), svn: 0.0421 s [54% info (21x), 40% getFile content (16x)] (39x), resolve: 0.0319 s [100% User (9x)] (9x), ObjectMaps: 0.0156 s [54% getPrimaryObjectProperty (8x), 25% getLastPromoted (8x), 21% getPrimaryObjectLocation (8x)] (32x)
2025-08-04 15:59:41,756 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d05fb01c4a_0_661d05fb01c4a_0_: finished. Total: 0.181 s, CPU [user: 0.0658 s, system: 0.00613 s], Allocated memory: 19.8 MB, svn: 0.132 s [63% getDir2 content (17x), 37% getFile content (44x)] (62x), RepositoryConfigService: 0.0771 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 15:59:42,451 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d05fb2f44b_0_661d05fb2f44b_0_: finished. Total: 0.695 s, CPU [user: 0.329 s, system: 0.0143 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.495 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.375 s [59% getFile content (412x), 41% getDir2 content (21x)] (434x)
2025-08-04 15:59:42,775 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d05fbf604e_0_661d05fbf604e_0_: finished. Total: 0.223 s, CPU [user: 0.0936 s, system: 0.00518 s], Allocated memory: 384.4 MB, svn: 0.141 s [51% getDir2 content (21x), 49% getFile content (186x)] (208x), RepositoryConfigService: 0.138 s [96% getReadConfiguration (2788x)] (3026x)
2025-08-04 15:59:42,834 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.64 s, CPU [user: 0.967 s, system: 0.141 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.54 s [42% getDir2 content (133x), 35% getFile content (865x), 20% getDatedRevision (181x)] (1222x), RepositoryConfigService: 0.834 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.49 s [86% Category (117x)] (139x), ObjectMaps: 0.182 s [46% getPrimaryObjectProperty (130x), 32% getPrimaryObjectLocation (136x), 23% getLastPromoted (130x)] (531x)
2025-08-04 15:59:42,834 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 2.17 s [43% getDatedRevision (362x), 30% getDir2 content (133x), 25% getFile content (865x)] (1405x), RepositoryConfigService: 0.834 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.49 s [86% Category (117x)] (140x), ObjectMaps: 0.182 s [46% getPrimaryObjectProperty (130x), 32% getPrimaryObjectLocation (136x), 23% getLastPromoted (130x)] (531x), Lucene: 0.11 s [50% buildBaselineSnapshots (2x), 28% search (5x), 14% buildBaseline (52x)] (60x)
2025-08-04 15:59:44,753 [ajp-nio-127.0.0.1-8889-exec-2 | cID:7417f6e7-c0a844bd-7f853544-e5057d8c] INFO  TXLOGGER - Summary for 'servlet /polarion/': Total: 0.394 s, CPU [user: 0.231 s, system: 0.0404 s], Allocated memory: 36.4 MB, transactions: 2, PolarionAuthenticator: 0.373 s [100% authenticate (1x)] (1x)
2025-08-04 15:59:45,036 [ajp-nio-127.0.0.1-8889-exec-3 | cID:7417f8fa-c0a844bd-7f853544-abdf3b92] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/polarion.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.145 s, CPU [user: 0.0175 s, system: 0.0056 s], Allocated memory: 973.6 kB, transactions: 0, UICustomizationDataProvider: 0.104 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0849 s [100% readUserData (1x)] (1x)
2025-08-04 15:59:46,653 [ajp-nio-127.0.0.1-8889-exec-1 | cID:7417feb9-c0a844bd-7f853544-af43f138] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.292 s, CPU [user: 0.179 s, system: 0.0418 s], Allocated memory: 66.7 MB, transactions: 1, RPC: 0.18 s [50% encodeResponse (1x), 32% decodeRequest (1x)] (4x), PortalDataService: 0.0863 s [100% getInitData (1x)] (1x), RepositoryConfigService: 0.0219 s [100% getReadConfiguration (10x)] (15x), svn: 0.0154 s [52% getDir2 content (1x), 32% testConnection (1x)] (4x)
2025-08-04 15:59:46,868 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74180006-c0a844bd-7f853544-4046a73b | u:admin] INFO  TXLOGGER - Tx 661d060003c53_0_661d060003c53_0_: finished. Total: 0.165 s, CPU [user: 0.083 s, system: 0.0185 s], Allocated memory: 14.6 MB, RepositoryConfigService: 0.0503 s [94% getReadConfiguration (13x)] (18x), svn: 0.036 s [40% getDir2 content (1x), 23% getFile content (6x), 23% info (4x)] (13x)
2025-08-04 15:59:46,878 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74180006-c0a844bd-7f853544-4046a73b] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.184 s, CPU [user: 0.0963 s, system: 0.0221 s], Allocated memory: 17.8 MB, transactions: 1, PortalDataService: 0.165 s [100% requestPortalSite (1x)] (1x), RepositoryConfigService: 0.0503 s [94% getReadConfiguration (13x)] (18x), svn: 0.036 s [40% getDir2 content (1x), 23% getFile content (6x), 23% info (4x)] (13x), RPC: 0.0166 s [58% encodeResponse (1x), 37% decodeRequest (1x)] (4x)
2025-08-04 15:59:47,442 [ajp-nio-127.0.0.1-8889-exec-8 | cID:74180245-c0a844bd-7f853544-1af70ef3] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.172 s, CPU [user: 0.0604 s, system: 0.0105 s], Allocated memory: 9.6 MB, transactions: 0, GC: 0.068 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 15:59:47,442 [ajp-nio-127.0.0.1-8889-exec-10 | cID:7418025d-c0a844bd-7f853544-7bffa7e5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.149 s, CPU [user: 0.0134 s, system: 0.00426 s], Allocated memory: 1.5 MB, transactions: 0, GC: 0.068 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 15:59:47,458 [ajp-nio-127.0.0.1-8889-exec-7 | cID:7418025d-c0a844bd-7f853544-572fa622] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1754294387240': Total: 0.165 s, CPU [user: 0.0326 s, system: 0.00603 s], Allocated memory: 2.0 MB, transactions: 0, GC: 0.068 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 15:59:47,483 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74180262-c0a844bd-7f853544-fbdcbfc6 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754294387241] INFO  TXLOGGER - Tx 661d0600a5c5e_0_661d0600a5c5e_0_: finished. Total: 0.132 s, CPU [user: 0.0373 s, system: 0.00647 s], Allocated memory: 5.5 MB, GC: 0.068 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 15:59:47,506 [ajp-nio-127.0.0.1-8889-exec-4 | cID:74180262-c0a844bd-7f853544-16a451e7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754294387242': Total: 0.208 s, CPU [user: 0.0268 s, system: 0.00485 s], Allocated memory: 4.7 MB, transactions: 1, RepositoryConfigService: 0.151 s [100% getReadConfiguration (1x)] (1x), GC: 0.068 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 15:59:47,506 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74180262-c0a844bd-7f853544-fbdcbfc6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754294387241': Total: 0.208 s, CPU [user: 0.0653 s, system: 0.0111 s], Allocated memory: 8.6 MB, transactions: 1, RepositoryConfigService: 0.133 s [100% getReadConfiguration (1x)] (1x), GC: 0.068 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 15:59:50,623 [ajp-nio-127.0.0.1-8889-exec-7 | cID:74180a3b-c0a844bd-7f853544-34c0d90e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.32 s, CPU [user: 0.146 s, system: 0.0179 s], Allocated memory: 10.2 MB, transactions: 0
2025-08-04 15:59:50,864 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74180a3b-c0a844bd-7f853544-e82b0074 | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661d0602bcc60_0_661d0602bcc60_0_: finished. Total: 1.37 s, CPU [user: 0.538 s, system: 0.111 s], Allocated memory: 233.9 MB, ObjectEnum Document: 1.27 s [100% available (6x)] (6x), resolve: 0.927 s [99% Module (97x)] (802x), svn: 0.531 s [50% info (92x), 33% getDir2 content (46x)] (185x), DB: 0.227 s [54% query (194x), 26% update (194x)] (5917x), ModulePageParser: 0.213 s [100% parsePage (46x)] (46x)
2025-08-04 15:59:50,866 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74180a3b-c0a844bd-7f853544-e82b0074] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.56 s, CPU [user: 0.635 s, system: 0.124 s], Allocated memory: 257.4 MB, transactions: 2, ObjectEnum Document: 1.27 s [100% available (6x)] (6x), resolve: 0.927 s [99% Module (97x)] (803x), svn: 0.531 s [50% info (92x), 33% getDir2 content (46x)] (185x), DB: 0.227 s [54% query (194x), 26% update (194x)] (5917x), ModulePageParser: 0.213 s [100% parsePage (46x)] (46x)
