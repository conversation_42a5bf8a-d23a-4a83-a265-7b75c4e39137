2025-08-04 16:02:06,745 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0617 s [69% update (144x), 31% query (12x)] (221x), svn: 0.0146 s [46% getLatestRevision (2x), 42% testConnection (1x)] (4x)
2025-08-04 16:02:06,858 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0337 s [55% getDir2 content (2x), 37% info (3x)] (6x)
2025-08-04 16:02:07,563 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.702 s, CPU [user: 0.0937 s, system: 0.162 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0959 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0513 s [79% log2 (10x), 13% getLatestRevision (2x)] (13x)
2025-08-04 16:02:07,563 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.702 s, CPU [user: 0.0511 s, system: 0.0883 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.049 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0407 s [80% log2 (5x)] (7x)
2025-08-04 16:02:07,563 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.702 s, CPU [user: 0.0656 s, system: 0.0829 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0824 s [77% log2 (10x), 18% getLatestRevision (2x)] (13x), ObjectMaps: 0.045 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 16:02:07,564 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.702 s, CPU [user: 0.128 s, system: 0.216 s], Allocated memory: 26.4 MB, transactions: 0, ObjectMaps: 0.0759 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0701 s [29% info (5x), 24% log (1x), 23% log2 (5x), 9% getLatestRevision (2x)] (18x)
2025-08-04 16:02:07,564 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.196 s, system: 0.274 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.0996 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 16:02:07,563 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.702 s, CPU [user: 0.234 s, system: 0.334 s], Allocated memory: 70.5 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 16:02:07,565 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.486 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.284 s [61% log2 (36x), 14% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-04 16:02:07,806 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.205 s [100% getReadConfiguration (48x)] (48x), svn: 0.076 s [83% info (18x)] (38x)
2025-08-04 16:02:08,079 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.211 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.162 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 16:02:08,302 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.108 s, CPU [user: 0.0413 s, system: 0.00879 s], Allocated memory: 10.3 MB, GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 16:02:08,330 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.236 s [100% doFinishStartup (1x)] (1x), commit: 0.0664 s [100% Revision (1x)] (1x), Lucene: 0.0327 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.015 s [100% objectsToInv (1x)] (1x)
2025-08-04 16:02:10,740 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.329 s [90% info (158x)] (168x)
2025-08-04 16:02:11,038 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661d068cb0445_0_661d068cb0445_0_: finished. Total: 0.284 s, CPU [user: 0.136 s, system: 0.0203 s], Allocated memory: 17.1 MB, resolve: 0.0614 s [60% User (2x), 38% Project (1x)] (5x), Lucene: 0.0367 s [100% search (1x)] (1x), svn: 0.0198 s [34% getLatestRevision (2x), 23% testConnection (1x), 22% log (1x), 11% info (1x)] (8x), ObjectMaps: 0.017 s [48% getPrimaryObjectProperty (2x), 42% getPrimaryObjectLocation (2x)] (11x)
2025-08-04 16:02:11,547 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.0047 s, system: 0.00141 s], Allocated memory: 322.6 kB, transactions: 1
2025-08-04 16:02:11,547 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 32, notification worker: 0.3 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.0811 s [68% User (3x), 28% Project (1x)] (7x), Lucene: 0.0462 s [79% search (1x), 16% add (1x)] (3x), svn: 0.0268 s [37% getLatestRevision (3x), 31% testConnection (2x), 16% log (1x)] (10x), Incremental Baseline: 0.0256 s [100% WorkItem (24x)] (24x), ObjectMaps: 0.0235 s [58% getPrimaryObjectLocation (3x), 35% getPrimaryObjectProperty (2x)] (12x), persistence listener: 0.0166 s [75% indexRefreshPersistenceListener (1x), 14% WorkItemActivityCreator (1x)] (7x)
2025-08-04 16:02:11,548 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.812 s, CPU [user: 0.16 s, system: 0.0248 s], Allocated memory: 19.1 MB, transactions: 26, svn: 0.6 s [97% getDatedRevision (181x)] (183x), Lucene: 0.0702 s [89% buildBaselineSnapshots (1x)] (27x)
2025-08-04 16:02:11,836 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d068caf040_0_661d068caf040_0_: finished. Total: 1.09 s, CPU [user: 0.308 s, system: 0.0891 s], Allocated memory: 51.6 MB, svn: 0.652 s [40% getDatedRevision (181x), 40% getDir2 content (25x)] (328x), resolve: 0.372 s [100% Category (117x)] (117x), ObjectMaps: 0.136 s [43% getPrimaryObjectProperty (117x), 32% getPrimaryObjectLocation (117x), 26% getLastPromoted (117x)] (472x)
2025-08-04 16:02:12,029 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d068dd2448_0_661d068dd2448_0_: finished. Total: 0.116 s, CPU [user: 0.0525 s, system: 0.01 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0517 s [52% getReadConfiguration (180x), 48% getReadUserConfiguration (10x)] (190x), svn: 0.0477 s [58% info (21x), 36% getFile content (16x)] (39x), resolve: 0.0377 s [100% User (9x)] (9x), ObjectMaps: 0.0215 s [48% getPrimaryObjectProperty (8x), 33% getLastPromoted (8x)] (32x)
2025-08-04 16:02:12,230 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d068df884a_0_661d068df884a_0_: finished. Total: 0.163 s, CPU [user: 0.0519 s, system: 0.00611 s], Allocated memory: 19.8 MB, svn: 0.109 s [69% getDir2 content (17x), 31% getFile content (44x)] (62x), RepositoryConfigService: 0.0702 s [98% getReadConfiguration (170x)] (192x), GC: 0.015 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 16:02:12,931 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d068e2184b_0_661d068e2184b_0_: finished. Total: 0.701 s, CPU [user: 0.331 s, system: 0.0263 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.524 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.372 s [64% getFile content (412x), 36% getDir2 content (21x)] (434x)
2025-08-04 16:02:13,277 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d068eee04e_0_661d068eee04e_0_: finished. Total: 0.229 s, CPU [user: 0.101 s, system: 0.00494 s], Allocated memory: 385.0 MB, RepositoryConfigService: 0.145 s [96% getReadConfiguration (2788x)] (3026x), svn: 0.14 s [50% getFile content (186x), 50% getDir2 content (21x)] (208x)
2025-08-04 16:02:13,334 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.59 s, CPU [user: 0.939 s, system: 0.15 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.52 s [45% getDir2 content (133x), 35% getFile content (865x), 17% getDatedRevision (181x)] (1222x), RepositoryConfigService: 0.866 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.451 s [83% Category (117x)] (139x), ObjectMaps: 0.178 s [44% getPrimaryObjectProperty (130x), 30% getPrimaryObjectLocation (136x), 26% getLastPromoted (130x)] (531x)
2025-08-04 16:02:13,335 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 63, svn: 2.12 s [40% getDatedRevision (362x), 32% getDir2 content (133x), 25% getFile content (865x)] (1405x), RepositoryConfigService: 0.866 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.451 s [83% Category (117x)] (140x), ObjectMaps: 0.178 s [44% getPrimaryObjectProperty (130x), 30% getPrimaryObjectLocation (136x), 26% getLastPromoted (130x)] (531x)
2025-08-04 16:02:24,188 [ajp-nio-127.0.0.1-8889-exec-4 | cID:741a65f7-c0a844bd-73e6d0e8-25992116] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/img/progress.gif': Total: 0.323 s, CPU [user: 0.138 s, system: 0.0235 s], Allocated memory: 29.8 MB, transactions: 2, PolarionAuthenticator: 0.319 s [100% authenticate (1x)] (1x)
2025-08-04 16:02:24,351 [ajp-nio-127.0.0.1-8889-exec-3 | cID:741a65e8-c0a844bd-73e6d0e8-62e5d339] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754294387244': Total: 0.502 s, CPU [user: 0.094 s, system: 0.0322 s], Allocated memory: 18.5 MB, transactions: 3, PolarionAuthenticator: 0.338 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0649 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 16:02:24,363 [ajp-nio-127.0.0.1-8889-exec-2 | cID:741a65e7-c0a844bd-73e6d0e8-24a893d8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754294387243': Total: 0.515 s, CPU [user: 0.103 s, system: 0.0247 s], Allocated memory: 16.6 MB, transactions: 3, PolarionAuthenticator: 0.329 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0777 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 16:02:28,057 [ajp-nio-127.0.0.1-8889-exec-9 | cID:741a7130-c0a844bd-73e6d0e8-fda491c2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.32 s, CPU [user: 0.13 s, system: 0.0165 s], Allocated memory: 10.2 MB, transactions: 0
2025-08-04 16:02:28,068 [ajp-nio-127.0.0.1-8889-exec-10 | cID:741a7130-c0a844bd-73e6d0e8-7a4704a4 | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661d069c79859_0_661d069c79859_0_: finished. Total: 1.15 s, CPU [user: 0.512 s, system: 0.0952 s], Allocated memory: 241.2 MB, ObjectEnum Document: 1.03 s [100% available (6x)] (6x), resolve: 0.786 s [99% Module (97x)] (802x), svn: 0.419 s [46% info (92x), 39% getDir2 content (46x)] (185x), ModulePageParser: 0.208 s [100% parsePage (46x)] (46x), DB: 0.128 s [52% query (194x), 26% update (194x), 21% commit (194x)] (5917x), GlobalHandler: 0.0638 s [86% applyTxChanges (2x)] (1193x)
2025-08-04 16:02:28,071 [ajp-nio-127.0.0.1-8889-exec-10 | cID:741a7130-c0a844bd-73e6d0e8-7a4704a4] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.33 s, CPU [user: 0.616 s, system: 0.112 s], Allocated memory: 267.0 MB, transactions: 3, ObjectEnum Document: 1.03 s [100% available (6x)] (6x), resolve: 0.786 s [99% Module (97x)] (803x), svn: 0.428 s [45% info (92x), 38% getDir2 content (46x)] (189x), ModulePageParser: 0.208 s [100% parsePage (46x)] (46x), DB: 0.128 s [52% query (194x), 26% update (194x), 21% commit (194x)] (5917x)
2025-08-04 16:03:00,212 [ajp-nio-127.0.0.1-8889-exec-1 | cID:741a7668-c0a844bd-73e6d0e8-240e17d1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/37b3705a-c69b-493b-b932-6917914c54b0/metadata': Total: 32.1 s, CPU [user: 0.167 s, system: 0.0411 s], Allocated memory: 10.2 MB, transactions: 0
2025-08-04 16:05:22,593 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d07475685b_0_661d07475685b_0_: finished. Total: 0.711 s, CPU [user: 0.0993 s, system: 0.0407 s], Allocated memory: 104.1 MB, ObjectEnum Document: 0.655 s [100% available (6x)] (6x), DB: 0.192 s [48% query (194x), 29% update (194x), 23% commit (194x)] (5917x), resolve: 0.0791 s [89% Module (97x)] (802x), GlobalHandler: 0.0671 s [100% get (1242x)] (1244x), EHCache: 0.064 s [100% GET (802x)] (802x)
2025-08-04 16:05:42,777 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d075b9345d_0_661d075b9345d_0_: finished. Total: 0.172 s, CPU [user: 0.054 s, system: 0.0229 s], Allocated memory: 5.0 MB, resolve: 0.0939 s [100% WorkItem (1x)] (1x), svn: 0.0619 s [44% info (4x), 18% testConnection (1x), 13% getLatestRevision (1x), 12% getDir2 content (1x)] (10x), RepositoryConfigService: 0.0241 s [65% getExistingPrefixes (1x), 35% getReadConfiguration (13x)] (14x), calculatedFieldsContributor: 0.0175 s [100% fields (1x)] (1x), ObjectMaps: 0.015 s [59% getPrimaryObjectProperty (1x), 28% getPrimaryObjectLocation (1x)] (4x)
2025-08-04 16:05:43,107 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 21.3 s, CPU [user: 0.296 s, system: 0.115 s], Allocated memory: 119.4 MB, transactions: 3
2025-08-04 16:15:09,015 [Worker-1: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d098485c5e_0_661d098485c5e_0_: finished. Total: 0.191 s, CPU [user: 0.0441 s, system: 0.0246 s], Allocated memory: 40.8 MB, ObjectEnum Document: 0.166 s [100% available (6x)] (6x), DB: 0.12 s [49% query (194x), 27% update (194x), 24% commit (194x)] (5917x)
2025-08-04 16:15:09,237 [Activities-Bulk-Publisher] INFO  TXLOGGER - Summary: Total: 0.111 s, CPU [user: 0.00382 s, system: 0.00391 s], Allocated memory: 993.1 kB, transactions: 0, Lucene: 0.111 s [100% add (1x)] (1x)
2025-08-04 16:16:54,721 [Worker-1: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 106 s, CPU [user: 0.134 s, system: 0.0936 s], Allocated memory: 49.7 MB, transactions: 3, SynchronizationTask: 96.6 s [100% Mapping created and updated items (1x)] (6x)
