2025-08-04 16:22:35,059 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0589 s [65% update (144x), 35% query (12x)] (221x), svn: 0.0232 s [57% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-08-04 16:22:35,162 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0298 s [62% getDir2 content (2x), 31% info (3x)] (6x)
2025-08-04 16:22:35,976 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.811 s, CPU [user: 0.0695 s, system: 0.0937 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0803 s [83% log2 (10x)] (13x), ObjectMaps: 0.0652 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 16:22:35,976 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.811 s, CPU [user: 0.221 s, system: 0.3 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.13 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 16:22:35,976 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.811 s, CPU [user: 0.128 s, system: 0.232 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0917 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 16:22:35,976 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.81 s, CPU [user: 0.0554 s, system: 0.101 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0616 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0454 s [75% log2 (5x), 14% testConnection (1x)] (7x)
2025-08-04 16:22:35,976 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.811 s, CPU [user: 0.0963 s, system: 0.169 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0852 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0795 s [79% log2 (10x), 13% getLatestRevision (2x)] (13x)
2025-08-04 16:22:35,976 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.81 s, CPU [user: 0.306 s, system: 0.372 s], Allocated memory: 72.6 MB, transactions: 0, ObjectMaps: 0.137 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0923 s [31% info (5x), 27% log2 (5x), 20% log (1x), 10% getLatestRevision (2x)] (18x)
2025-08-04 16:22:35,977 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.571 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.346 s [63% log2 (36x), 13% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-08-04 16:22:36,230 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.217 s [100% getReadConfiguration (48x)] (48x), svn: 0.0816 s [86% info (18x)] (38x)
2025-08-04 16:22:36,565 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.263 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.193 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 16:22:36,805 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.124 s, CPU [user: 0.0245 s, system: 0.00537 s], Allocated memory: 2.5 MB
2025-08-04 16:22:36,805 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.124 s, CPU [user: 0.0447 s, system: 0.00605 s], Allocated memory: 2.9 MB
2025-08-04 16:22:36,809 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.128 s, CPU [user: 0.0165 s, system: 0.00421 s], Allocated memory: 1.5 MB
2025-08-04 16:22:36,812 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.131 s, CPU [user: 0.00732 s, system: 0.00227 s], Allocated memory: 900.2 kB
2025-08-04 16:22:36,855 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.173 s, CPU [user: 0.0253 s, system: 0.00585 s], Allocated memory: 8.6 MB
2025-08-04 16:22:36,905 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.324 s [100% doFinishStartup (1x)] (1x), Lucene: 0.079 s [100% refresh (1x)] (1x), commit: 0.0739 s [100% Revision (1x)] (1x), DB: 0.0228 s [68% update (3x), 13% query (1x)] (8x)
2025-08-04 16:22:39,525 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.414 s [90% info (158x)] (168x)
2025-08-04 16:22:39,840 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661d0b3caac45_0_661d0b3caac45_0_: finished. Total: 0.309 s, CPU [user: 0.152 s, system: 0.0209 s], Allocated memory: 17.1 MB, resolve: 0.101 s [71% User (2x), 28% Project (1x)] (5x), ObjectMaps: 0.0287 s [47% getPrimaryObjectLocation (2x), 44% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.0248 s [100% search (1x)] (1x), svn: 0.024 s [43% getLatestRevision (2x), 19% testConnection (1x), 19% log (1x)] (8x)
2025-08-04 16:22:40,353 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.745 s, CPU [user: 0.00712 s, system: 0.00166 s], Allocated memory: 322.0 kB, transactions: 1
2025-08-04 16:22:40,356 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 32, notification worker: 0.316 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.118 s [75% User (3x), 24% Project (1x)] (7x), Lucene: 0.0413 s [60% search (1x), 25% add (1x)] (3x), svn: 0.0358 s [47% getLatestRevision (3x), 28% testConnection (2x), 12% log (1x)] (10x), ObjectMaps: 0.0325 s [53% getPrimaryObjectLocation (3x), 39% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0265 s [100% WorkItem (24x)] (24x)
2025-08-04 16:22:40,357 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.833 s, CPU [user: 0.156 s, system: 0.0275 s], Allocated memory: 19.1 MB, transactions: 26, svn: 0.632 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0701 s [89% buildBaselineSnapshots (1x)] (27x)
2025-08-04 16:22:40,781 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0b3caa842_0_661d0b3caa842_0_: finished. Total: 1.25 s, CPU [user: 0.345 s, system: 0.106 s], Allocated memory: 51.6 MB, svn: 0.751 s [51% getDatedRevision (181x), 31% getDir2 content (25x)] (328x), resolve: 0.446 s [100% Category (117x)] (117x), ObjectMaps: 0.182 s [36% getPrimaryObjectProperty (117x), 35% getPrimaryObjectLocation (117x), 29% getLastPromoted (117x)] (472x)
2025-08-04 16:22:40,994 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0b3df7048_0_661d0b3df7048_0_: finished. Total: 0.134 s, CPU [user: 0.0669 s, system: 0.0133 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0675 s [54% getReadConfiguration (180x), 46% getReadUserConfiguration (10x)] (190x), svn: 0.0596 s [56% info (21x), 36% getFile content (16x)] (39x), resolve: 0.0383 s [100% User (9x)] (9x), ObjectMaps: 0.0191 s [59% getPrimaryObjectProperty (8x), 21% getLastPromoted (8x)] (32x)
2025-08-04 16:22:41,267 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0b3e2144a_0_661d0b3e2144a_0_: finished. Total: 0.238 s, CPU [user: 0.0836 s, system: 0.0171 s], Allocated memory: 19.8 MB, svn: 0.16 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), RepositoryConfigService: 0.112 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 16:22:41,974 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0b3e5cc4b_0_661d0b3e5cc4b_0_: finished. Total: 0.706 s, CPU [user: 0.322 s, system: 0.0292 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.498 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.384 s [58% getFile content (412x), 42% getDir2 content (21x)] (434x)
2025-08-04 16:22:42,378 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0b3f25c4e_0_661d0b3f25c4e_0_: finished. Total: 0.307 s, CPU [user: 0.12 s, system: 0.00772 s], Allocated memory: 388.0 MB, RepositoryConfigService: 0.199 s [96% getReadConfiguration (2788x)] (3026x), svn: 0.198 s [54% getFile content (186x), 46% getDir2 content (21x)] (208x)
2025-08-04 16:22:42,466 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.94 s, CPU [user: 1.04 s, system: 0.188 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.76 s [41% getDir2 content (133x), 33% getFile content (865x), 22% getDatedRevision (181x)] (1222x), RepositoryConfigService: 0.963 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.521 s [86% Category (117x)] (139x), ObjectMaps: 0.217 s [40% getPrimaryObjectProperty (130x), 33% getPrimaryObjectLocation (136x), 27% getLastPromoted (130x)] (531x)
2025-08-04 16:22:42,466 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 63, svn: 2.39 s [42% getDatedRevision (362x), 30% getDir2 content (133x), 25% getFile content (865x)] (1405x), RepositoryConfigService: 0.963 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.521 s [86% Category (117x)] (140x), ObjectMaps: 0.217 s [40% getPrimaryObjectProperty (130x), 33% getPrimaryObjectLocation (136x), 27% getLastPromoted (130x)] (531x)
2025-08-04 16:23:16,932 [ajp-nio-127.0.0.1-8889-exec-2 | cID:742d8218-c0a844bd-5090eafd-f68e3d42] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test/execute': Total: 0.679 s, CPU [user: 0.292 s, system: 0.117 s], Allocated memory: 59.0 MB, transactions: 3, PolarionAuthenticator: 0.404 s [100% authenticate (1x)] (1x), GC: 0.058 s [100% G1 Young Generation (1x)] (1x), RepositoryConfigService: 0.054 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 16:23:18,189 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d0b6157c54_0_661d0b6157c54_0_: finished. Total: 1.1 s, CPU [user: 0.484 s, system: 0.0843 s], Allocated memory: 236.3 MB, ObjectEnum Document: 1.01 s [100% available (6x)] (6x), resolve: 0.758 s [99% Module (97x)] (802x), svn: 0.408 s [47% info (92x), 37% getDir2 content (46x)] (185x), ModulePageParser: 0.188 s [100% parsePage (46x)] (46x), DB: 0.147 s [52% query (194x), 26% update (194x), 22% commit (194x)] (5917x), GlobalHandler: 0.0565 s [91% applyTxChanges (2x)] (1193x)
2025-08-04 16:23:27,904 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 11 s, CPU [user: 0.835 s, system: 0.133 s], Allocated memory: 285.5 MB, transactions: 4, SynchronizationTask: 5.72 s [95% Mapping created and updated items (1x)] (6x), ObjectEnum Document: 1.01 s [100% available (6x)] (6x), resolve: 0.799 s [94% Module (97x)] (804x)
2025-08-04 16:25:10,515 [ajp-nio-127.0.0.1-8889-exec-5 | cID:742f3fb1-c0a844bd-5090eafd-385912cb] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/fileContent.jsp?url=WBSdev%2F.polarion%2Fsynchronizer%2Fconfiguration.xml': Total: 0.193 s, CPU [user: 0.0493 s, system: 0.0392 s], Allocated memory: 4.0 MB, transactions: 1
2025-08-04 16:30:29,889 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.168310546875
2025-08-04 16:30:39,888 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.03408203125
