2025-08-04 16:33:55,817 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0428 s [62% update (144x), 37% query (12x)] (221x), svn: 0.0183 s [54% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-08-04 16:33:55,954 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0348 s [57% getDir2 content (2x), 35% info (3x)] (6x)
2025-08-04 16:33:56,716 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.759 s, CPU [user: 0.194 s, system: 0.285 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.112 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 16:33:56,717 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.758 s, CPU [user: 0.0548 s, system: 0.0894 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0587 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 16:33:56,717 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.759 s, CPU [user: 0.249 s, system: 0.352 s], Allocated memory: 71.0 MB, transactions: 0, ObjectMaps: 0.158 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 16:33:56,717 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.759 s, CPU [user: 0.133 s, system: 0.223 s], Allocated memory: 26.1 MB, transactions: 0, ObjectMaps: 0.0878 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0745 s [30% log2 (5x), 24% info (5x), 22% log (1x), 11% getLatestRevision (2x)] (18x)
2025-08-04 16:33:56,717 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.759 s, CPU [user: 0.0654 s, system: 0.0911 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0924 s [75% log2 (10x), 13% testConnection (1x)] (13x), ObjectMaps: 0.0556 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 16:33:56,717 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.759 s, CPU [user: 0.0934 s, system: 0.165 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0878 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0608 s [73% log2 (10x), 17% getLatestRevision (2x)] (13x)
2025-08-04 16:33:56,717 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.56 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.314 s [60% log2 (36x), 14% getLatestRevision (9x), 13% testConnection (6x)] (61x)
2025-08-04 16:33:56,970 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.212 s [100% getReadConfiguration (48x)] (48x), svn: 0.0869 s [82% info (18x)] (38x)
2025-08-04 16:33:57,337 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.286 s [73% info (94x), 20% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.206 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 16:33:57,565 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.109 s, CPU [user: 0.0349 s, system: 0.00909 s], Allocated memory: 9.4 MB
2025-08-04 16:33:57,603 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.251 s [100% doFinishStartup (1x)] (1x), commit: 0.0471 s [100% Revision (1x)] (1x), Lucene: 0.039 s [100% refresh (1x)] (1x), DB: 0.0291 s [75% query (1x), 16% update (3x)] (8x), SubterraURITable: 0.0247 s [100% addIfNotExistsDB (1x)] (1x), derivedLinkedRevisionsContributor: 0.0149 s [100% objectsToInv (1x)] (1x)
2025-08-04 16:34:00,615 [main | u:p | u:p] INFO  TXLOGGER - Tx 661d0dd5a883f_0_661d0dd5a883f_0_: finished. Total: 0.132 s, CPU [user: 0.081 s, system: 0.00919 s], Allocated memory: 7.6 MB
2025-08-04 16:34:01,393 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.72 s [84% info (158x)] (170x)
2025-08-04 16:34:01,900 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661d0dd695c43_0_661d0dd695c43_0_: finished. Total: 0.466 s, CPU [user: 0.165 s, system: 0.0336 s], Allocated memory: 17.1 MB, resolve: 0.136 s [53% Project (1x), 46% User (2x)] (5x), Lucene: 0.0639 s [100% search (1x)] (1x), ObjectMaps: 0.0473 s [52% getPrimaryObjectLocation (2x), 36% getPrimaryObjectProperty (2x)] (11x), svn: 0.0285 s [31% getLatestRevision (2x), 22% log (1x), 19% testConnection (1x), 15% info (1x)] (8x)
2025-08-04 16:34:02,464 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.868 s, CPU [user: 0.00686 s, system: 0.00188 s], Allocated memory: 322.6 kB, transactions: 1
2025-08-04 16:34:02,465 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 32, notification worker: 0.492 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.219 s [64% User (4x), 33% Project (1x)] (8x), Lucene: 0.0875 s [73% search (1x), 20% add (1x)] (3x), ObjectMaps: 0.0771 s [71% getPrimaryObjectLocation (4x), 22% getPrimaryObjectProperty (2x)] (13x), persistence listener: 0.0475 s [57% indexRefreshPersistenceListener (1x), 41% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.0419 s [100% WorkItem (24x)] (24x), GlobalHandler: 0.0324 s [64% applyTxChanges (2x), 36% get (6x)] (8x), svn: 0.0285 s [31% getLatestRevision (2x), 22% log (1x), 19% testConnection (1x), 15% info (1x)] (8x)
2025-08-04 16:34:02,466 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.874 s, CPU [user: 0.169 s, system: 0.038 s], Allocated memory: 19.1 MB, transactions: 26, svn: 0.759 s [98% getDatedRevision (181x)] (183x), Lucene: 0.066 s [86% buildBaselineSnapshots (1x)] (27x)
2025-08-04 16:34:02,937 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0dd694040_0_661d0dd694040_0_: finished. Total: 1.51 s, CPU [user: 0.394 s, system: 0.122 s], Allocated memory: 51.6 MB, svn: 0.912 s [47% getDatedRevision (181x), 36% getDir2 content (25x)] (328x), resolve: 0.513 s [100% Category (117x)] (117x), ObjectMaps: 0.208 s [44% getPrimaryObjectLocation (117x), 35% getPrimaryObjectProperty (117x), 21% getLastPromoted (117x)] (472x)
2025-08-04 16:34:03,120 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0dd81c447_0_661d0dd81c447_0_: finished. Total: 0.125 s, CPU [user: 0.03 s, system: 0.0086 s], Allocated memory: 2.7 MB, resolve: 0.107 s [100% Project (6x)] (6x), svn: 0.0606 s [41% log (2x), 37% info (5x), 21% getFile content (5x)] (13x), ObjectMaps: 0.0495 s [62% getPrimaryObjectProperty (5x), 19% getLastPromoted (5x)] (21x)
2025-08-04 16:34:03,369 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0dd83c048_0_661d0dd83c048_0_: finished. Total: 0.248 s, CPU [user: 0.0871 s, system: 0.0175 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.137 s [69% getReadConfiguration (180x), 31% getReadUserConfiguration (10x)] (190x), svn: 0.122 s [63% info (21x), 31% getFile content (16x)] (39x), resolve: 0.0611 s [100% User (9x)] (9x), ObjectMaps: 0.0292 s [55% getPrimaryObjectProperty (8x), 25% getLastPromoted (8x)] (32x)
2025-08-04 16:34:03,685 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0dd88a84a_0_661d0dd88a84a_0_: finished. Total: 0.25 s, CPU [user: 0.0732 s, system: 0.0135 s], Allocated memory: 19.8 MB, svn: 0.196 s [70% getDir2 content (17x), 30% getFile content (44x)] (62x), RepositoryConfigService: 0.0893 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 16:34:04,584 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0dd8c944b_0_661d0dd8c944b_0_: finished. Total: 0.899 s, CPU [user: 0.37 s, system: 0.0374 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.659 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.532 s [65% getFile content (412x), 35% getDir2 content (21x)] (434x), GC: 0.049 s [100% G1 Young Generation (4x)] (4x)
2025-08-04 16:34:05,099 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d0dd9c884e_0_661d0dd9c884e_0_: finished. Total: 0.392 s, CPU [user: 0.161 s, system: 0.0119 s], Allocated memory: 385.0 MB, RepositoryConfigService: 0.287 s [97% getReadConfiguration (2788x)] (3026x), svn: 0.237 s [66% getFile content (186x), 34% getDir2 content (21x)] (208x)
2025-08-04 16:34:05,176 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.76 s, CPU [user: 1.22 s, system: 0.223 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.29 s [39% getDir2 content (133x), 35% getFile content (865x), 19% getDatedRevision (181x)] (1222x), RepositoryConfigService: 1.29 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.682 s [75% Category (117x), 16% Project (7x)] (139x), ObjectMaps: 0.286 s [42% getPrimaryObjectProperty (130x), 37% getPrimaryObjectLocation (136x), 21% getLastPromoted (130x)] (531x)
2025-08-04 16:34:05,176 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 63, svn: 3.05 s [38% getDatedRevision (362x), 29% getDir2 content (133x), 26% getFile content (865x)] (1406x), RepositoryConfigService: 1.29 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.683 s [75% Category (117x), 16% Project (7x)] (140x), ObjectMaps: 0.286 s [42% getPrimaryObjectProperty (130x), 37% getPrimaryObjectLocation (136x), 21% getLastPromoted (130x)] (531x)
2025-08-04 16:34:36,469 [ajp-nio-127.0.0.1-8889-exec-5 | cID:7437e1d6-c0a844bd-318e5309-da793b8f] INFO  TXLOGGER - Summary for 'servlet /polarion/wiki/skins/sidecar/save.gif?buildId=20220419-1528-22_R1-be3adceb': Total: 0.349 s, CPU [user: 0.0128 s, system: 0.00666 s], Allocated memory: 1.1 MB, transactions: 2, PolarionAuthenticator: 0.345 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0373 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 16:34:36,469 [ajp-nio-127.0.0.1-8889-exec-6 | cID:7437e1d6-c0a844bd-318e5309-5aaa8732] INFO  TXLOGGER - Summary for 'servlet /polarion/wiki/skins/sidecar/button_arrow2.gif?buildId=20220419-1528-22_R1-be3adceb': Total: 0.349 s, CPU [user: 0.0214 s, system: 0.0107 s], Allocated memory: 2.3 MB, transactions: 2, PolarionAuthenticator: 0.342 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0399 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 16:34:36,476 [ajp-nio-127.0.0.1-8889-exec-4 | cID:7437e1d6-c0a844bd-318e5309-261e0222] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/img/gray_11slides_002sec.gif': Total: 0.357 s, CPU [user: 0.015 s, system: 0.00733 s], Allocated memory: 1.7 MB, transactions: 2, PolarionAuthenticator: 0.356 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0339 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 16:34:38,034 [ajp-nio-127.0.0.1-8889-exec-3 | cID:7437e196-c0a844bd-318e5309-89edb631] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.98 s, CPU [user: 0.29 s, system: 0.0716 s], Allocated memory: 31.6 MB, transactions: 3, PolarionAuthenticator: 0.399 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.101 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 16:34:38,624 [ajp-nio-127.0.0.1-8889-exec-2 | cID:7437e197-c0a844bd-318e5309-9c008c67 | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661d0df91d45c_0_661d0df91d45c_0_: finished. Total: 1.83 s, CPU [user: 0.614 s, system: 0.151 s], Allocated memory: 240.6 MB, ObjectEnum Document: 1.73 s [100% available (6x)] (6x), resolve: 1.21 s [99% Module (97x)] (802x), svn: 0.77 s [52% info (92x), 31% getDir2 content (46x)] (185x), DB: 0.273 s [53% query (194x), 26% update (194x), 21% commit (194x)] (5917x), ModulePageParser: 0.207 s [100% parsePage (46x)] (46x), Lucene: 0.0962 s [100% search (9x)] (9x)
2025-08-04 16:34:38,627 [ajp-nio-127.0.0.1-8889-exec-2 | cID:7437e197-c0a844bd-318e5309-9c008c67] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 2.57 s, CPU [user: 0.875 s, system: 0.215 s], Allocated memory: 296.1 MB, transactions: 5, ObjectEnum Document: 1.73 s [100% available (6x)] (6x), resolve: 1.22 s [99% Module (97x)] (804x), svn: 0.799 s [50% info (92x), 30% getDir2 content (46x)] (191x), PolarionAuthenticator: 0.399 s [100% authenticate (1x)] (1x), DB: 0.273 s [53% query (194x), 26% update (194x), 21% commit (194x)] (5917x), ModulePageParser: 0.207 s [100% parsePage (46x)] (46x)
2025-08-04 16:37:25,039 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.125 s, CPU [user: 0.00221 s, system: 0.0102 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.0598 s [100% collectChanges (1x)] (1x), svn: 0.0574 s [100% getLatestRevision (1x)] (1x)
2025-08-04 16:38:58,336 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.297 s, CPU [user: 0.00367 s, system: 0.0189 s], Allocated memory: 129.7 kB, transactions: 0, PullingJob: 0.164 s [100% collectChanges (1x)] (1x), svn: 0.146 s [100% getLatestRevision (1x)] (1x)
2025-08-04 16:49:07,848 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.172 s, CPU [user: 0.00206 s, system: 0.0098 s], Allocated memory: 129.8 kB, transactions: 0, PullingJob: 0.112 s [100% collectChanges (1x)] (1x), svn: 0.109 s [100% getLatestRevision (1x)] (1x)
