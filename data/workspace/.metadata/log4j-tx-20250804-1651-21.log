2025-08-04 16:51:26,717 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0604 s [71% update (144x), 29% query (12x)] (221x), svn: 0.00939 s [52% getLatestRevision (2x), 36% testConnection (1x)] (4x)
2025-08-04 16:51:26,827 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0357 s [58% getDir2 content (2x), 32% info (3x)] (6x)
2025-08-04 16:51:27,563 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.0461 s, system: 0.102 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0638 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 16:51:27,564 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.231 s, system: 0.372 s], Allocated memory: 70.5 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 16:51:27,564 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.734 s, CPU [user: 0.0922 s, system: 0.195 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.109 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0612 s [75% log2 (10x), 16% getLatestRevision (2x)] (13x)
2025-08-04 16:51:27,564 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.127 s, system: 0.249 s], Allocated memory: 26.3 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0789 s [30% log (1x), 24% info (5x), 23% log2 (5x), 9% getLatestRevision (2x)] (18x)
2025-08-04 16:51:27,564 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.734 s, CPU [user: 0.0598 s, system: 0.0905 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0776 s [74% log2 (10x), 20% getLatestRevision (2x)] (13x), ObjectMaps: 0.0635 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 16:51:27,564 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.734 s, CPU [user: 0.193 s, system: 0.312 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 16:51:27,564 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.56 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.29 s [56% log2 (36x), 16% getLatestRevision (9x), 12% testConnection (6x)] (61x)
2025-08-04 16:51:27,783 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.188 s [100% getReadConfiguration (48x)] (48x), svn: 0.0743 s [86% info (18x)] (38x)
2025-08-04 16:51:28,090 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.24 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.182 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 16:51:28,307 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.108 s, CPU [user: 0.0398 s, system: 0.00966 s], Allocated memory: 10.0 MB
2025-08-04 16:51:28,325 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.222 s [100% doFinishStartup (1x)] (1x), commit: 0.0399 s [100% Revision (1x)] (1x), Lucene: 0.0359 s [100% refresh (1x)] (1x), DB: 0.0205 s [52% execute (1x), 22% update (3x), 20% query (1x)] (8x), derivedLinkedRevisionsContributor: 0.0153 s [100% objectsToInv (1x)] (1x)
2025-08-04 16:51:30,492 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.292 s [86% info (158x)] (168x)
2025-08-04 16:51:30,781 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661d11d713c45_0_661d11d713c45_0_: finished. Total: 0.271 s, CPU [user: 0.128 s, system: 0.0167 s], Allocated memory: 18.4 MB, resolve: 0.0742 s [70% User (2x), 27% Project (1x)] (5x), Lucene: 0.0416 s [100% search (1x)] (1x), ObjectMaps: 0.022 s [59% getPrimaryObjectLocation (2x), 32% getPrimaryObjectProperty (2x)] (11x), GC: 0.022 s [100% G1 Young Generation (1x)] (1x), svn: 0.0195 s [36% getLatestRevision (2x), 22% testConnection (1x), 21% log (1x), 13% getFile content (2x)] (8x)
2025-08-04 16:51:31,302 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.00702 s, system: 0.00142 s], Allocated memory: 322.6 kB, transactions: 1
2025-08-04 16:51:31,302 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.28 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.0947 s [75% User (3x), 21% Project (1x)] (7x), Lucene: 0.0587 s [71% search (1x), 21% add (1x)] (3x), ObjectMaps: 0.0308 s [71% getPrimaryObjectLocation (3x), 23% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0302 s [100% WorkItem (24x)] (24x), persistence listener: 0.0205 s [88% indexRefreshPersistenceListener (1x)] (7x), svn: 0.0195 s [36% getLatestRevision (2x), 22% testConnection (1x), 21% log (1x), 13% getFile content (2x)] (8x)
2025-08-04 16:51:31,303 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.811 s, CPU [user: 0.165 s, system: 0.023 s], Allocated memory: 19.6 MB, transactions: 27, svn: 0.573 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0826 s [85% buildBaselineSnapshots (1x)] (27x)
2025-08-04 16:51:31,732 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d11d712440_0_661d11d712440_0_: finished. Total: 1.23 s, CPU [user: 0.312 s, system: 0.0849 s], Allocated memory: 51.2 MB, svn: 0.771 s [51% getDatedRevision (181x), 32% getDir2 content (25x)] (328x), resolve: 0.362 s [100% Category (117x)] (117x), ObjectMaps: 0.13 s [40% getPrimaryObjectProperty (117x), 37% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (472x)
2025-08-04 16:51:32,085 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d11d87c84a_0_661d11d87c84a_0_: finished. Total: 0.132 s, CPU [user: 0.046 s, system: 0.00413 s], Allocated memory: 19.8 MB, svn: 0.101 s [73% getDir2 content (17x), 27% getFile content (44x)] (62x), RepositoryConfigService: 0.044 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 16:51:32,700 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d11d89d44b_0_661d11d89d44b_0_: finished. Total: 0.615 s, CPU [user: 0.317 s, system: 0.0116 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.47 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.294 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-08-04 16:51:33,042 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d11d94fc4e_0_661d11d94fc4e_0_: finished. Total: 0.243 s, CPU [user: 0.1 s, system: 0.00579 s], Allocated memory: 384.4 MB, svn: 0.155 s [53% getFile content (186x), 47% getDir2 content (21x)] (208x), RepositoryConfigService: 0.152 s [96% getReadConfiguration (2788x)] (3026x)
2025-08-04 16:51:33,100 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.6 s, CPU [user: 0.915 s, system: 0.128 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.55 s [40% getDir2 content (133x), 31% getFile content (865x), 26% getDatedRevision (181x)] (1222x), RepositoryConfigService: 0.786 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.426 s [85% Category (117x)] (139x), ObjectMaps: 0.158 s [43% getPrimaryObjectProperty (130x), 35% getPrimaryObjectLocation (136x), 22% getLastPromoted (130x)] (531x)
2025-08-04 16:51:33,100 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 2.13 s [45% getDatedRevision (362x), 29% getDir2 content (133x), 23% getFile content (865x)] (1407x), RepositoryConfigService: 0.786 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.426 s [85% Category (117x)] (140x), ObjectMaps: 0.158 s [43% getPrimaryObjectProperty (130x), 35% getPrimaryObjectLocation (136x), 22% getLastPromoted (130x)] (531x), Lucene: 0.116 s [60% buildBaselineSnapshots (2x), 26% search (5x)] (60x)
2025-08-04 16:51:41,594 [ajp-nio-127.0.0.1-8889-exec-4 | cID:7447861a-c0a844bd-4161d3f8-5e3a56bb] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/img/progress.gif': Total: 0.382 s, CPU [user: 0.0287 s, system: 0.0139 s], Allocated memory: 3.8 MB, transactions: 2, PolarionAuthenticator: 0.38 s [100% authenticate (1x)] (1x)
2025-08-04 16:51:41,773 [ajp-nio-127.0.0.1-8889-exec-3 | cID:7447860e-c0a844bd-4161d3f8-c728068e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754294387248': Total: 0.574 s, CPU [user: 0.221 s, system: 0.0763 s], Allocated memory: 45.3 MB, transactions: 3, PolarionAuthenticator: 0.39 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0785 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 16:51:41,781 [ajp-nio-127.0.0.1-8889-exec-2 | cID:74478602-c0a844bd-4161d3f8-cca55cc9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754294387247': Total: 0.593 s, CPU [user: 0.0973 s, system: 0.0269 s], Allocated memory: 15.7 MB, transactions: 3, PolarionAuthenticator: 0.384 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0899 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 16:51:45,096 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74478fea-c0a844bd-4161d3f8-77cf0cff | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661d11e430459_0_661d11e430459_0_: finished. Total: 1.16 s, CPU [user: 0.473 s, system: 0.094 s], Allocated memory: 240.1 MB, ObjectEnum Document: 1.07 s [100% available (6x)] (6x), resolve: 0.841 s [99% Module (97x)] (802x), svn: 0.496 s [42% getDir2 content (46x), 42% info (92x)] (185x), ModulePageParser: 0.192 s [100% parsePage (46x)] (46x), DB: 0.127 s [53% query (194x), 25% update (194x), 21% commit (194x)] (5917x)
2025-08-04 16:51:45,098 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74478fea-c0a844bd-4161d3f8-77cf0cff] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.38 s, CPU [user: 0.586 s, system: 0.114 s], Allocated memory: 265.7 MB, transactions: 3, ObjectEnum Document: 1.07 s [100% available (6x)] (6x), resolve: 0.841 s [99% Module (97x)] (803x), svn: 0.505 s [42% getDir2 content (46x), 41% info (92x)] (189x), ModulePageParser: 0.192 s [100% parsePage (46x)] (46x), DB: 0.127 s [53% query (194x), 25% update (194x), 21% commit (194x)] (5917x)
2025-08-04 16:51:45,120 [ajp-nio-127.0.0.1-8889-exec-1 | cID:74478fea-c0a844bd-4161d3f8-64a58b16] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.4 s, CPU [user: 0.141 s, system: 0.0225 s], Allocated memory: 11.9 MB, transactions: 0
2025-08-04 16:51:49,721 [ajp-nio-127.0.0.1-8889-exec-9 | cID:74479569-c0a844bd-4161d3f8-a3333184] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/170ab2e2-33b4-4999-bcd8-912a645b4c19/metadata': Total: 4.59 s, CPU [user: 0.101 s, system: 0.015 s], Allocated memory: 10.2 MB, transactions: 0
2025-08-04 16:53:52,107 [ajp-nio-127.0.0.1-8889-exec-8 | cID:744984e5-c0a844bd-4161d3f8-d2a51cda] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.133 s, CPU [user: 0.0492 s, system: 0.0258 s], Allocated memory: 4.4 MB, transactions: 0
2025-08-04 16:53:54,566 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d12637f05b_0_661d12637f05b_0_: finished. Total: 0.265 s, CPU [user: 0.0692 s, system: 0.0412 s], Allocated memory: 101.8 MB, ObjectEnum Document: 0.229 s [100% available (6x)] (6x), DB: 0.149 s [51% query (194x), 26% update (194x), 22% commit (194x)] (5917x), resolve: 0.0394 s [85% Module (97x)] (802x), GlobalHandler: 0.0331 s [100% get (1242x)] (1244x), EHCache: 0.0295 s [100% GET (802x)] (802x)
2025-08-04 16:54:02,264 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d126b2b05d_0_661d126b2b05d_0_: finished. Total: 0.108 s, CPU [user: 0.0382 s, system: 0.0075 s], Allocated memory: 5.4 MB, resolve: 0.0616 s [100% WorkItem (1x)] (1x), svn: 0.0399 s [40% info (4x), 19% getDir2 content (1x), 18% testConnection (1x), 10% log (1x)] (10x), RepositoryConfigService: 0.0134 s [53% getExistingPrefixes (1x), 47% getReadConfiguration (10x)] (11x), ObjectMaps: 0.0118 s [52% getPrimaryObjectProperty (1x), 34% getPrimaryObjectLocation (1x)] (4x)
2025-08-04 16:55:01,613 [ajp-nio-127.0.0.1-8889-exec-9 | cID:744a9449-c0a844bd-4161d3f8-9864c7a4] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test/execute': Total: 0.163 s, CPU [user: 0.0442 s, system: 0.023 s], Allocated memory: 4.6 MB, transactions: 0, svn: 0.0213 s [72% testConnection (1x), 20% info (1x)] (3x)
2025-08-04 16:55:01,936 [Worker-1: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d12a54545e_0_661d12a54545e_0_: finished. Total: 0.283 s, CPU [user: 0.0687 s, system: 0.0251 s], Allocated memory: 38.8 MB, ObjectEnum Document: 0.246 s [100% available (6x)] (6x), DB: 0.181 s [50% query (194x), 26% update (194x), 23% commit (194x)] (5917x), resolve: 0.0145 s [63% Module (97x), 37% Category (696x)] (802x)
2025-08-04 16:55:28,939 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 94.7 s, CPU [user: 0.258 s, system: 0.115 s], Allocated memory: 119.7 MB, transactions: 3, SynchronizationTask: 86.8 s [99% Mapping created and updated items (1x)] (10x)
2025-08-04 16:55:29,861 [Worker-1: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 28.2 s, CPU [user: 0.165 s, system: 0.0799 s], Allocated memory: 49.0 MB, transactions: 3
2025-08-04 16:56:45,465 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d130a69461_0_661d130a69461_0_: finished. Total: 0.243 s, CPU [user: 0.0606 s, system: 0.0297 s], Allocated memory: 38.8 MB, ObjectEnum Document: 0.202 s [100% available (6x)] (6x), DB: 0.143 s [50% query (194x), 26% update (194x), 24% commit (194x)] (5917x), resolve: 0.0123 s [54% Category (696x), 44% Module (97x)] (802x)
2025-08-04 17:06:06,087 [ajp-nio-127.0.0.1-8889-exec-10 | cID:7454b7b9-c0a844bd-4161d3f8-6431706a] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754298365858': Total: 0.2 s, CPU [user: 0.0354 s, system: 0.054 s], Allocated memory: 1.1 MB, transactions: 0
2025-08-04 17:06:06,087 [ajp-nio-127.0.0.1-8889-exec-5 | cID:7454b7b7-c0a844bd-4161d3f8-53e422b0] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754298365858': Total: 0.202 s, CPU [user: 0.0222 s, system: 0.0347 s], Allocated memory: 603.4 kB, transactions: 0, svn: 0.0419 s [78% testConnection (1x), 22% info (1x)] (2x)
