2025-08-04 17:12:36,235 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0555 s [62% update (144x), 37% query (12x)] (221x), svn: 0.0182 s [70% getLatestRevision (2x), 20% testConnection (1x)] (4x)
2025-08-04 17:12:36,376 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0372 s [67% getDir2 content (2x), 27% info (3x)] (6x)
2025-08-04 17:12:37,199 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.817 s, CPU [user: 0.258 s, system: 0.373 s], Allocated memory: 70.7 MB, transactions: 0, ObjectMaps: 0.135 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 17:12:37,199 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.819 s, CPU [user: 0.212 s, system: 0.307 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.107 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 17:12:37,199 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.819 s, CPU [user: 0.0699 s, system: 0.103 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.101 s [84% log2 (10x)] (13x), ObjectMaps: 0.067 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 17:12:37,199 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.818 s, CPU [user: 0.117 s, system: 0.245 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0813 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 17:12:37,199 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.819 s, CPU [user: 0.0986 s, system: 0.188 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0973 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0814 s [82% log2 (10x)] (13x)
2025-08-04 17:12:37,200 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.818 s, CPU [user: 0.0766 s, system: 0.107 s], Allocated memory: 9.2 MB, transactions: 0, svn: 0.123 s [40% log2 (5x), 29% log (1x), 12% info (5x)] (18x), ObjectMaps: 0.0636 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 17:12:37,200 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.551 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.375 s [65% log2 (36x), 12% getLatestRevision (9x), 10% log (1x)] (61x)
2025-08-04 17:12:37,454 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.221 s [100% getReadConfiguration (48x)] (48x), svn: 0.0846 s [90% info (18x)] (38x)
2025-08-04 17:12:37,805 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.273 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.212 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 17:12:38,034 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.11 s, CPU [user: 0.0236 s, system: 0.00804 s], Allocated memory: 8.6 MB, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 17:12:38,076 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.252 s [100% doFinishStartup (1x)] (1x), commit: 0.076 s [100% Revision (1x)] (1x), Lucene: 0.0313 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0156 s [100% objectsToInv (1x)] (1x)
2025-08-04 17:12:40,409 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.347 s [87% info (158x)] (168x)
2025-08-04 17:12:40,735 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661d16af38443_0_661d16af38443_0_: finished. Total: 0.315 s, CPU [user: 0.145 s, system: 0.0223 s], Allocated memory: 17.1 MB, resolve: 0.0864 s [57% User (2x), 40% Project (1x)] (5x), Lucene: 0.0304 s [100% search (1x)] (1x), ObjectMaps: 0.0273 s [51% getPrimaryObjectProperty (2x), 41% getPrimaryObjectLocation (2x)] (11x), GC: 0.027 s [100% G1 Young Generation (1x)] (1x), svn: 0.0228 s [27% log (1x), 27% getLatestRevision (2x), 23% testConnection (1x), 13% getFile content (2x)] (8x)
2025-08-04 17:12:41,251 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.699 s, CPU [user: 0.00639 s, system: 0.00115 s], Allocated memory: 322.4 kB, transactions: 1
2025-08-04 17:12:41,251 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.331 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.123 s [68% User (3x), 28% Project (1x)] (7x), ObjectMaps: 0.0389 s [59% getPrimaryObjectLocation (3x), 36% getPrimaryObjectProperty (2x)] (12x), Lucene: 0.0368 s [82% search (1x)] (2x), Incremental Baseline: 0.0285 s [100% WorkItem (24x)] (24x), svn: 0.0278 s [30% getLatestRevision (3x), 29% testConnection (2x), 22% log (1x)] (10x)
2025-08-04 17:12:41,252 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.846 s, CPU [user: 0.172 s, system: 0.0261 s], Allocated memory: 19.6 MB, transactions: 27, svn: 0.575 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0846 s [82% buildBaselineSnapshots (1x)] (27x)
2025-08-04 17:12:41,700 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d16af38844_0_661d16af38844_0_: finished. Total: 1.28 s, CPU [user: 0.357 s, system: 0.0948 s], Allocated memory: 51.6 MB, svn: 0.797 s [53% getDatedRevision (181x), 31% getDir2 content (25x)] (328x), resolve: 0.382 s [100% Category (117x)] (117x), ObjectMaps: 0.135 s [41% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 24% getLastPromoted (117x)] (472x)
2025-08-04 17:12:41,889 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d16b08cc48_0_661d16b08cc48_0_: finished. Total: 0.11 s, CPU [user: 0.0558 s, system: 0.00941 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0546 s [54% getReadConfiguration (180x), 46% getReadUserConfiguration (10x)] (190x), svn: 0.047 s [62% info (21x), 34% getFile content (16x)] (39x), resolve: 0.0301 s [100% User (9x)] (9x), ObjectMaps: 0.0147 s [47% getPrimaryObjectProperty (8x), 27% getLastPromoted (8x), 25% getPrimaryObjectLocation (8x)] (32x)
2025-08-04 17:12:42,160 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d16b0b2c4a_0_661d16b0b2c4a_0_: finished. Total: 0.229 s, CPU [user: 0.0759 s, system: 0.00718 s], Allocated memory: 19.8 MB, svn: 0.169 s [59% getDir2 content (17x), 41% getFile content (44x)] (62x), RepositoryConfigService: 0.11 s [99% getReadConfiguration (170x)] (192x)
2025-08-04 17:12:42,916 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d16b0ec04b_0_661d16b0ec04b_0_: finished. Total: 0.756 s, CPU [user: 0.346 s, system: 0.0162 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.545 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.414 s [61% getFile content (412x), 39% getDir2 content (21x)] (434x)
2025-08-04 17:12:43,338 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d16b1ca44e_0_661d16b1ca44e_0_: finished. Total: 0.289 s, CPU [user: 0.125 s, system: 0.00783 s], Allocated memory: 390.3 MB, RepositoryConfigService: 0.196 s [96% getReadConfiguration (2788x)] (3026x), svn: 0.173 s [57% getFile content (186x), 43% getDir2 content (21x)] (208x)
2025-08-04 17:12:43,408 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.99 s, CPU [user: 1.06 s, system: 0.15 s], Allocated memory: 1.7 GB, transactions: 11, svn: 1.83 s [40% getDir2 content (133x), 33% getFile content (865x), 23% getDatedRevision (181x)] (1222x), RepositoryConfigService: 0.991 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.456 s [84% Category (117x)] (139x), ObjectMaps: 0.169 s [43% getPrimaryObjectProperty (130x), 33% getPrimaryObjectLocation (136x), 24% getLastPromoted (130x)] (531x)
2025-08-04 17:12:43,408 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 2.4 s [41% getDatedRevision (362x), 31% getDir2 content (133x), 25% getFile content (865x)] (1405x), RepositoryConfigService: 0.991 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.457 s [84% Category (117x)] (140x), ObjectMaps: 0.169 s [43% getPrimaryObjectProperty (130x), 33% getPrimaryObjectLocation (136x), 24% getLastPromoted (130x)] (531x), Lucene: 0.125 s [56% buildBaselineSnapshots (2x), 26% search (5x)] (60x)
2025-08-04 17:14:11,087 [ajp-nio-127.0.0.1-8889-exec-2 | cID:745c1bc4-c0a844bd-19450d3d-80a19504] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754298850158': Total: 0.834 s, CPU [user: 0.0926 s, system: 0.0717 s], Allocated memory: 4.1 MB, transactions: 2, RepositoryConfigService: 0.0622 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 17:14:11,087 [ajp-nio-127.0.0.1-8889-exec-3 | cID:745c1bc4-c0a844bd-19450d3d-bfcbd6d4] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754298850157': Total: 0.834 s, CPU [user: 0.0698 s, system: 0.0455 s], Allocated memory: 2.9 MB, transactions: 2, RepositoryConfigService: 0.0663 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 17:14:31,204 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.11015625
2025-08-04 17:14:31,611 [ajp-nio-127.0.0.1-8889-exec-6 | cID:745c6dce-c0a844bd-19450d3d-4663d673] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754298871205': Total: 0.359 s, CPU [user: 0.00698 s, system: 0.0179 s], Allocated memory: 286.8 kB, transactions: 0, svn: 0.316 s [79% testConnection (1x), 21% info (1x)] (2x)
2025-08-04 17:14:41,206 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.00029296875
2025-08-04 17:14:43,867 [ajp-nio-127.0.0.1-8889-exec-5 | cID:745c6dce-c0a844bd-19450d3d-a65be2f1] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754298871206': Total: 12.6 s, CPU [user: 0.00481 s, system: 0.01 s], Allocated memory: 84.0 kB, transactions: 0
2025-08-04 17:15:38,400 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.12 s, CPU [user: 0.00218 s, system: 0.00775 s], Allocated memory: 130.5 kB, transactions: 0, PullingJob: 0.115 s [100% collectChanges (1x)] (1x), svn: 0.106 s [100% getLatestRevision (1x)] (1x)
