2025-08-04 18:26:35,256 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.131 s [56% update (144x), 43% query (12x)] (221x), svn: 0.0229 s [47% testConnection (1x), 38% getLatestRevision (2x)] (4x)
2025-08-04 18:26:35,420 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0571 s [66% getDir2 content (2x), 29% info (3x)] (6x)
2025-08-04 18:26:36,310 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.885 s, CPU [user: 0.25 s, system: 0.279 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.172 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 18:26:36,310 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.885 s, CPU [user: 0.144 s, system: 0.213 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 18:26:36,310 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.885 s, CPU [user: 0.0537 s, system: 0.0816 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0527 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0507 s [65% log2 (5x), 22% getLatestRevision (1x)] (7x)
2025-08-04 18:26:36,310 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.885 s, CPU [user: 0.0998 s, system: 0.144 s], Allocated memory: 14.3 MB, transactions: 0, svn: 0.0814 s [75% log2 (10x), 15% getLatestRevision (2x)] (13x), ObjectMaps: 0.0718 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 18:26:36,310 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.886 s, CPU [user: 0.0768 s, system: 0.076 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.112 s [84% log2 (10x)] (13x), ObjectMaps: 0.0469 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 18:26:36,310 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.885 s, CPU [user: 0.317 s, system: 0.345 s], Allocated memory: 72.9 MB, transactions: 0, ObjectMaps: 0.138 s [99% getAllPrimaryObjects (1x)] (16x), svn: 0.136 s [32% log2 (5x), 29% log (1x), 20% info (5x)] (18x)
2025-08-04 18:26:36,311 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.599 s [100% getAllPrimaryObjects (8x)] (65x), svn: 0.432 s [61% log2 (36x), 12% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-04 18:26:36,466 [main | u:p] INFO  TXLOGGER - Tx 661d279b2c801_0_661d279b2c801_0_: finished. Total: 0.127 s, CPU [user: 0.0922 s, system: 0.00586 s], Allocated memory: 21.8 MB
2025-08-04 18:26:36,612 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.252 s [100% getReadConfiguration (48x)] (48x), svn: 0.082 s [78% info (18x), 12% getLatestRevision (1x)] (38x)
2025-08-04 18:26:36,979 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.245 s [77% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.195 s [100% getReadConfiguration (94x)] (94x)
2025-08-04 18:26:37,219 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.1 s, CPU [user: 0.0287 s, system: 0.00598 s], Allocated memory: 2.3 MB
2025-08-04 18:26:37,219 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.1 s, CPU [user: 0.0197 s, system: 0.00483 s], Allocated memory: 1.9 MB
2025-08-04 18:26:37,221 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.102 s, CPU [user: 0.0303 s, system: 0.0071 s], Allocated memory: 2.9 MB
2025-08-04 18:26:37,223 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.104 s, CPU [user: 0.00563 s, system: 0.00297 s], Allocated memory: 869.0 kB
2025-08-04 18:26:37,283 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.163 s, CPU [user: 0.0249 s, system: 0.00941 s], Allocated memory: 8.5 MB, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 18:26:37,411 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.397 s [100% doFinishStartup (1x)] (1x), DB: 0.102 s [47% update (45x), 29% execute (15x), 15% query (20x)] (122x), Lucene: 0.0566 s [100% refresh (2x)] (2x), SubterraURITable: 0.0412 s [100% addIfNotExistsDB (20x)] (20x), commit: 0.0384 s [67% Revision (1x), 33% BuildArtifact (1x)] (2x), resolve: 0.0336 s [84% BuildArtifact (11x)] (13x), GlobalHandler: 0.024 s [92% put (13x)] (26x)
2025-08-04 18:26:40,410 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.437 s [86% info (158x)] (168x)
2025-08-04 18:26:41,412 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.885 s, CPU [user: 0.00604 s, system: 0.0021 s], Allocated memory: 714.3 kB, transactions: 1
2025-08-04 18:26:41,412 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.885 s, CPU [user: 0.00503 s, system: 0.00163 s], Allocated memory: 342.7 kB, transactions: 1
2025-08-04 18:26:41,413 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 73, persistence listener: 0.045 s [60% indexRefreshPersistenceListener (9x), 12% PlanActivityCreator (9x), 11% WorkItemActivityCreator (9x)] (63x), Incremental Baseline: 0.0321 s [100% WorkItem (24x)] (24x), resolve: 0.0297 s [82% User (1x)] (3x), notification worker: 0.0254 s [59% RevisionActivityCreator (18x), 14% WorkItemActivityCreator (9x), 13% PlanActivityCreator (9x)] (54x), PullingJob: 0.0211 s [100% collectChanges (1x)] (1x), Lucene: 0.0203 s [64% refresh (2x), 36% add (1x)] (3x), svn: 0.019 s [55% getLatestRevision (1x), 45% testConnection (1x)] (2x), Full Baseline: 0.0168 s [100% WorkItem (2x)] (2x), ObjectMaps: 0.00587 s [100% getPrimaryObjectLocation (1x)] (1x), EHCache: 0.0042 s [99% GET (15x)] (41x), DB: 0.00295 s [65% update (2x), 35% commit (2x)] (4x), GlobalHandler: 0.00232 s [85% get (3x)] (5x)
2025-08-04 18:26:41,414 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.01 s, CPU [user: 0.193 s, system: 0.0313 s], Allocated memory: 19.3 MB, transactions: 26, svn: 0.765 s [99% getDatedRevision (181x)] (183x), Lucene: 0.079 s [89% buildBaselineSnapshots (1x)] (27x)
2025-08-04 18:26:41,993 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d279f29440_0_661d279f29440_0_: finished. Total: 1.57 s, CPU [user: 0.483 s, system: 0.119 s], Allocated memory: 56.9 MB, svn: 0.972 s [55% getDatedRevision (181x), 26% getDir2 content (25x)] (328x), resolve: 0.543 s [100% Category (117x)] (117x), ObjectMaps: 0.19 s [42% getPrimaryObjectProperty (117x), 35% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 18:26:42,284 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d27a0d0070_0_661d27a0d0070_0_: finished. Total: 0.171 s, CPU [user: 0.0679 s, system: 0.0137 s], Allocated memory: 8.3 MB, svn: 0.0766 s [55% info (21x), 40% getFile content (17x)] (40x), RepositoryConfigService: 0.0751 s [59% getReadUserConfiguration (10x), 41% getReadConfiguration (180x)] (190x), resolve: 0.0604 s [100% User (9x)] (9x), ObjectMaps: 0.0241 s [45% getPrimaryObjectProperty (9x), 33% getPrimaryObjectLocation (9x), 22% getLastPromoted (9x)] (37x)
2025-08-04 18:26:42,508 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d27a110072_0_661d27a110072_0_: finished. Total: 0.14 s, CPU [user: 0.0378 s, system: 0.00513 s], Allocated memory: 19.6 MB, svn: 0.113 s [83% getDir2 content (17x)] (62x), RepositoryConfigService: 0.031 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 18:26:43,508 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d27a133073_0_661d27a133073_0_: finished. Total: 1 s, CPU [user: 0.43 s, system: 0.0463 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.798 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.523 s [74% getFile content (412x), 26% getDir2 content (21x)] (434x)
2025-08-04 18:26:43,947 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d27a24bc76_0_661d27a24bc76_0_: finished. Total: 0.316 s, CPU [user: 0.119 s, system: 0.00819 s], Allocated memory: 385.7 MB, svn: 0.211 s [55% getDir2 content (21x), 45% getFile content (186x)] (208x), RepositoryConfigService: 0.18 s [96% getReadConfiguration (2788x)] (3026x)
2025-08-04 18:26:44,014 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.6 s, CPU [user: 1.27 s, system: 0.214 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.15 s [36% getFile content (867x), 35% getDir2 content (133x), 25% getDatedRevision (181x)] (1226x), RepositoryConfigService: 1.19 s [93% getReadConfiguration (12166x)] (12860x), resolve: 0.665 s [82% Category (117x)] (139x), ObjectMaps: 0.239 s [45% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 22% getLastPromoted (132x)] (541x)
2025-08-04 18:26:44,014 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 63, svn: 2.92 s [44% getDatedRevision (362x), 26% getFile content (867x), 26% getDir2 content (133x)] (1410x), RepositoryConfigService: 1.19 s [93% getReadConfiguration (12166x)] (12860x), resolve: 0.666 s [82% Category (117x)] (140x), ObjectMaps: 0.239 s [45% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 22% getLastPromoted (132x)] (541x)
