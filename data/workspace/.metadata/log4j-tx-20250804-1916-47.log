2025-08-04 19:16:51,917 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0295 s [59% update (144x), 41% query (12x)] (221x), svn: 0.0103 s [52% getLatestRevision (2x), 39% testConnection (1x)] (4x)
2025-08-04 19:16:52,021 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0353 s [63% getDir2 content (2x), 30% info (3x)] (6x)
2025-08-04 19:16:52,713 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.0529 s, system: 0.101 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0637 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:16:52,713 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.0642 s, system: 0.0948 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0664 s [83% log2 (10x)] (13x), ObjectMaps: 0.0662 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 19:16:52,713 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.186 s, system: 0.276 s], Allocated memory: 53.4 MB, transactions: 0, ObjectMaps: 0.106 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:16:52,713 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.256 s, system: 0.334 s], Allocated memory: 73.0 MB, transactions: 0, ObjectMaps: 0.124 s [99% getAllPrimaryObjects (1x)] (16x), svn: 0.0729 s [30% info (5x), 26% log2 (5x), 20% log (1x), 11% getLatestRevision (2x)] (18x)
2025-08-04 19:16:52,713 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.114 s, system: 0.219 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0696 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:16:52,713 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.0886 s, system: 0.166 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0846 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0477 s [76% log2 (10x), 17% getLatestRevision (2x)] (13x)
2025-08-04 19:16:52,714 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.514 s [100% getAllPrimaryObjects (8x)] (65x), svn: 0.247 s [61% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-04 19:16:52,924 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.177 s [100% getReadConfiguration (48x)] (48x), svn: 0.0693 s [85% info (18x)] (38x)
2025-08-04 19:16:53,146 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.165 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.131 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 19:16:53,384 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.101 s, CPU [user: 0.029 s, system: 0.0065 s], Allocated memory: 2.8 MB
2025-08-04 19:16:53,421 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.137 s, CPU [user: 0.0263 s, system: 0.00771 s], Allocated memory: 8.6 MB
2025-08-04 19:16:53,484 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.323 s [100% doFinishStartup (1x)] (1x), commit: 0.0936 s [100% Revision (1x)] (1x), Lucene: 0.0515 s [100% refresh (1x)] (1x)
2025-08-04 19:16:55,781 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.288 s [91% info (158x)] (168x)
2025-08-04 19:16:56,616 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.00479 s, system: 0.00119 s], Allocated memory: 557.1 kB, transactions: 1
2025-08-04 19:16:56,617 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.00444 s, system: 0.00152 s], Allocated memory: 232.2 kB, transactions: 1
2025-08-04 19:16:56,618 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 32, persistence listener: 0.0645 s [88% indexRefreshPersistenceListener (1x)] (7x), Incremental Baseline: 0.0285 s [100% WorkItem (24x)] (24x), resolve: 0.0265 s [86% User (2x)] (4x), Lucene: 0.0248 s [54% add (1x), 46% refresh (2x)] (3x), notification worker: 0.0215 s [63% RevisionActivityCreator (2x), 21% WorkItemActivityCreator (1x)] (6x), ObjectMaps: 0.00908 s [100% getPrimaryObjectLocation (2x)] (2x), PullingJob: 0.00904 s [100% collectChanges (1x)] (1x), svn: 0.00897 s [54% getLatestRevision (1x), 46% testConnection (1x)] (2x), Full Baseline: 0.00649 s [100% WorkItem (1x)] (1x), DB: 0.00378 s [74% update (2x), 26% commit (2x)] (4x)
2025-08-04 19:16:56,618 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.736 s, CPU [user: 0.167 s, system: 0.0238 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.627 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0678 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (26x)] (27x)
