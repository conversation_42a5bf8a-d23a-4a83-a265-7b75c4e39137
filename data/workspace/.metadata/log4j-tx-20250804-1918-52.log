2025-08-04 19:18:58,808 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0506 s [61% update (144x), 39% query (12x)] (221x), svn: 0.0115 s [56% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-08-04 19:18:58,932 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.032 s [59% getDir2 content (2x), 35% info (3x)] (6x)
2025-08-04 19:18:59,682 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.748 s, CPU [user: 0.208 s, system: 0.291 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.115 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:18:59,682 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.748 s, CPU [user: 0.118 s, system: 0.224 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0924 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:18:59,682 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.748 s, CPU [user: 0.056 s, system: 0.0825 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0637 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:18:59,682 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.0648 s, system: 0.101 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.103 s [85% log2 (10x)] (13x), ObjectMaps: 0.0624 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 19:18:59,682 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.746 s, CPU [user: 0.264 s, system: 0.349 s], Allocated memory: 72.7 MB, transactions: 0, ObjectMaps: 0.122 s [99% getAllPrimaryObjects (1x)] (16x), svn: 0.0843 s [28% info (5x), 21% log2 (5x), 21% log (1x), 13% testConnection (1x)] (18x)
2025-08-04 19:18:59,682 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.0958 s, system: 0.172 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.105 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.054 s [74% log2 (10x), 19% getLatestRevision (2x)] (13x)
2025-08-04 19:18:59,683 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.56 s [100% getAllPrimaryObjects (8x)] (65x), svn: 0.315 s [61% log2 (36x), 14% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-04 19:18:59,900 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.185 s [100% getReadConfiguration (48x)] (48x), svn: 0.0718 s [82% info (18x)] (38x)
2025-08-04 19:19:00,168 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.203 s [77% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.159 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 19:19:00,409 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.227 s [100% doFinishStartup (1x)] (1x), commit: 0.049 s [100% Revision (1x)] (1x), Lucene: 0.0348 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0133 s [100% objectsToInv (1x)] (1x)
