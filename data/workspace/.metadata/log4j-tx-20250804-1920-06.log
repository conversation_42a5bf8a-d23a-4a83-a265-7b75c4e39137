2025-08-04 19:20:12,232 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.061 s [71% update (144x), 29% query (12x)] (221x), svn: 0.0125 s [59% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-08-04 19:20:12,350 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0293 s [58% getDir2 content (2x), 31% info (3x)] (6x)
2025-08-04 19:20:13,100 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.745 s, CPU [user: 0.262 s, system: 0.334 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.123 s [99% getAllPrimaryObjects (1x)] (16x)
2025-08-04 19:20:13,100 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.745 s, CPU [user: 0.22 s, system: 0.276 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.112 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:20:13,101 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.745 s, CPU [user: 0.0721 s, system: 0.0782 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0828 s [77% log2 (10x), 16% getLatestRevision (2x)] (13x), ObjectMaps: 0.0685 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 19:20:13,101 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.745 s, CPU [user: 0.0983 s, system: 0.159 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0927 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0716 s [66% log2 (10x), 19% getLatestRevision (2x)] (13x)
2025-08-04 19:20:13,101 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.745 s, CPU [user: 0.156 s, system: 0.22 s], Allocated memory: 26.1 MB, transactions: 0, ObjectMaps: 0.103 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0684 s [27% info (5x), 24% log (1x), 22% log2 (5x), 10% getLatestRevision (2x)] (18x)
2025-08-04 19:20:13,101 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.745 s, CPU [user: 0.0531 s, system: 0.081 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0554 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.043 s [67% log2 (5x), 18% testConnection (1x)] (7x)
2025-08-04 19:20:13,101 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.554 s [100% getAllPrimaryObjects (8x)] (65x), svn: 0.301 s [58% log2 (36x), 16% getLatestRevision (9x), 13% testConnection (6x)] (61x)
2025-08-04 19:20:13,315 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.181 s [100% getReadConfiguration (48x)] (48x), svn: 0.0684 s [83% info (18x)] (38x)
2025-08-04 19:20:13,673 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.28 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.218 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 19:20:13,920 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.232 s [100% doFinishStartup (1x)] (1x), commit: 0.0604 s [100% Revision (1x)] (1x), Lucene: 0.0326 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0231 s [100% objectsToInv (1x)] (1x)
2025-08-04 19:20:16,892 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.409 s [92% info (158x)] (168x)
2025-08-04 19:20:17,767 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.743 s, CPU [user: 0.00461 s, system: 0.0015 s], Allocated memory: 551.3 kB, transactions: 1
2025-08-04 19:20:17,767 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.743 s, CPU [user: 0.0038 s, system: 0.00126 s], Allocated memory: 226.4 kB, transactions: 1
2025-08-04 19:20:17,768 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 33, persistence listener: 0.0341 s [62% TestRunActivityCreator (1x), 27% indexRefreshPersistenceListener (1x)] (7x), Incremental Baseline: 0.034 s [100% WorkItem (24x)] (24x), notification worker: 0.0313 s [66% RevisionActivityCreator (2x), 14% WorkItemActivityCreator (1x), 11% TestRunActivityCreator (1x)] (6x), Lucene: 0.0199 s [62% refresh (2x), 38% add (1x)] (3x), PullingJob: 0.0182 s [100% collectChanges (1x)] (1x), Full Baseline: 0.015 s [100% WorkItem (2x)] (2x), resolve: 0.0141 s [61% User (1x), 39% Revision (2x)] (3x), svn: 0.014 s [54% getLatestRevision (1x), 46% testConnection (1x)] (2x), DB: 0.00249 s [61% update (2x), 39% commit (2x)] (4x), ObjectMaps: 0.00227 s [100% getPrimaryObjectLocation (1x)] (1x), EHCache: 0.00198 s [99% GET (15x)] (41x)
2025-08-04 19:20:17,768 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.882 s, CPU [user: 0.166 s, system: 0.0245 s], Allocated memory: 19.3 MB, transactions: 26, svn: 0.653 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0554 s [85% buildBaselineSnapshots (1x)] (27x)
2025-08-04 19:20:18,111 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d33e446042_0_661d33e446042_0_: finished. Total: 1.19 s, CPU [user: 0.391 s, system: 0.0961 s], Allocated memory: 57.0 MB, svn: 0.663 s [48% getDatedRevision (181x), 31% getDir2 content (25x), 20% getFile content (119x)] (328x), resolve: 0.483 s [100% Category (117x)] (117x), ObjectMaps: 0.19 s [45% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 22% getLastPromoted (117x)] (473x)
2025-08-04 19:20:18,339 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d33e58a448_0_661d33e58a448_0_: finished. Total: 0.121 s, CPU [user: 0.0575 s, system: 0.0108 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0515 s [62% getReadUserConfiguration (10x), 38% getReadConfiguration (180x)] (190x), svn: 0.0511 s [55% info (21x), 35% getFile content (17x)] (40x), resolve: 0.0435 s [100% User (9x)] (9x), ObjectMaps: 0.0238 s [52% getPrimaryObjectProperty (9x), 28% getPrimaryObjectLocation (9x)] (37x)
