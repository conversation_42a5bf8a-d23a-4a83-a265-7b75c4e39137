2025-08-04 19:21:19,716 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.07 s [62% update (144x), 38% query (12x)] (221x), svn: 0.02 s [55% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-08-04 19:21:19,843 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0332 s [56% getDir2 content (2x), 34% info (3x)] (6x)
2025-08-04 19:21:20,593 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.238 s, system: 0.362 s], Allocated memory: 70.6 MB, transactions: 0, ObjectMaps: 0.127 s [99% getAllPrimaryObjects (1x)] (16x)
2025-08-04 19:21:20,593 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.193 s, system: 0.302 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.111 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:21:20,593 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.0904 s, system: 0.185 s], Allocated memory: 14.8 MB, transactions: 0, ObjectMaps: 0.0872 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0613 s [58% log2 (10x), 27% testConnection (1x)] (13x)
2025-08-04 19:21:20,593 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.0656 s, system: 0.1 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0685 s [84% log2 (10x)] (13x), ObjectMaps: 0.0587 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 19:21:20,593 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.11 s, system: 0.24 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0785 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:21:20,593 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.0705 s, system: 0.11 s], Allocated memory: 9.1 MB, transactions: 0, svn: 0.0874 s [36% log (1x), 24% log2 (5x), 23% info (5x)] (18x), ObjectMaps: 0.0716 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:21:20,594 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.534 s [100% getAllPrimaryObjects (8x)] (65x), svn: 0.28 s [56% log2 (36x), 13% testConnection (6x), 11% log (1x)] (61x)
2025-08-04 19:21:20,820 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.191 s [100% getReadConfiguration (48x)] (48x), svn: 0.0715 s [82% info (18x)] (38x)
2025-08-04 19:21:21,327 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.403 s [78% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.305 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 19:21:21,567 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.115 s, CPU [user: 0.0398 s, system: 0.0111 s], Allocated memory: 9.7 MB
2025-08-04 19:21:21,642 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.293 s [100% doFinishStartup (1x)] (1x), commit: 0.0825 s [100% Revision (1x)] (1x), Lucene: 0.0403 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0204 s [100% objectsToInv (1x)] (1x), DB: 0.016 s [43% update (3x), 35% query (1x), 11% commit (2x)] (8x)
2025-08-04 19:21:23,928 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.277 s [89% info (158x)] (168x)
2025-08-04 19:21:24,702 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.674 s, CPU [user: 0.00737 s, system: 0.00163 s], Allocated memory: 624.8 kB, transactions: 1
2025-08-04 19:21:24,703 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 32, resolve: 0.0506 s [90% User (1x)] (3x), Incremental Baseline: 0.0272 s [100% WorkItem (24x)] (24x), persistence listener: 0.0157 s [81% indexRefreshPersistenceListener (1x)] (7x), Full Baseline: 0.0152 s [100% WorkItem (2x)] (2x), notification worker: 0.0142 s [67% RevisionActivityCreator (2x), 16% WorkItemActivityCreator (1x)] (6x), ObjectMaps: 0.00839 s [100% getPrimaryObjectLocation (1x)] (1x), Lucene: 0.00752 s [100% refresh (1x)] (1x), PullingJob: 0.00677 s [100% collectChanges (1x)] (1x), svn: 0.00653 s [52% testConnection (1x), 48% getLatestRevision (1x)] (2x)
2025-08-04 19:21:24,703 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.778 s, CPU [user: 0.157 s, system: 0.0231 s], Allocated memory: 19.3 MB, transactions: 26, svn: 0.58 s [97% getDatedRevision (181x)] (183x), Lucene: 0.0609 s [85% buildBaselineSnapshots (1x)] (27x)
2025-08-04 19:21:25,010 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3425b7040_0_661d3425b7040_0_: finished. Total: 1.08 s, CPU [user: 0.353 s, system: 0.0899 s], Allocated memory: 58.2 MB, svn: 0.611 s [46% getDatedRevision (181x), 35% getDir2 content (25x)] (328x), resolve: 0.375 s [100% Category (117x)] (117x), ObjectMaps: 0.129 s [40% getPrimaryObjectProperty (117x), 35% getPrimaryObjectLocation (117x), 25% getLastPromoted (117x)] (473x)
2025-08-04 19:21:25,236 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3426e1848_0_661d3426e1848_0_: finished. Total: 0.109 s, CPU [user: 0.0539 s, system: 0.00984 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0486 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (180x)] (190x), svn: 0.0439 s [55% info (21x), 37% getFile content (17x)] (40x), resolve: 0.0345 s [100% User (9x)] (9x), ObjectMaps: 0.0186 s [48% getPrimaryObjectProperty (9x), 29% getPrimaryObjectLocation (9x), 24% getLastPromoted (9x)] (37x)
2025-08-04 19:21:25,451 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d342705c4a_0_661d342705c4a_0_: finished. Total: 0.18 s, CPU [user: 0.0567 s, system: 0.00719 s], Allocated memory: 19.8 MB, svn: 0.137 s [70% getDir2 content (17x), 30% getFile content (44x)] (62x), RepositoryConfigService: 0.0615 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 19:21:26,816 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d342732c4b_0_661d342732c4b_0_: finished. Total: 1.36 s, CPU [user: 0.485 s, system: 0.0465 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.15 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.468 s [72% getFile content (412x), 28% getDir2 content (21x)] (434x), GC: 0.081 s [100% G1 Young Generation (4x)] (4x)
2025-08-04 19:21:27,445 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3428a904e_0_661d3428a904e_0_: finished. Total: 0.497 s, CPU [user: 0.197 s, system: 0.0316 s], Allocated memory: 386.9 MB, RepositoryConfigService: 0.384 s [95% getReadConfiguration (2788x)] (3026x), svn: 0.258 s [69% getFile content (186x), 31% getDir2 content (21x)] (208x)
2025-08-04 19:21:27,542 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.61 s, CPU [user: 1.26 s, system: 0.207 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.77 s [41% getFile content (867x), 39% getDir2 content (133x)] (1226x), RepositoryConfigService: 1.72 s [96% getReadConfiguration (12166x)] (12860x), resolve: 0.49 s [77% Category (117x), 16% Project (7x)] (139x)
2025-08-04 19:21:27,542 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 63, svn: 2.35 s [36% getDatedRevision (362x), 31% getFile content (867x), 30% getDir2 content (133x)] (1409x), RepositoryConfigService: 1.72 s [96% getReadConfiguration (12166x)] (12860x), resolve: 0.49 s [77% Category (117x), 16% Project (7x)] (140x), ObjectMaps: 0.174 s [43% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 24% getLastPromoted (132x)] (541x)
2025-08-04 19:21:28,492 [ajp-nio-127.0.0.1-8889-exec-3 | cID:74d0a70c-7f000001-79c67d42-535ca707] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.415 s, CPU [user: 0.225 s, system: 0.0412 s], Allocated memory: 42.3 MB, transactions: 2, PolarionAuthenticator: 0.373 s [100% authenticate (1x)] (1x)
2025-08-04 19:21:29,036 [ajp-nio-127.0.0.1-8889-exec-8 | cID:74d0aa3c-7f000001-79c67d42-8a37f706] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.143 s, CPU [user: 0.0191 s, system: 0.00356 s], Allocated memory: 1.5 MB, transactions: 0
2025-08-04 19:21:29,036 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74d0aa27-7f000001-79c67d42-b80531d1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.164 s, CPU [user: 0.0768 s, system: 0.014 s], Allocated memory: 9.6 MB, transactions: 0
2025-08-04 19:21:29,055 [ajp-nio-127.0.0.1-8889-exec-5 | cID:74d0aa3c-7f000001-79c67d42-cad744e5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1754306488813': Total: 0.162 s, CPU [user: 0.0343 s, system: 0.00457 s], Allocated memory: 1.9 MB, transactions: 0
2025-08-04 19:21:29,149 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74d0aa3e-7f000001-79c67d42-b3905d63] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754306488815': Total: 0.255 s, CPU [user: 0.0392 s, system: 0.00784 s], Allocated memory: 5.3 MB, transactions: 1, RepositoryConfigService: 0.113 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 19:21:29,150 [ajp-nio-127.0.0.1-8889-exec-3 | cID:74d0aa3d-7f000001-79c67d42-d742b3b3] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754306488814': Total: 0.257 s, CPU [user: 0.0809 s, system: 0.014 s], Allocated memory: 8.6 MB, transactions: 1, RepositoryConfigService: 0.0758 s [100% getReadConfiguration (1x)] (1x)
2025-08-04 19:21:32,750 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74d0b421-7f000001-79c67d42-3a5e9eda | u:admin | POST:/polarion/synchronizer/rest/proxyInstance?projectId=WBSdev | u:admin] INFO  TXLOGGER - Tx 661d342d4dc55_0_661d342d4dc55_0_: finished. Total: 1.05 s, CPU [user: 0.489 s, system: 0.0634 s], Allocated memory: 240.7 MB, ObjectEnum Document: 0.965 s [100% available (6x)] (6x), resolve: 0.731 s [99% Module (97x)] (802x), svn: 0.407 s [48% info (92x), 32% getDir2 content (46x), 20% getFile content (46x)] (185x), ModulePageParser: 0.184 s [100% parsePage (46x)] (46x), DB: 0.131 s [54% query (194x), 25% update (194x), 21% commit (194x)] (5917x)
2025-08-04 19:21:32,752 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74d0b421-7f000001-79c67d42-3a5e9eda] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.33 s, CPU [user: 0.626 s, system: 0.0924 s], Allocated memory: 272.5 MB, transactions: 3, ObjectEnum Document: 0.965 s [100% available (6x)] (6x), resolve: 0.731 s [99% Module (97x)] (803x), svn: 0.414 s [47% info (92x), 32% getDir2 content (46x), 20% getFile content (48x)] (189x), ModulePageParser: 0.184 s [100% parsePage (46x)] (46x), DB: 0.131 s [54% query (194x), 25% update (194x), 21% commit (194x)] (5917x)
2025-08-04 19:21:33,011 [ajp-nio-127.0.0.1-8889-exec-5 | cID:74d0b421-7f000001-79c67d42-2d4895a7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 1.59 s, CPU [user: 0.158 s, system: 0.0242 s], Allocated memory: 10.4 MB, transactions: 0
2025-08-04 19:21:37,569 [ajp-nio-127.0.0.1-8889-exec-8 | cID:74d0ba5e-7f000001-79c67d42-315025cc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance/ccc5621a-e32b-4a2a-adf3-92d2a615d2e8/metadata': Total: 4.55 s, CPU [user: 0.109 s, system: 0.0165 s], Allocated memory: 10.6 MB, transactions: 0
2025-08-04 19:27:10,131 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.102 s, CPU [user: 0.00232 s, system: 0.00208 s], Allocated memory: 130.5 kB, transactions: 0, PullingJob: 0.101 s [100% collectChanges (1x)] (1x), svn: 0.101 s [100% getLatestRevision (1x)] (1x), GC: 0.09 s [100% G1 Young Generation (1x)] (1x)
