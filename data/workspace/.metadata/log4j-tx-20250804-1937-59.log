2025-08-04 19:38:05,570 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0437 s [61% update (144x), 39% query (12x)] (221x), svn: 0.0187 s [53% getLatestRevision (2x), 37% testConnection (1x)] (4x)
2025-08-04 19:38:05,677 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0318 s [65% getDir2 content (2x), 28% info (3x)] (6x)
2025-08-04 19:38:06,373 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.185 s, system: 0.277 s], Allocated memory: 53.4 MB, transactions: 0, ObjectMaps: 0.0986 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:38:06,373 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.105 s, system: 0.217 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0847 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:38:06,373 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.082 s, system: 0.171 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0948 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0754 s [78% log2 (10x), 17% getLatestRevision (2x)] (13x)
2025-08-04 19:38:06,374 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.0609 s, system: 0.0924 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.0889 s [80% log2 (10x), 11% getLatestRevision (2x)] (13x), ObjectMaps: 0.046 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 19:38:06,374 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.24 s, system: 0.338 s], Allocated memory: 72.9 MB, transactions: 0, ObjectMaps: 0.126 s [99% getAllPrimaryObjects (1x)] (16x), svn: 0.0649 s [28% log2 (5x), 26% info (5x), 22% log (1x), 11% getLatestRevision (2x)] (18x)
2025-08-04 19:38:06,373 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.0466 s, system: 0.0877 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0614 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.05 s [77% log2 (5x), 13% getLatestRevision (1x)] (7x)
2025-08-04 19:38:06,374 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.512 s [100% getAllPrimaryObjects (8x)] (65x), svn: 0.315 s [65% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-04 19:38:06,602 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.198 s [100% getReadConfiguration (48x)] (48x), svn: 0.0642 s [86% info (18x)] (38x)
2025-08-04 19:38:06,963 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.294 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.218 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 19:38:07,169 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.109 s, CPU [user: 0.0233 s, system: 0.00625 s], Allocated memory: 8.6 MB, GC: 0.012 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 19:38:07,199 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.222 s [100% doFinishStartup (1x)] (1x), commit: 0.0652 s [100% Revision (1x)] (1x), Lucene: 0.0313 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0141 s [100% objectsToInv (1x)] (1x)
2025-08-04 19:38:09,449 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.331 s [90% info (158x)] (168x)
2025-08-04 19:38:10,356 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.781 s, CPU [user: 0.00561 s, system: 0.00128 s], Allocated memory: 552.8 kB, transactions: 1
2025-08-04 19:38:10,357 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.0472 s [90% RevisionActivityCreator (2x)] (6x), Incremental Baseline: 0.034 s [100% WorkItem (24x)] (24x), resolve: 0.0198 s [80% User (1x)] (3x), Lucene: 0.0187 s [74% add (1x), 26% refresh (1x)] (2x), persistence listener: 0.0146 s [80% indexRefreshPersistenceListener (1x)] (7x), PullingJob: 0.0105 s [100% collectChanges (1x)] (1x), svn: 0.0104 s [62% testConnection (1x), 38% getLatestRevision (1x)] (2x), ObjectMaps: 0.00733 s [100% getPrimaryObjectLocation (1x)] (1x), Full Baseline: 0.00448 s [100% WorkItem (1x)] (1x)
2025-08-04 19:38:10,358 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.914 s, CPU [user: 0.167 s, system: 0.0277 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.695 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0545 s [70% buildBaselineSnapshots (1x), 30% buildBaseline (26x)] (27x)
2025-08-04 19:38:10,863 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d37fbab842_0_661d37fbab842_0_: finished. Total: 1.41 s, CPU [user: 0.455 s, system: 0.116 s], Allocated memory: 56.8 MB, svn: 0.826 s [56% getDatedRevision (181x), 22% getDir2 content (25x), 17% getFile content (119x)] (328x), resolve: 0.557 s [100% Category (117x)] (117x), ObjectMaps: 0.219 s [42% getPrimaryObjectLocation (117x), 38% getPrimaryObjectProperty (117x), 20% getLastPromoted (117x)] (473x)
2025-08-04 19:38:11,117 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d37fd2ac48_0_661d37fd2ac48_0_: finished. Total: 0.129 s, CPU [user: 0.0568 s, system: 0.0128 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0593 s [65% getReadUserConfiguration (10x), 35% getReadConfiguration (180x)] (190x), svn: 0.053 s [55% info (21x), 39% getFile content (17x)] (40x), resolve: 0.0416 s [100% User (9x)] (9x), ObjectMaps: 0.0207 s [47% getPrimaryObjectProperty (9x), 30% getPrimaryObjectLocation (9x), 23% getLastPromoted (9x)] (37x)
2025-08-04 19:38:11,762 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d37fd6244a_0_661d37fd6244a_0_: finished. Total: 0.552 s, CPU [user: 0.11 s, system: 0.0262 s], Allocated memory: 19.7 MB, RepositoryConfigService: 0.339 s [99% getReadConfiguration (170x)] (192x), svn: 0.325 s [54% getDir2 content (17x), 46% getFile content (44x)] (62x)
2025-08-04 19:38:12,809 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d37fdec84b_0_661d37fdec84b_0_: finished. Total: 1.05 s, CPU [user: 0.424 s, system: 0.0593 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.71 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.594 s [56% getFile content (412x), 44% getDir2 content (21x)] (434x)
2025-08-04 19:38:13,503 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d37ff1584e_0_661d37ff1584e_0_: finished. Total: 0.553 s, CPU [user: 0.208 s, system: 0.0249 s], Allocated memory: 382.6 MB, RepositoryConfigService: 0.428 s [96% getReadConfiguration (2788x)] (3026x), svn: 0.295 s [73% getFile content (186x), 27% getDir2 content (21x)] (208x)
2025-08-04 19:38:13,650 [ajp-nio-127.0.0.1-8889-exec-4 | cID:74dffde2-7f000001-14288452-ae6ee655] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/img/progress.gif': Total: 0.303 s, CPU [user: 0.0254 s, system: 0.00686 s], Allocated memory: 2.5 MB, transactions: 2, PolarionAuthenticator: 0.3 s [100% authenticate (1x)] (1x)
2025-08-04 19:38:13,669 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d37ff9fc4f_0_661d37ff9fc4f_0_: finished. Total: 0.167 s, CPU [user: 0.0389 s, system: 0.00641 s], Allocated memory: 13.6 MB, svn: 0.136 s [57% getDir2 content (18x), 25% getFile content (33x)] (65x), RepositoryConfigService: 0.084 s [69% getReadConfiguration (128x), 31% getExistingPrefixes (12x)] (150x)
2025-08-04 19:38:13,670 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.22 s, CPU [user: 1.41 s, system: 0.273 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.44 s [39% getFile content (867x), 37% getDir2 content (133x), 19% getDatedRevision (181x)] (1239x), RepositoryConfigService: 1.72 s [93% getReadConfiguration (12166x)] (12860x), resolve: 0.674 s [83% Category (117x)] (139x), ObjectMaps: 0.271 s [42% getPrimaryObjectProperty (132x), 38% getPrimaryObjectLocation (138x)] (541x)
2025-08-04 19:38:13,670 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 66, svn: 3.15 s [37% getDatedRevision (362x), 30% getFile content (868x), 29% getDir2 content (133x)] (1426x), RepositoryConfigService: 1.72 s [93% getReadConfiguration (12167x)] (12861x), resolve: 0.677 s [82% Category (117x)] (141x), PolarionAuthenticator: 0.3 s [100% authenticate (1x)] (1x), ObjectMaps: 0.271 s [42% getPrimaryObjectProperty (132x), 38% getPrimaryObjectLocation (138x)] (541x)
2025-08-04 19:38:13,811 [ajp-nio-127.0.0.1-8889-exec-2 | cID:74dffd63-7f000001-14288452-d6fbe529] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1754306488817': Total: 0.592 s, CPU [user: 0.0577 s, system: 0.00987 s], Allocated memory: 8.7 MB, transactions: 3, PolarionAuthenticator: 0.425 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0728 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 19:38:13,818 [ajp-nio-127.0.0.1-8889-exec-3 | cID:74dffd63-7f000001-14288452-73a74c8c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1754306488816': Total: 0.599 s, CPU [user: 0.304 s, system: 0.0451 s], Allocated memory: 54.2 MB, transactions: 3, PolarionAuthenticator: 0.425 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0592 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 19:38:16,828 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d3801c9459_0_661d3801c9459_0_: finished. Total: 1.11 s, CPU [user: 0.507 s, system: 0.061 s], Allocated memory: 236.1 MB, ObjectEnum Document: 0.988 s [100% available (6x)] (6x), resolve: 0.755 s [98% Module (97x)] (802x), svn: 0.418 s [49% info (92x), 32% getDir2 content (46x)] (185x), ModulePageParser: 0.176 s [100% parsePage (46x)] (46x), DB: 0.129 s [57% query (194x), 23% update (194x), 20% commit (194x)] (5917x)
2025-08-04 19:38:16,872 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 1.38 s, CPU [user: 0.631 s, system: 0.0819 s], Allocated memory: 269.6 MB, transactions: 3, ObjectEnum Document: 0.988 s [100% available (6x)] (6x), resolve: 0.755 s [98% Module (97x)] (803x), svn: 0.426 s [48% info (92x), 31% getDir2 content (46x), 20% getFile content (48x)] (189x), ModulePageParser: 0.176 s [100% parsePage (46x)] (46x), DB: 0.129 s [57% query (194x), 23% update (194x), 20% commit (194x)] (5917x)
