2025-08-04 19:38:37,001 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0486 s [67% update (144x), 33% query (12x)] (221x), svn: 0.0108 s [53% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-08-04 19:38:37,098 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0273 s [56% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-04 19:38:37,834 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.13 s, system: 0.209 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0941 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:38:37,835 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.1 s, system: 0.153 s], Allocated memory: 14.4 MB, transactions: 0, ObjectMaps: 0.0752 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.059 s [80% log2 (10x)] (13x)
2025-08-04 19:38:37,835 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.059 s, system: 0.0872 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0733 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0497 s [79% log2 (5x), 11% getLatestRevision (1x)] (7x)
2025-08-04 19:38:37,835 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.0742 s, system: 0.086 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.102 s [85% log2 (10x)] (13x), ObjectMaps: 0.0643 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 19:38:37,835 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.224 s, system: 0.27 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.139 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:38:37,835 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.733 s, CPU [user: 0.286 s, system: 0.337 s], Allocated memory: 72.9 MB, transactions: 0, ObjectMaps: 0.124 s [99% getAllPrimaryObjects (1x)] (16x), svn: 0.0569 s [28% info (5x), 26% log2 (5x), 23% log (1x), 11% getLatestRevision (2x)] (18x)
2025-08-04 19:38:37,835 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.57 s [100% getAllPrimaryObjects (8x)] (65x), svn: 0.313 s [70% log2 (36x), 12% getLatestRevision (9x)] (61x)
2025-08-04 19:38:38,038 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.174 s [100% getReadConfiguration (48x)] (48x), svn: 0.0663 s [85% info (18x)] (38x)
2025-08-04 19:38:38,338 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.235 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.18 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 19:38:38,558 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.202 s [100% doFinishStartup (1x)] (1x), commit: 0.058 s [100% Revision (1x)] (1x), Lucene: 0.0242 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.013 s [100% objectsToInv (1x)] (1x)
2025-08-04 19:38:40,898 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.354 s [90% info (158x)] (168x), GlobalHandler: 0.026 s [99% applyTxChanges (1x)] (7x)
2025-08-04 19:38:41,859 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.897 s, CPU [user: 0.0063 s, system: 0.00125 s], Allocated memory: 552.9 kB, transactions: 1
2025-08-04 19:38:41,860 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 32, Incremental Baseline: 0.0601 s [100% WorkItem (24x)] (24x), Lucene: 0.0183 s [71% add (1x), 29% refresh (1x)] (2x), persistence listener: 0.0179 s [79% indexRefreshPersistenceListener (1x), 13% WorkItemActivityCreator (1x)] (7x), resolve: 0.0127 s [76% User (1x), 24% Revision (2x)] (3x), PullingJob: 0.0118 s [100% collectChanges (1x)] (1x), svn: 0.0116 s [68% testConnection (1x), 32% getLatestRevision (1x)] (2x), Full Baseline: 0.0101 s [100% WorkItem (2x)] (2x), notification worker: 0.00889 s [65% RevisionActivityCreator (2x), 26% WorkItemActivityCreator (1x)] (6x)
2025-08-04 19:38:41,862 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.964 s, CPU [user: 0.155 s, system: 0.0277 s], Allocated memory: 19.3 MB, transactions: 26, svn: 0.801 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0491 s [67% buildBaselineSnapshots (1x), 33% buildBaseline (26x)] (27x)
2025-08-04 19:38:42,580 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d381a62040_0_661d381a62040_0_: finished. Total: 1.68 s, CPU [user: 0.435 s, system: 0.105 s], Allocated memory: 57.4 MB, svn: 0.999 s [67% getDatedRevision (181x), 16% getDir2 content (25x)] (328x), resolve: 0.482 s [100% Category (117x)] (117x), ObjectMaps: 0.158 s [44% getPrimaryObjectProperty (117x), 32% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 19:38:42,792 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d381c20048_0_661d381c20048_0_: finished. Total: 0.104 s, CPU [user: 0.05 s, system: 0.00909 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0472 s [54% getReadConfiguration (180x), 46% getReadUserConfiguration (10x)] (190x), svn: 0.0437 s [57% info (21x), 38% getFile content (17x)] (40x), resolve: 0.0314 s [100% User (9x)] (9x), ObjectMaps: 0.0171 s [45% getPrimaryObjectProperty (9x), 31% getPrimaryObjectLocation (9x), 24% getLastPromoted (9x)] (37x)
2025-08-04 19:38:43,068 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d381c4444a_0_661d381c4444a_0_: finished. Total: 0.234 s, CPU [user: 0.0806 s, system: 0.0135 s], Allocated memory: 19.8 MB, svn: 0.175 s [71% getDir2 content (17x), 29% getFile content (44x)] (62x), RepositoryConfigService: 0.087 s [99% getReadConfiguration (170x)] (192x)
2025-08-04 19:38:44,026 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d381c7f04b_0_661d381c7f04b_0_: finished. Total: 0.957 s, CPU [user: 0.411 s, system: 0.04 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.751 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.475 s [70% getFile content (412x), 30% getDir2 content (21x)] (434x), GC: 0.061 s [100% G1 Young Generation (4x)] (4x)
2025-08-04 19:38:44,367 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d381d8c04e_0_661d381d8c04e_0_: finished. Total: 0.223 s, CPU [user: 0.0927 s, system: 0.00478 s], Allocated memory: 384.4 MB, RepositoryConfigService: 0.141 s [96% getReadConfiguration (2788x)] (3026x), svn: 0.14 s [51% getFile content (186x), 49% getDir2 content (21x)] (208x)
2025-08-04 19:38:44,432 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.53 s, CPU [user: 1.18 s, system: 0.191 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.06 s [33% getFile content (867x), 33% getDatedRevision (181x), 31% getDir2 content (133x)] (1226x), RepositoryConfigService: 1.11 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.576 s [84% Category (117x)] (139x), ObjectMaps: 0.202 s [46% getPrimaryObjectProperty (132x), 31% getPrimaryObjectLocation (138x), 22% getLastPromoted (132x)] (541x)
2025-08-04 19:38:44,432 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 63, svn: 2.86 s [51% getDatedRevision (362x), 24% getFile content (867x), 23% getDir2 content (133x)] (1409x), RepositoryConfigService: 1.11 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.577 s [84% Category (117x)] (140x), ObjectMaps: 0.202 s [46% getPrimaryObjectProperty (132x), 31% getPrimaryObjectLocation (138x), 22% getLastPromoted (132x)] (541x)
2025-08-04 19:38:46,947 [ajp-nio-127.0.0.1-8889-exec-2 | cID:74e07f56-7f000001-41acf46d-c9db2be5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test/execute': Total: 0.46 s, CPU [user: 0.286 s, system: 0.0451 s], Allocated memory: 59.6 MB, transactions: 3, PolarionAuthenticator: 0.285 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0421 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 19:38:48,035 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d38206b454_0_661d38206b454_0_: finished. Total: 0.95 s, CPU [user: 0.474 s, system: 0.0412 s], Allocated memory: 236.1 MB, ObjectEnum Document: 0.866 s [100% available (6x)] (6x), resolve: 0.662 s [99% Module (97x)] (802x), svn: 0.341 s [49% info (92x), 34% getDir2 content (46x)] (185x), ModulePageParser: 0.191 s [100% parsePage (46x)] (46x), DB: 0.113 s [51% query (194x), 26% update (194x), 23% commit (194x)] (5917x)
2025-08-04 19:40:12,354 [ajp-nio-127.0.0.1-8889-exec-9 | cID:74e1cd91-7f000001-41acf46d-4d793837] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/WorkItemDataService': Total: 0.304 s, CPU [user: 0.0528 s, system: 0.0126 s], Allocated memory: 4.2 MB, transactions: 0, RPC: 0.247 s [100% decodeRequest (1x)] (3x)
2025-08-04 19:40:12,354 [ajp-nio-127.0.0.1-8889-exec-2 | cID:74e1ce10-7f000001-41acf46d-8981d9f3] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.176 s, CPU [user: 0.0411 s, system: 0.0104 s], Allocated memory: 3.5 MB, transactions: 0, RPC: 0.143 s [100% decodeRequest (1x)] (3x)
2025-08-04 19:40:12,362 [ajp-nio-127.0.0.1-8889-exec-8 | cID:74e1cd82-7f000001-41acf46d-770967ad] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.327 s, CPU [user: 0.0365 s, system: 0.0126 s], Allocated memory: 3.5 MB, transactions: 0, RPC: 0.314 s [100% decodeRequest (1x)] (3x)
2025-08-04 19:40:12,364 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74e1cd91-7f000001-41acf46d-839f1672] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.313 s, CPU [user: 0.0415 s, system: 0.00985 s], Allocated memory: 3.4 MB, transactions: 0, RPC: 0.31 s [100% decodeRequest (1x)] (3x)
2025-08-04 19:40:12,373 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74e1ce5e-7f000001-41acf46d-2817ee28] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.117 s, CPU [user: 0.00876 s, system: 0.00235 s], Allocated memory: 1.3 MB, transactions: 0, RPC: 0.116 s [99% decodeRequest (1x)] (3x)
2025-08-04 19:40:12,631 [ajp-nio-127.0.0.1-8889-exec-7 | cID:74e1cd20-7f000001-41acf46d-d6679da2] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.694 s, CPU [user: 0.284 s, system: 0.079 s], Allocated memory: 28.5 MB, transactions: 0, RPC: 0.599 s [100% decodeRequest (1x)] (3x)
2025-08-04 19:40:12,872 [ajp-nio-127.0.0.1-8889-exec-9 | cID:74e1d06e-7f000001-41acf46d-9086eea1 | u:admin] INFO  TXLOGGER - Tx 661d38741bc59_0_661d38741bc59_0_: finished. Total: 0.0883 s, CPU [user: 0.0194 s, system: 0.00762 s], Allocated memory: 1.8 MB, RepositoryConfigService: 0.0377 s [71% getReadConfiguration (155x), 29% getReadUserConfiguration (1x)] (156x), svn: 0.011 s [46% testConnection (1x), 39% getFile content (2x)] (5x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:40:12,872 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74e1d06f-7f000001-41acf46d-c503462a | u:admin] INFO  TXLOGGER - Tx 661d38741c85b_0_661d38741c85b_0_: finished. Total: 0.0856 s, CPU [user: 0.0166 s, system: 0.00656 s], Allocated memory: 1.9 MB, RepositoryConfigService: 0.0385 s [76% getReadConfiguration (155x), 24% getReadUserConfiguration (1x)] (156x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:40:12,877 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74e1d06d-7f000001-41acf46d-ab58bc48 | u:admin] INFO  TXLOGGER - Tx 661d38741bc5a_0_661d38741bc5a_0_: finished. Total: 0.0933 s, CPU [user: 0.0167 s, system: 0.00584 s], Allocated memory: 1.8 MB, RepositoryConfigService: 0.0435 s [64% getReadConfiguration (155x), 36% getReadUserConfiguration (1x)] (156x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:40:12,881 [ajp-nio-127.0.0.1-8889-exec-9 | cID:74e1d06e-7f000001-41acf46d-9086eea1] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.099 s, CPU [user: 0.0257 s, system: 0.00947 s], Allocated memory: 3.0 MB, transactions: 1, UserDataService: 0.0889 s [100% getUsers (1x)] (1x), RepositoryConfigService: 0.0377 s [71% getReadConfiguration (155x), 29% getReadUserConfiguration (1x)] (156x), svn: 0.011 s [46% testConnection (1x), 39% getFile content (2x)] (5x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:40:12,881 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74e1d06f-7f000001-41acf46d-c503462a] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.0985 s, CPU [user: 0.0219 s, system: 0.00747 s], Allocated memory: 2.8 MB, transactions: 1, UserDataService: 0.086 s [100% getUsers (1x)] (1x), RepositoryConfigService: 0.0385 s [76% getReadConfiguration (155x), 24% getReadUserConfiguration (1x)] (156x), RPC: 0.0116 s [78% encodeResponse (1x), 14% decodeRequest (1x)] (4x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:40:12,882 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74e1d06d-7f000001-41acf46d-ab58bc48] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.0998 s, CPU [user: 0.0196 s, system: 0.00635 s], Allocated memory: 2.4 MB, transactions: 1, UserDataService: 0.0939 s [100% getUsers (1x)] (1x), RepositoryConfigService: 0.0435 s [64% getReadConfiguration (155x), 36% getReadUserConfiguration (1x)] (156x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:40:12,954 [ajp-nio-127.0.0.1-8889-exec-3 | cID:74e1d0dc-7f000001-41acf46d-d7e24a5e | u:admin] INFO  TXLOGGER - Tx 661d38743785d_0_661d38743785d_0_: finished. Total: 0.0596 s, CPU [user: 0.0103 s, system: 0.00205 s], Allocated memory: 1.4 MB, DB: 0.0428 s [94% query (2x)] (10x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 19 ]
2025-08-04 19:40:12,964 [ajp-nio-127.0.0.1-8889-exec-3 | cID:74e1d0dc-7f000001-41acf46d-d7e24a5e] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.0712 s, CPU [user: 0.019 s, system: 0.00419 s], Allocated memory: 3.4 MB, transactions: 1, UserDataService: 0.0603 s [100% loadUser (1x)] (1x), DB: 0.0428 s [94% query (2x)] (10x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 19 ]
2025-08-04 19:40:29,570 [ajp-nio-127.0.0.1-8889-exec-7 | cID:74e2105c-7f000001-41acf46d-2c9f5df0 | u:admin] INFO  TXLOGGER - Tx 661d38842cc61_0_661d38842cc61_0_: finished. Total: 0.335 s, CPU [user: 0.0616 s, system: 0.063 s], Allocated memory: 16.5 MB, svn: 0.144 s [87% endActivity (1x)] (8x), PullingJob Listeners: 0.105 s [86% ExtendedListener (8x)] (16x), PersistenceEngineListener: 0.0902 s [84% objectsModified (1x)] (6x), Lucene: 0.046 s [96% refresh (1x)] (2x), DB: 0.0256 s [79% update (9x), 9% query (2x)] (19x), ObjectMaps: 0.017 s [22% getPrimaryObjectLocations (1x), 17% addLocation (1x), 15% getLastPromoted (4x), 15% removeLocation (1x), 13% getLastRevision (2x)] (24x), processed revs: [296, 296 (delay 0s)], write: true
2025-08-04 19:40:29,595 [ajp-nio-127.0.0.1-8889-exec-7 | cID:74e2105c-7f000001-41acf46d-2c9f5df0 | u:admin] INFO  TXLOGGER - Tx 661d388480c62_0_661d388480c62_0_: finished. Total: 0.0229 s, CPU [user: 0.00466 s, system: 0.00704 s], Allocated memory: 683.2 kB, WeakPObjectList: [bulkResolves: 1 secondGetHits: 19 ]
2025-08-04 19:40:29,600 [ajp-nio-127.0.0.1-8889-exec-7 | cID:74e2105c-7f000001-41acf46d-2c9f5df0] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.451 s, CPU [user: 0.0786 s, system: 0.097 s], Allocated memory: 19.3 MB, transactions: 2, UserDataService: 0.372 s [100% saveUser (1x)] (1x), svn: 0.144 s [87% endActivity (1x)] (8x), PullingJob Listeners: 0.105 s [86% ExtendedListener (8x)] (16x), PersistenceEngineListener: 0.0902 s [84% objectsModified (1x)] (6x), RPC: 0.0577 s [89% decodeRequest (1x)] (4x), Lucene: 0.046 s [96% refresh (1x)] (2x), DB: 0.0326 s [64% update (11x), 23% query (4x)] (29x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 19 ]
2025-08-04 19:40:29,630 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74e2123a-7f000001-41acf46d-2b776686 | u:admin] INFO  TXLOGGER - Tx 661d38848ec6e_0_661d38848ec6e_0_: finished. Total: 0.00189 s, CPU [user: 0.00162 s, system: 0.000303 s], Allocated memory: 508.7 kB, WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:40:29,630 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74e2123a-7f000001-41acf46d-2b776686] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.00393 s, CPU [user: 0.00319 s, system: 0.000558 s], Allocated memory: 647.7 kB, transactions: 1, WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:40:29,653 [ajp-nio-127.0.0.1-8889-exec-1 | cID:74e2123b-7f000001-41acf46d-8b7208c8 | u:admin] INFO  TXLOGGER - Tx 661d38848f06f_0_661d38848f06f_0_: finished. Total: 0.0255 s, CPU [user: 0.00969 s, system: 0.00284 s], Allocated memory: 1.8 MB, RepositoryConfigService: 0.0168 s [100% getReadConfiguration (30x)] (31x), svn: 0.0116 s [58% getFile content (2x), 41% testConnection (1x)] (4x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 25 ]
2025-08-04 19:40:29,664 [ajp-nio-127.0.0.1-8889-exec-1 | cID:74e2123b-7f000001-41acf46d-8b7208c8] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.0377 s, CPU [user: 0.0156 s, system: 0.00532 s], Allocated memory: 3.2 MB, transactions: 1, UserDataService: 0.0263 s [100% loadUserById (1x)] (1x), RepositoryConfigService: 0.0168 s [100% getReadConfiguration (30x)] (31x), svn: 0.0116 s [58% getFile content (2x), 41% testConnection (1x)] (4x), RPC: 0.0108 s [84% encodeResponse (1x)] (4x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 25 ]
2025-08-04 19:40:56,251 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 129 s, CPU [user: 0.917 s, system: 0.11 s], Allocated memory: 306.6 MB, transactions: 4, SynchronizationTask: 124 s [99% Mapping created and updated items (1x)] (10x)
2025-08-04 19:41:50,734 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74e34e98-7f000001-41acf46d-1324ad73] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test/execute': Total: 0.116 s, CPU [user: 0.0368 s, system: 0.0554 s], Allocated memory: 4.9 MB, transactions: 0, svn: 0.0221 s [93% info (1x)] (2x)
2025-08-04 19:41:51,202 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d38d3d7072_0_661d38d3d7072_0_: finished. Total: 0.389 s, CPU [user: 0.0905 s, system: 0.0523 s], Allocated memory: 104.0 MB, ObjectEnum Document: 0.347 s [100% available (6x)] (6x), DB: 0.193 s [51% query (194x), 25% update (194x), 23% commit (194x)] (5917x), resolve: 0.0898 s [93% Module (97x)] (802x), GlobalHandler: 0.0805 s [100% get (1242x)] (1244x), EHCache: 0.077 s [100% GET (802x)] (802x)
2025-08-04 19:46:07,971 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 257 s, CPU [user: 0.347 s, system: 0.124 s], Allocated memory: 118.5 MB, transactions: 3, SynchronizationTask: 254 s [100% Mapping created and updated items (1x)] (10x)
