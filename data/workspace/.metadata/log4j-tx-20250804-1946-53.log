2025-08-04 19:47:03,278 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0401 s [63% update (144x), 37% query (12x)] (221x), svn: 0.00948 s [50% getLatestRevision (2x), 38% testConnection (1x)] (4x)
2025-08-04 19:47:03,378 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0258 s [60% getDir2 content (2x), 33% info (3x)] (6x)
2025-08-04 19:47:04,138 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.757 s, CPU [user: 0.229 s, system: 0.393 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.122 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:47:04,138 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.757 s, CPU [user: 0.114 s, system: 0.25 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0895 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 19:47:04,138 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.757 s, CPU [user: 0.21 s, system: 0.324 s], Allocated memory: 54.9 MB, transactions: 0, ObjectMaps: 0.148 s [99% getAllPrimaryObjects (1x)] (12x)
2025-08-04 19:47:04,138 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.757 s, CPU [user: 0.0621 s, system: 0.12 s], Allocated memory: 8.5 MB, transactions: 0, ObjectMaps: 0.0745 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.067 s [83% log2 (10x)] (13x)
2025-08-04 19:47:04,138 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.757 s, CPU [user: 0.0476 s, system: 0.133 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0794 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0406 s [79% log2 (5x), 12% testConnection (1x)] (7x)
2025-08-04 19:47:04,138 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.757 s, CPU [user: 0.0983 s, system: 0.194 s], Allocated memory: 17.2 MB, transactions: 0, svn: 0.111 s [37% log2 (10x), 22% info (5x), 20% log (1x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.0794 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 19:47:04,138 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.592 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.279 s [60% log2 (36x), 12% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-08-04 19:47:04,396 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.217 s [100% getReadConfiguration (48x)] (48x), svn: 0.0829 s [87% info (18x)] (38x)
2025-08-04 19:47:04,801 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.32 s [75% info (94x), 19% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.236 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 19:47:05,016 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.104 s, CPU [user: 0.0231 s, system: 0.00728 s], Allocated memory: 8.5 MB, GC: 0.013 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 19:47:05,041 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.225 s [100% doFinishStartup (1x)] (1x), commit: 0.0596 s [100% Revision (1x)] (1x), Lucene: 0.0316 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0146 s [100% objectsToInv (1x)] (1x)
2025-08-04 19:47:07,491 [ajp-nio-127.0.0.1-8889-exec-1 | cID:74e823a0-7f000001-6ae85b76-d6ea1033] INFO  TXLOGGER - Summary for 'servlet /polarion/rt/configuration-change-all': Total: 0.194 s, CPU [user: 0.0472 s, system: 0.00852 s], Allocated memory: 3.1 MB, transactions: 0
2025-08-04 19:47:07,731 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.284 s [90% info (158x)] (168x)
2025-08-04 19:47:07,751 [Persistence Events Processing Thread | u:p] INFO  TXLOGGER - Summary: Total: 0.123 s, CPU [user: 0.0156 s, system: 0.00285 s], Allocated memory: 1.0 MB, transactions: 0, persistence listener: 0.121 s [90% indexRefreshPersistenceListener (1x)] (7x), processed revs: [296 (delay 398s)]
2025-08-04 19:47:08,602 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.746 s, CPU [user: 0.00766 s, system: 0.00186 s], Allocated memory: 624.2 kB, transactions: 1
2025-08-04 19:47:08,603 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, persistence listener: 0.121 s [90% indexRefreshPersistenceListener (1x)] (7x), Full Baseline: 0.0405 s [100% WorkItem (2x)] (2x), notification worker: 0.0341 s [85% RevisionActivityCreator (2x)] (6x), resolve: 0.0309 s [57% User (1x), 43% Revision (2x)] (3x), Incremental Baseline: 0.03 s [100% WorkItem (23x)] (23x), Lucene: 0.0272 s [56% add (1x), 44% refresh (1x)] (2x), PullingJob: 0.00986 s [100% collectChanges (1x)] (1x), svn: 0.00972 s [53% getLatestRevision (1x), 47% testConnection (1x)] (2x)
2025-08-04 19:47:08,603 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.871 s, CPU [user: 0.174 s, system: 0.0315 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.615 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0818 s [80% buildBaselineSnapshots (1x), 20% buildBaseline (26x)] (27x)
2025-08-04 19:47:09,179 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3a0957840_0_661d3a0957840_0_: finished. Total: 1.44 s, CPU [user: 0.418 s, system: 0.115 s], Allocated memory: 57.6 MB, svn: 0.927 s [57% getDatedRevision (181x), 29% getDir2 content (25x)] (328x), resolve: 0.406 s [100% Category (117x)] (117x), ObjectMaps: 0.137 s [43% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 19:47:09,257 [ajp-nio-127.0.0.1-8889-exec-2 | cID:74e8291a-7f000001-6ae85b76-399b270b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test/execute': Total: 0.558 s, CPU [user: 0.349 s, system: 0.0458 s], Allocated memory: 56.5 MB, transactions: 3, PolarionAuthenticator: 0.338 s [100% authenticate (1x)] (1x), resolve: 0.0459 s [55% Project (3x), 45% User (1x)] (4x), RepositoryConfigService: 0.0329 s [100% getReadConfiguration (2x)] (2x)
2025-08-04 19:47:09,438 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3a0ad684b_0_661d3a0ad684b_0_: finished. Total: 0.163 s, CPU [user: 0.0659 s, system: 0.0157 s], Allocated memory: 8.5 MB, RepositoryConfigService: 0.0819 s [60% getReadConfiguration (180x), 40% getReadUserConfiguration (10x)] (190x), svn: 0.0664 s [53% info (21x), 38% getFile content (16x)] (39x), resolve: 0.0433 s [100% User (9x)] (9x), ObjectMaps: 0.0199 s [60% getPrimaryObjectProperty (8x), 22% getLastPromoted (8x)] (33x)
2025-08-04 19:47:09,553 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3a0aff84c_0_661d3a0aff84c_0_: finished. Total: 0.114 s, CPU [user: 0.0333 s, system: 0.00357 s], Allocated memory: 4.9 MB, svn: 0.0732 s [80% info (29x)] (42x), RepositoryConfigService: 0.0529 s [52% getReadConfiguration (54x), 48% getExistingPrefixes (9x)] (74x)
2025-08-04 19:47:09,677 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3a0b1c44f_0_661d3a0b1c44f_0_: finished. Total: 0.123 s, CPU [user: 0.0361 s, system: 0.00392 s], Allocated memory: 20.6 MB, svn: 0.101 s [45% getDir2 content (13x), 33% info (35x), 22% getFile content (44x)] (93x), RepositoryConfigService: 0.045 s [70% getReadConfiguration (170x), 30% getExistingPrefixes (11x)] (192x)
2025-08-04 19:47:10,520 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3a0b3b450_0_661d3a0b3b450_0_: finished. Total: 0.843 s, CPU [user: 0.372 s, system: 0.0183 s], Allocated memory: 1.2 GB, RepositoryConfigService: 0.672 s [70% getReadConfiguration (8682x), 30% getExistingPrefixes (129x)] (9018x), svn: 0.5 s [39% info (226x), 39% getFile content (412x), 21% getDir2 content (17x)] (656x)
2025-08-04 19:47:10,721 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3a0c0e051_0_661d3a0c0e051_0_: finished. Total: 0.201 s, CPU [user: 0.0449 s, system: 0.00708 s], Allocated memory: 19.3 MB, svn: 0.179 s [49% getDir2 content (14x), 40% info (37x)] (81x), RepositoryConfigService: 0.0575 s [61% getReadConfiguration (124x), 39% getExistingPrefixes (12x)] (148x)
2025-08-04 19:47:10,823 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3a0c40452_0_661d3a0c40452_0_: finished. Total: 0.101 s, CPU [user: 0.0254 s, system: 0.004 s], Allocated memory: 6.9 MB, svn: 0.0923 s [54% info (35x), 38% getDir2 content (8x)] (52x), RepositoryConfigService: 0.0363 s [57% getExistingPrefixes (9x), 43% getReadConfiguration (38x)] (54x)
2025-08-04 19:47:10,837 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d3a0b0604e_0_661d3a0b0604e_0_: finished. Total: 1.37 s, CPU [user: 0.627 s, system: 0.062 s], Allocated memory: 290.9 MB, ObjectEnum Document: 1 s [100% available (6x)] (6x), resolve: 0.654 s [97% Module (97x)] (802x), svn: 0.48 s [38% info (140x), 36% getFile content (208x), 26% getDir2 content (47x)] (396x), RepositoryConfigService: 0.266 s [89% getReadConfiguration (464x)] (621x), DB: 0.229 s [46% query (194x), 33% update (194x), 21% commit (194x)] (5917x), ModulePageParser: 0.185 s [100% parsePage (46x)] (46x)
2025-08-04 19:47:11,135 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d3a0c59c53_0_661d3a0c59c53_0_: finished. Total: 0.313 s, CPU [user: 0.109 s, system: 0.00757 s], Allocated memory: 338.4 MB, svn: 0.226 s [61% info (151x), 34% getDir2 content (16x)] (193x), RepositoryConfigService: 0.201 s [61% getExistingPrefixes (89x), 39% getReadConfiguration (2787x)] (2877x)
2025-08-04 19:47:11,209 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.47 s, CPU [user: 1.16 s, system: 0.187 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.28 s [29% getDir2 content (109x), 27% info (578x), 23% getDatedRevision (181x), 19% getFile content (703x)] (1587x), RepositoryConfigService: 1.19 s [63% getReadConfiguration (12165x), 35% getExistingPrefixes (271x)] (12705x), resolve: 0.502 s [81% Category (117x)] (139x), ObjectMaps: 0.18 s [47% getPrimaryObjectProperty (130x), 30% getPrimaryObjectLocation (136x), 22% getLastPromoted (130x)] (533x)
2025-08-04 19:47:11,209 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 67, svn: 2.92 s [39% getDatedRevision (362x), 23% getDir2 content (109x), 22% info (582x)] (1787x), RepositoryConfigService: 1.22 s [64% getReadConfiguration (12167x), 34% getExistingPrefixes (271x)] (12707x), resolve: 0.549 s [74% Category (117x), 14% Project (12x)] (147x), PolarionAuthenticator: 0.339 s [100% authenticate (2x)] (2x), ObjectMaps: 0.197 s [47% getPrimaryObjectProperty (132x), 32% getPrimaryObjectLocation (138x), 21% getLastPromoted (132x)] (541x)
2025-08-04 19:47:14,572 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d3a0fe7856_0_661d3a0fe7856_0_: finished. Total: 0.11 s, CPU [user: 0.0472 s, system: 0.00673 s], Allocated memory: 8.8 MB, resolve: 0.0598 s [80% WorkItem (1x), 20% Attachment (1x)] (2x), svn: 0.0437 s [47% info (8x), 21% getLatestRevision (1x), 13% getDir2 content (1x)] (14x)
2025-08-04 19:49:04,458 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 115 s, CPU [user: 3.26 s, system: 0.201 s], Allocated memory: 363.0 MB, transactions: 4, SynchronizationTask: 110 s [51% Retrieving missing FEISHU-test items (1x), 48% Mapping created and updated items (1x)] (10x)
2025-08-04 19:50:39,679 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d3ad780457_0_661d3ad780457_0_: finished. Total: 0.829 s, CPU [user: 0.0923 s, system: 0.0463 s], Allocated memory: 103.4 MB, ObjectEnum Document: 0.8 s [100% available (6x)] (6x), DB: 0.225 s [46% query (194x), 33% update (194x), 21% commit (194x)] (5917x), resolve: 0.133 s [97% Module (97x)] (802x), GlobalHandler: 0.123 s [100% get (1242x)] (1244x), EHCache: 0.12 s [100% GET (802x)] (802x), GC: 0.066 s [100% G1 Young Generation (2x)] (2x)
2025-08-04 19:52:26,081 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 107 s, CPU [user: 0.335 s, system: 0.125 s], Allocated memory: 118.0 MB, transactions: 3, SynchronizationTask: 103 s [98% Retrieving missing FEISHU-test items (1x)] (10x)
2025-08-04 19:55:49,580 [ajp-nio-127.0.0.1-8889-exec-1 | cID:74f01a74-7f000001-6ae85b76-858b655a] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/WorkItemDataService': Total: 0.344 s, CPU [user: 0.0465 s, system: 0.0183 s], Allocated memory: 4.5 MB, transactions: 0, RPC: 0.258 s [100% decodeRequest (1x)] (3x)
2025-08-04 19:55:49,580 [ajp-nio-127.0.0.1-8889-exec-3 | cID:74f01b4f-7f000001-6ae85b76-9083a9c6] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.125 s, CPU [user: 0.0315 s, system: 0.0118 s], Allocated memory: 3.9 MB, transactions: 0, RPC: 0.0888 s [99% decodeRequest (1x)] (3x)
2025-08-04 19:55:49,625 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74f01a70-7f000001-6ae85b76-34c7a92e] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.391 s, CPU [user: 0.0456 s, system: 0.0207 s], Allocated memory: 3.8 MB, transactions: 0, RPC: 0.307 s [99% decodeRequest (1x)] (3x)
2025-08-04 19:55:49,627 [ajp-nio-127.0.0.1-8889-exec-2 | cID:74f01a76-7f000001-6ae85b76-179afa96] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.387 s, CPU [user: 0.0395 s, system: 0.0153 s], Allocated memory: 3.6 MB, transactions: 0, RPC: 0.307 s [99% decodeRequest (1x)] (3x)
2025-08-04 19:55:49,628 [ajp-nio-127.0.0.1-8889-exec-5 | cID:74f01b5b-7f000001-6ae85b76-d7d46d96] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.158 s, CPU [user: 0.021 s, system: 0.00408 s], Allocated memory: 2.5 MB, transactions: 0, RPC: 0.155 s [99% decodeRequest (1x)] (3x)
2025-08-04 19:55:49,878 [ajp-nio-127.0.0.1-8889-exec-9 | cID:74f01a68-7f000001-6ae85b76-a342df68] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.651 s, CPU [user: 0.261 s, system: 0.0676 s], Allocated memory: 27.5 MB, transactions: 0, RPC: 0.561 s [99% decodeRequest (1x)] (3x)
2025-08-04 19:55:50,137 [ajp-nio-127.0.0.1-8889-exec-8 | cID:74f01da0-7f000001-6ae85b76-418cf723 | u:admin] INFO  TXLOGGER - Tx 661d3c076885d_0_661d3c076885d_0_: finished. Total: 0.0873 s, CPU [user: 0.0209 s, system: 0.00908 s], Allocated memory: 2.1 MB, RepositoryConfigService: 0.0311 s [75% getReadConfiguration (155x), 25% getReadUserConfiguration (1x)] (156x), svn: 0.0127 s [46% getFile content (2x), 39% testConnection (1x)] (5x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:55:50,144 [ajp-nio-127.0.0.1-8889-exec-4 | cID:74f01da7-7f000001-6ae85b76-ecd871ae | u:admin] INFO  TXLOGGER - Tx 661d3c076ac5f_0_661d3c076ac5f_0_: finished. Total: 0.0842 s, CPU [user: 0.015 s, system: 0.00666 s], Allocated memory: 1.7 MB, RepositoryConfigService: 0.0397 s [61% getReadConfiguration (155x), 39% getReadUserConfiguration (1x)] (156x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:55:50,149 [ajp-nio-127.0.0.1-8889-exec-5 | cID:74f01d9d-7f000001-6ae85b76-cad82075 | u:admin] INFO  TXLOGGER - Tx 661d3c076805c_0_661d3c076805c_0_: finished. Total: 0.101 s, CPU [user: 0.0158 s, system: 0.0123 s], Allocated memory: 1.6 MB, RepositoryConfigService: 0.046 s [51% getReadConfiguration (155x), 49% getReadUserConfiguration (1x)] (156x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:55:50,149 [ajp-nio-127.0.0.1-8889-exec-5 | cID:74f01d9d-7f000001-6ae85b76-cad82075] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.104 s, CPU [user: 0.0175 s, system: 0.0126 s], Allocated memory: 1.8 MB, transactions: 1, UserDataService: 0.102 s [100% getUsers (1x)] (1x), RepositoryConfigService: 0.046 s [51% getReadConfiguration (155x), 49% getReadUserConfiguration (1x)] (156x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:55:50,150 [ajp-nio-127.0.0.1-8889-exec-4 | cID:74f01da7-7f000001-6ae85b76-ecd871ae] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.0949 s, CPU [user: 0.0186 s, system: 0.00779 s], Allocated memory: 2.2 MB, transactions: 1, UserDataService: 0.0854 s [100% getUsers (1x)] (1x), RepositoryConfigService: 0.0397 s [61% getReadConfiguration (155x), 39% getReadUserConfiguration (1x)] (156x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:55:50,152 [ajp-nio-127.0.0.1-8889-exec-8 | cID:74f01da0-7f000001-6ae85b76-418cf723] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.102 s, CPU [user: 0.029 s, system: 0.0128 s], Allocated memory: 3.3 MB, transactions: 1, UserDataService: 0.0879 s [100% getUsers (1x)] (1x), RepositoryConfigService: 0.0311 s [75% getReadConfiguration (155x), 25% getReadUserConfiguration (1x)] (156x), RPC: 0.0129 s [86% encodeResponse (1x)] (4x), svn: 0.0127 s [46% getFile content (2x), 39% testConnection (1x)] (5x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:55:50,221 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74f01e12-7f000001-6ae85b76-cee681d8 | u:admin] INFO  TXLOGGER - Tx 661d3c0784c60_0_661d3c0784c60_0_: finished. Total: 0.0572 s, CPU [user: 0.0103 s, system: 0.00263 s], Allocated memory: 1.4 MB, DB: 0.0351 s [94% query (2x)] (10x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 19 ]
2025-08-04 19:55:50,245 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74f01e12-7f000001-6ae85b76-cee681d8] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.0825 s, CPU [user: 0.0223 s, system: 0.00711 s], Allocated memory: 3.3 MB, transactions: 1, UserDataService: 0.058 s [100% loadUser (1x)] (1x), DB: 0.0351 s [94% query (2x)] (10x), RPC: 0.0238 s [89% encodeResponse (1x)] (4x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 19 ]
2025-08-04 19:55:55,137 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74f03050-7f000001-6ae85b76-056c8663 | u:admin] INFO  TXLOGGER - Tx 661d3c0c1a065_0_661d3c0c1a065_0_: finished. Total: 0.281 s, CPU [user: 0.0597 s, system: 0.0296 s], Allocated memory: 16.8 MB, svn: 0.133 s [91% endActivity (1x)] (7x), PullingJob Listeners: 0.0705 s [85% ExtendedListener (8x)] (16x), PersistenceEngineListener: 0.0575 s [83% objectsModified (1x)] (6x), Lucene: 0.0303 s [94% refresh (1x)] (2x), DB: 0.0152 s [78% update (9x), 12% query (2x)] (19x), processed revs: [297, 297 (delay 0s)], write: true
2025-08-04 19:55:55,147 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74f03050-7f000001-6ae85b76-056c8663 | u:admin] INFO  TXLOGGER - Tx 661d3c0c60866_0_661d3c0c60866_0_: finished. Total: 0.00897 s, CPU [user: 0.00328 s, system: 0.000749 s], Allocated memory: 677.7 kB, WeakPObjectList: [bulkResolves: 1 secondGetHits: 19 ]
2025-08-04 19:55:55,152 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74f03050-7f000001-6ae85b76-056c8663] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.32 s, CPU [user: 0.0799 s, system: 0.0371 s], Allocated memory: 19.6 MB, transactions: 2, UserDataService: 0.292 s [100% saveUser (1x)] (1x), svn: 0.133 s [91% endActivity (1x)] (7x), PullingJob Listeners: 0.0705 s [85% ExtendedListener (8x)] (16x), PersistenceEngineListener: 0.0575 s [83% objectsModified (1x)] (6x), Lucene: 0.0303 s [94% refresh (1x)] (2x), RPC: 0.0263 s [80% decodeRequest (1x)] (4x), DB: 0.0181 s [69% update (11x), 20% query (4x)] (29x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 19 ]
2025-08-04 19:55:55,226 [ajp-nio-127.0.0.1-8889-exec-5 | cID:74f031a4-7f000001-6ae85b76-3010ce71 | u:admin] INFO  TXLOGGER - Tx 661d3c0c69873_0_661d3c0c69873_0_: finished. Total: 0.0515 s, CPU [user: 0.00336 s, system: 0.00122 s], Allocated memory: 498.1 kB, permissions: 0.04 s [98% modifyInstance (25x)] (49x), GC: 0.038 s [100% G1 Young Generation (1x)] (1x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:55:55,229 [ajp-nio-127.0.0.1-8889-exec-5 | cID:74f031a4-7f000001-6ae85b76-3010ce71] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.0565 s, CPU [user: 0.00558 s, system: 0.00173 s], Allocated memory: 643.8 kB, transactions: 1, UserDataService: 0.0529 s [100% loadUsers (1x)] (1x), permissions: 0.04 s [98% modifyInstance (25x)] (49x), GC: 0.038 s [100% G1 Young Generation (1x)] (1x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 13 ]
2025-08-04 19:55:55,244 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74f031a4-7f000001-6ae85b76-aee44fa6 | u:admin] INFO  TXLOGGER - Tx 661d3c0c69872_0_661d3c0c69872_0_: finished. Total: 0.0704 s, CPU [user: 0.0102 s, system: 0.00196 s], Allocated memory: 1.7 MB, permissions: 0.0394 s [97% deleteInstances (13x)] (85x), GC: 0.038 s [100% G1 Young Generation (1x)] (1x), RepositoryConfigService: 0.0185 s [100% getReadConfiguration (30x)] (31x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 25 ]
2025-08-04 19:55:55,248 [ajp-nio-127.0.0.1-8889-exec-10 | cID:74f031a4-7f000001-6ae85b76-aee44fa6] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UserDataService': Total: 0.0757 s, CPU [user: 0.0145 s, system: 0.00247 s], Allocated memory: 3.1 MB, transactions: 1, UserDataService: 0.0711 s [100% loadUserById (1x)] (1x), permissions: 0.0394 s [97% deleteInstances (13x)] (85x), GC: 0.038 s [100% G1 Young Generation (1x)] (1x), RepositoryConfigService: 0.0185 s [100% getReadConfiguration (30x)] (31x), WeakPObjectList: [bulkResolves: 1 secondGetHits: 25 ]
2025-08-04 19:56:01,333 [Worker-1: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d3c121bc76_0_661d3c121bc76_0_: finished. Total: 0.326 s, CPU [user: 0.0875 s, system: 0.0271 s], Allocated memory: 38.8 MB, ObjectEnum Document: 0.286 s [100% available (6x)] (6x), DB: 0.215 s [50% query (194x), 27% update (194x), 22% commit (194x)] (5917x)
2025-08-04 19:56:23,094 [Worker-1: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 22.1 s, CPU [user: 0.243 s, system: 0.102 s], Allocated memory: 53.7 MB, transactions: 3, SynchronizationTask: 18.4 s [67% Mapping created and updated items (1x), 28% Retrieving missing FEISHU-test items (1x)] (10x)
2025-08-04 19:56:38,272 [Worker-1: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d3c3644479_0_661d3c3644479_0_: finished. Total: 0.239 s, CPU [user: 0.0542 s, system: 0.0232 s], Allocated memory: 38.8 MB, ObjectEnum Document: 0.207 s [100% available (6x)] (6x), DB: 0.163 s [51% query (194x), 26% update (194x), 23% commit (194x)] (5917x)
2025-08-04 19:56:41,842 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.149 s, CPU [user: 0.00202 s, system: 0.000579 s], Allocated memory: 130.8 kB, transactions: 0, PullingJob: 0.148 s [100% collectChanges (1x)] (1x), svn: 0.148 s [100% getLatestRevision (1x)] (1x)
2025-08-04 19:57:27,349 [Worker-1: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 49.4 s, CPU [user: 0.215 s, system: 0.0872 s], Allocated memory: 51.5 MB, transactions: 3, SynchronizationTask: 45.9 s [60% Mapping created and updated items (1x), 35% Retrieving missing FEISHU-test items (1x)] (10x)
2025-08-04 20:00:13,840 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.033837890625
2025-08-04 20:00:23,837 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.007763671875
2025-08-04 20:00:33,839 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.160595703125
2025-08-04 20:00:43,838 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.058984375
2025-08-04 20:03:43,843 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.19814453125
2025-08-04 20:03:53,843 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.044677734375
2025-08-04 20:04:39,328 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.104 s, CPU [user: 0.00191 s, system: 0.00273 s], Allocated memory: 130.1 kB, transactions: 0, PullingJob: 0.101 s [100% collectChanges (1x)] (1x), svn: 0.101 s [100% getLatestRevision (1x)] (1x)
2025-08-04 20:04:41,015 [ajp-nio-127.0.0.1-8889-exec-6 | cID:74f8372a-7f000001-6ae85b76-8b1ef52d] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS': Total: 0.14 s, CPU [user: 0.0406 s, system: 0.0451 s], Allocated memory: 2.8 MB, transactions: 0
2025-08-04 20:07:39,555 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.146 s, CPU [user: 0.00223 s, system: 0.0128 s], Allocated memory: 130.0 kB, transactions: 0, PullingJob: 0.137 s [100% collectChanges (1x)] (1x), svn: 0.136 s [100% getLatestRevision (1x)] (1x)
2025-08-04 20:17:53,851 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.560693359375
2025-08-04 20:18:03,848 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.502294921875
2025-08-04 20:18:13,845 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.3328125
2025-08-04 20:18:23,845 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.180712890625
2025-08-04 20:18:33,845 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.202197265625
2025-08-04 20:18:43,842 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.11630859375
2025-08-04 20:18:53,843 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.06220703125
