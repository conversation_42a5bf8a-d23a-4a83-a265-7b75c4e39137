2025-08-04 20:23:13,440 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0579 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0165 s [56% testConnection (1x), 30% getLatestRevision (2x)] (4x)
2025-08-04 20:23:13,562 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0457 s [63% getDir2 content (2x), 31% info (3x)] (6x)
2025-08-04 20:23:14,353 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.788 s, CPU [user: 0.235 s, system: 0.248 s], Allocated memory: 55.5 MB, transactions: 0, ObjectMaps: 0.112 s [99% getAllPrimaryObjects (1x)] (12x)
2025-08-04 20:23:14,353 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.788 s, CPU [user: 0.13 s, system: 0.187 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0732 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 20:23:14,353 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.787 s, CPU [user: 0.263 s, system: 0.305 s], Allocated memory: 70.9 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.108 s [36% log (1x), 23% info (5x), 20% log2 (5x), 10% getLatestRevision (2x)] (18x)
2025-08-04 20:23:14,353 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.787 s, CPU [user: 0.0527 s, system: 0.0699 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0532 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.052 s [74% log2 (5x), 15% testConnection (1x)] (7x)
2025-08-04 20:23:14,353 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.787 s, CPU [user: 0.0986 s, system: 0.138 s], Allocated memory: 14.3 MB, transactions: 0, svn: 0.0815 s [80% log2 (10x), 15% getLatestRevision (2x)] (13x), ObjectMaps: 0.0809 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 20:23:14,353 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.787 s, CPU [user: 0.071 s, system: 0.0799 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.131 s [83% log2 (10x)] (13x), ObjectMaps: 0.0478 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 20:23:14,354 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.485 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.419 s [63% log2 (36x), 11% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-08-04 20:23:14,584 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.201 s [100% getReadConfiguration (48x)] (48x), svn: 0.0773 s [90% info (18x)] (38x)
2025-08-04 20:23:14,807 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.153 s [78% info (94x), 14% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.123 s [100% getReadConfiguration (94x)] (94x), PersistenceEngineListener: 0.00819 s [100% objectsModified (8x)] (16x)
2025-08-04 20:23:15,120 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.286 s [100% doFinishStartup (1x)] (1x), DB: 0.063 s [41% update (45x), 25% commit (22x), 20% query (20x)] (122x), commit: 0.0442 s [63% Revision (1x), 37% BuildArtifact (1x)] (2x), SubterraURITable: 0.0374 s [100% addIfNotExistsDB (20x)] (20x), Lucene: 0.0363 s [100% refresh (2x)] (2x), resolve: 0.0162 s [80% BuildArtifact (11x), 20% Revision (2x)] (13x)
2025-08-04 20:23:18,222 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.982 s [92% info (158x)] (170x)
2025-08-04 20:23:18,227 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, notification worker: 0.000431 s [100% BuildActivityCreator (1x)] (1x), EHCache: 0.0000766 s [100% GET (1x)] (1x)
2025-08-04 20:23:19,938 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.09 s, CPU [user: 0.00527 s, system: 0.00178 s], Allocated memory: 556.2 kB, transactions: 1
2025-08-04 20:23:19,938 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.09 s, CPU [user: 0.00499 s, system: 0.00208 s], Allocated memory: 492.8 kB, transactions: 1
2025-08-04 20:23:19,939 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 72, resolve: 0.264 s [99% User (1x)] (3x), Lucene: 0.0845 s [59% refresh (2x), 41% add (1x)] (3x), Incremental Baseline: 0.0407 s [100% WorkItem (23x)] (23x), notification worker: 0.0239 s [57% RevisionActivityCreator (18x), 22% WorkItemActivityCreator (9x), 10% TestRunActivityCreator (9x)] (53x), ObjectMaps: 0.0231 s [100% getPrimaryObjectLocation (1x)] (1x), Full Baseline: 0.0223 s [100% WorkItem (2x)] (2x), persistence listener: 0.0167 s [79% indexRefreshPersistenceListener (9x), 11% WorkItemActivityCreator (9x)] (63x)
2025-08-04 20:23:19,939 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.72 s, CPU [user: 0.19 s, system: 0.0364 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.886 s [99% getDatedRevision (181x)] (183x), GC: 0.236 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.126 s [82% buildBaselineSnapshots (1x)] (27x)
2025-08-04 20:23:20,823 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d4250f5c45_0_661d4250f5c45_0_: finished. Total: 2.59 s, CPU [user: 0.506 s, system: 0.147 s], Allocated memory: 57.0 MB, svn: 1.87 s [46% getDir2 content (25x), 45% getDatedRevision (181x)] (328x), resolve: 0.565 s [100% Category (117x)] (117x), GC: 0.236 s [100% G1 Young Generation (1x)] (1x), ObjectMaps: 0.21 s [40% getPrimaryObjectLocation (117x), 37% getPrimaryObjectProperty (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 20:23:21,065 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d425395c70_0_661d425395c70_0_: finished. Total: 0.146 s, CPU [user: 0.0595 s, system: 0.0128 s], Allocated memory: 8.7 MB, RepositoryConfigService: 0.0628 s [52% getReadUserConfiguration (10x), 48% getReadConfiguration (180x)] (190x), svn: 0.062 s [54% info (21x), 37% getFile content (17x)] (40x), resolve: 0.0425 s [100% User (9x)] (9x), ObjectMaps: 0.0225 s [54% getPrimaryObjectProperty (9x), 24% getPrimaryObjectLocation (9x), 22% getLastPromoted (9x)] (37x), GlobalHandler: 0.0133 s [96% applyTxChanges (2x)] (29x)
2025-08-04 20:23:21,280 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d4253c8072_0_661d4253c8072_0_: finished. Total: 0.16 s, CPU [user: 0.0433 s, system: 0.00705 s], Allocated memory: 19.8 MB, svn: 0.129 s [82% getDir2 content (17x)] (62x), RepositoryConfigService: 0.04 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 20:23:22,119 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d4253f0073_0_661d4253f0073_0_: finished. Total: 0.838 s, CPU [user: 0.355 s, system: 0.0406 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.634 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.461 s [67% getFile content (412x), 33% getDir2 content (21x)] (434x)
2025-08-04 20:23:22,258 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d4254c1c74_0_661d4254c1c74_0_: finished. Total: 0.138 s, CPU [user: 0.0282 s, system: 0.00354 s], Allocated memory: 17.9 MB, svn: 0.124 s [86% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0276 s [98% getReadConfiguration (124x)] (148x)
2025-08-04 20:23:22,554 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d4254ed476_0_661d4254ed476_0_: finished. Total: 0.26 s, CPU [user: 0.096 s, system: 0.00652 s], Allocated memory: 385.0 MB, svn: 0.171 s [57% getDir2 content (21x), 43% getFile content (186x)] (208x), RepositoryConfigService: 0.144 s [96% getReadConfiguration (2788x)] (3026x)
2025-08-04 20:23:22,628 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.4 s, CPU [user: 1.19 s, system: 0.235 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.98 s [47% getDir2 content (133x), 28% getDatedRevision (181x), 22% getFile content (867x)] (1226x), RepositoryConfigService: 0.989 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.662 s [85% Category (117x)] (139x), GC: 0.283 s [100% G1 Young Generation (6x)] (6x), ObjectMaps: 0.251 s [41% getPrimaryObjectProperty (132x), 37% getPrimaryObjectLocation (138x), 22% getLastPromoted (132x)] (541x)
2025-08-04 20:23:22,628 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 3.87 s [44% getDatedRevision (362x), 36% getDir2 content (133x)] (1410x), RepositoryConfigService: 0.989 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.662 s [85% Category (117x)] (140x), ObjectMaps: 0.251 s [41% getPrimaryObjectProperty (132x), 37% getPrimaryObjectLocation (138x), 22% getLastPromoted (132x)] (541x), Lucene: 0.203 s [51% buildBaselineSnapshots (2x), 34% search (5x)] (60x)
2025-08-04 20:23:36,075 [ajp-nio-127.0.0.1-8889-exec-2 | cID:750987ac-c0a844bd-5d36212a-e31ef983 | u:admin | POST:/polarion/checklist/api/templates?projectId=WBS] INFO  TXLOGGER - Tx 661d42623c47a_0_661d42623c47a_0_: finished. Total: 0.153 s, CPU [user: 0.0235 s, system: 0.00612 s], Allocated memory: 3.2 MB, svn: 0.127 s [89% endActivity (1x)] (8x), PullingJob Listeners: 0.0225 s [90% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0198 s [99% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0197 s [98% objectsCreated (1x)] (3x), processed revs: [298, 298 (delay 0s)], write: true
2025-08-04 20:23:36,097 [ajp-nio-127.0.0.1-8889-exec-2 | cID:750987ac-c0a844bd-5d36212a-e31ef983] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS': Total: 0.5 s, CPU [user: 0.171 s, system: 0.0579 s], Allocated memory: 14.1 MB, transactions: 3, svn: 0.144 s [78% endActivity (1x), 12% info (5x)] (12x), RepositoryConfigService: 0.0279 s [99% getReadConfiguration (1x)] (4x), resolve: 0.025 s [96% User (1x)] (3x)
2025-08-04 20:27:33,090 [ajp-nio-127.0.0.1-8889-exec-5 | cID:750d26f7-c0a844bd-5d36212a-3b010ecc] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/1?projectId=default-project&_t=1754310452908': Total: 0.104 s, CPU [user: 0.0102 s, system: 0.0225 s], Allocated memory: 349.5 kB, transactions: 0
2025-08-04 20:30:20,131 [ajp-nio-127.0.0.1-8889-exec-10 | cID:750fb327-c0a844bd-5d36212a-2e91c0d7] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews?projectId=WBS': Total: 0.18 s, CPU [user: 0.0117 s, system: 0.0309 s], Allocated memory: 733.8 kB, transactions: 1
2025-08-04 20:33:46,184 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.155859375
2025-08-04 20:33:56,180 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0537109375
2025-08-04 20:34:28,615 [ajp-nio-127.0.0.1-8889-exec-3 | cID:75137e0d-c0a844bd-5d36212a-160a6117] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates/types?projectId=WBS&_t=1754310868336': Total: 0.118 s, CPU [user: 0.00483 s, system: 0.0219 s], Allocated memory: 55.8 kB, transactions: 0
2025-08-04 20:34:45,862 [ajp-nio-127.0.0.1-8889-exec-5 | cID:7513c17a-c0a844bd-5d36212a-88cae64a] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754310885714': Total: 0.102 s, CPU [user: 0.0107 s, system: 0.0213 s], Allocated memory: 971.0 kB, transactions: 0, svn: 0.0788 s [35% testConnection (1x), 27% getFile content (1x), 26% info (2x)] (5x)
