2025-08-04 20:46:41,805 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0699 s [69% update (144x), 31% query (12x)] (221x), svn: 0.00931 s [53% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-08-04 20:46:41,926 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0352 s [64% getDir2 content (2x), 29% info (3x)] (6x)
2025-08-04 20:46:42,699 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.771 s, CPU [user: 0.219 s, system: 0.285 s], Allocated memory: 53.5 MB, transactions: 0, ObjectMaps: 0.111 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 20:46:42,699 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.771 s, CPU [user: 0.123 s, system: 0.218 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0864 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 20:46:42,700 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.771 s, CPU [user: 0.234 s, system: 0.338 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.122 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 20:46:42,700 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.0638 s, system: 0.106 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.101 s [87% log2 (10x)] (13x), ObjectMaps: 0.0518 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 20:46:42,700 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.0535 s, system: 0.0896 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.087 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 20:46:42,700 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.115 s, system: 0.18 s], Allocated memory: 16.6 MB, transactions: 0, svn: 0.143 s [32% log (1x), 30% log2 (10x), 15% info (5x), 14% getLatestRevision (3x)] (24x), ObjectMaps: 0.0968 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 20:46:42,700 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.554 s [100% getAllPrimaryObjects (8x)] (56x), svn: 0.34 s [55% log2 (36x), 13% log (1x), 13% getLatestRevision (9x)] (61x)
2025-08-04 20:46:42,939 [main | u:p] INFO  TXLOGGER - Tx 661d47ac89c01_0_661d47ac89c01_0_: finished. Total: 0.21 s, CPU [user: 0.0926 s, system: 0.00845 s], Allocated memory: 21.8 MB
2025-08-04 20:46:43,072 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.333 s [100% getReadConfiguration (48x)] (48x), svn: 0.0669 s [89% info (18x)] (38x)
2025-08-04 20:46:43,318 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.172 s [79% info (94x), 14% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.139 s [100% getReadConfiguration (94x)] (94x), PersistenceEngineListener: 0.00912 s [100% objectsModified (8x)] (16x)
2025-08-04 20:46:43,527 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.101 s, CPU [user: 0.0231 s, system: 0.00501 s], Allocated memory: 8.6 MB
2025-08-04 20:46:43,628 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.283 s [100% doFinishStartup (1x)] (1x), commit: 0.0487 s [56% Revision (1x), 44% BuildArtifact (1x)] (2x), DB: 0.0481 s [51% update (45x), 20% query (20x), 17% execute (15x)] (122x), Lucene: 0.0385 s [100% refresh (2x)] (2x), SubterraURITable: 0.0253 s [100% addIfNotExistsDB (20x)] (20x), resolve: 0.0226 s [84% BuildArtifact (11x)] (13x), GlobalHandler: 0.0174 s [90% put (13x)] (26x)
2025-08-04 20:46:45,891 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.281 s [88% info (158x)] (168x)
2025-08-04 20:46:46,629 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.672 s, CPU [user: 0.00452 s, system: 0.00118 s], Allocated memory: 496.8 kB, transactions: 1
2025-08-04 20:46:46,629 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.673 s, CPU [user: 0.0043 s, system: 0.00143 s], Allocated memory: 549.3 kB, transactions: 1
2025-08-04 20:46:46,630 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 72, Incremental Baseline: 0.0255 s [100% WorkItem (23x)] (23x), notification worker: 0.0223 s [54% RevisionActivityCreator (18x), 16% WorkItemActivityCreator (9x), 16% TestRunActivityCreator (9x)] (54x), Lucene: 0.0166 s [60% refresh (2x), 40% add (1x)] (3x), resolve: 0.0151 s [75% User (1x), 25% Revision (2x)] (3x), Full Baseline: 0.0129 s [100% WorkItem (2x)] (2x), persistence listener: 0.0095 s [81% indexRefreshPersistenceListener (9x)] (63x), ObjectMaps: 0.00323 s [100% getPrimaryObjectLocation (1x)] (1x), DB: 0.00218 s [68% update (2x), 32% commit (2x)] (4x), GlobalHandler: 0.00154 s [75% applyTxChanges (2x), 25% get (3x)] (5x), EHCache: 0.00145 s [99% GET (15x)] (40x)
2025-08-04 20:46:46,631 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.742 s, CPU [user: 0.151 s, system: 0.0217 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.583 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0627 s [68% buildBaselineSnapshots (1x), 32% buildBaseline (26x)] (27x)
2025-08-04 20:46:46,971 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d47afa2445_0_661d47afa2445_0_: finished. Total: 1.07 s, CPU [user: 0.342 s, system: 0.0854 s], Allocated memory: 58.9 MB, svn: 0.614 s [52% getDatedRevision (181x), 28% getDir2 content (25x), 19% getFile content (119x)] (328x), resolve: 0.395 s [100% Category (117x)] (117x), ObjectMaps: 0.133 s [42% getPrimaryObjectProperty (117x), 34% getPrimaryObjectLocation (117x), 24% getLastPromoted (117x)] (473x)
2025-08-04 20:46:47,151 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d47b0c1070_0_661d47b0c1070_0_: finished. Total: 0.107 s, CPU [user: 0.0471 s, system: 0.00838 s], Allocated memory: 8.8 MB, RepositoryConfigService: 0.041 s [54% getReadUserConfiguration (10x), 46% getReadConfiguration (180x)] (190x), svn: 0.0405 s [52% info (21x), 38% getFile content (17x)] (41x), resolve: 0.034 s [100% User (9x)] (9x), ObjectMaps: 0.0183 s [50% getPrimaryObjectProperty (9x), 27% getPrimaryObjectLocation (9x), 22% getLastPromoted (9x)] (37x), GlobalHandler: 0.0131 s [97% applyTxChanges (2x)] (29x), GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 20:46:47,321 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d47b0e4872_0_661d47b0e4872_0_: finished. Total: 0.135 s, CPU [user: 0.0492 s, system: 0.00443 s], Allocated memory: 19.6 MB, svn: 0.0977 s [66% getDir2 content (17x), 34% getFile content (44x)] (62x), RepositoryConfigService: 0.0513 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 20:46:48,127 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d47b106473_0_661d47b106473_0_: finished. Total: 0.805 s, CPU [user: 0.326 s, system: 0.0159 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.49 s [96% getReadConfiguration (8682x)] (9021x), svn: 0.479 s [56% getDir2 content (21x), 44% getFile content (412x)] (434x)
2025-08-04 20:46:48,243 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d47b1cfc74_0_661d47b1cfc74_0_: finished. Total: 0.116 s, CPU [user: 0.0208 s, system: 0.00402 s], Allocated memory: 17.8 MB, svn: 0.103 s [88% getDir2 content (18x)] (48x), RepositoryConfigService: 0.023 s [98% getReadConfiguration (124x)] (148x)
2025-08-04 20:46:48,516 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d47b1f3c76_0_661d47b1f3c76_0_: finished. Total: 0.245 s, CPU [user: 0.0971 s, system: 0.00571 s], Allocated memory: 383.2 MB, svn: 0.156 s [51% getDir2 content (21x), 49% getFile content (186x)] (208x), RepositoryConfigService: 0.15 s [96% getReadConfiguration (2788x)] (3026x)
2025-08-04 20:46:48,580 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.68 s, CPU [user: 0.96 s, system: 0.134 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.61 s [46% getDir2 content (133x), 31% getFile content (867x), 20% getDatedRevision (181x)] (1227x), RepositoryConfigService: 0.81 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.472 s [84% Category (117x)] (139x), ObjectMaps: 0.168 s [45% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 23% getLastPromoted (132x)] (541x)
2025-08-04 20:46:48,580 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 2.2 s [41% getDatedRevision (362x), 34% getDir2 content (133x), 23% getFile content (867x)] (1412x), RepositoryConfigService: 0.81 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.473 s [83% Category (117x)] (140x), ObjectMaps: 0.168 s [45% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 23% getLastPromoted (132x)] (541x)
2025-08-04 20:47:02,788 [ajp-nio-127.0.0.1-8889-exec-2 | cID:751eff06-c613f903-0be57341-69267f75] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754311622386': Total: 0.381 s, CPU [user: 0.106 s, system: 0.0789 s], Allocated memory: 9.1 MB, transactions: 5, resolve: 0.106 s [62% WorkItem (1x), 38% User (1x)] (2x), svn: 0.0522 s [44% info (5x), 19% getFile content (2x), 15% testConnection (1x), 12% getLatestRevision (1x)] (14x), RepositoryConfigService: 0.046 s [76% getReadConfiguration (6x), 24% getExistingPrefixes (1x)] (7x), GlobalHandler: 0.0392 s [97% get (10x)] (15x), EHCache: 0.0363 s [100% GET (4x)] (5x)
2025-08-04 20:47:03,154 [ajp-nio-127.0.0.1-8889-exec-4 | cID:751f0106-c613f903-0be57341-90092f0d | u:admin | POST:/polarion/checklist/api/reviews?projectId=WBS] INFO  TXLOGGER - Tx 661d47c04fc7d_0_661d47c04fc7d_0_: finished. Total: 0.18 s, CPU [user: 0.0266 s, system: 0.00691 s], Allocated memory: 3.4 MB, svn: 0.153 s [93% endActivity (1x)] (6x), PullingJob Listeners: 0.0222 s [92% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0202 s [99% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.02 s [99% objectsCreated (1x)] (3x), processed revs: [299, 299 (delay 0s)], write: true
2025-08-04 20:47:03,367 [ajp-nio-127.0.0.1-8889-exec-4 | cID:751f0106-c613f903-0be57341-90092f0d] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews?projectId=WBS': Total: 0.448 s, CPU [user: 0.102 s, system: 0.0201 s], Allocated memory: 10.6 MB, transactions: 3, svn: 0.329 s [81% endActivity (3x)] (25x), PullingJob Listeners: 0.0346 s [93% StorageListener (3x)] (48x), RevisionsPersistenceModule Listeners: 0.0318 s [100% PersistenceEngineListener (3x)] (9x), PersistenceEngineListener: 0.0316 s [99% objectsCreated (3x)] (9x)
2025-08-04 20:47:03,686 [ajp-nio-127.0.0.1-8889-exec-6 | cID:751f0319-c613f903-0be57341-eb014b9d | u:admin | POST:/polarion/checklist/api/template-version/review-versions?projectId=WBS] INFO  TXLOGGER - Tx 661d47c0e4895_0_661d47c0e4895_0_: finished. Total: 0.117 s, CPU [user: 0.00792 s, system: 0.00218 s], Allocated memory: 1.3 MB, svn: 0.108 s [96% endActivity (1x)] (5x), GC: 0.047 s [100% G1 Young Generation (1x)] (1x), processed revs: [303, 303 (delay 0s)], write: true
2025-08-04 20:47:03,692 [ajp-nio-127.0.0.1-8889-exec-6 | cID:751f0319-c613f903-0be57341-eb014b9d] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-version/review-versions?projectId=WBS': Total: 0.243 s, CPU [user: 0.0325 s, system: 0.00697 s], Allocated memory: 4.5 MB, transactions: 2, svn: 0.198 s [85% endActivity (2x)] (14x), GC: 0.047 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 20:47:36,636 [ajp-nio-127.0.0.1-8889-exec-3 | cID:751f836e-c613f903-0be57341-be9a31a9] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.333 s, CPU [user: 0.0384 s, system: 0.0251 s], Allocated memory: 9.0 MB, transactions: 0, PolarionAuthenticator: 0.25 s [100% authenticate (1x)] (1x), RPC: 0.061 s [98% decodeRequest (1x)] (3x)
2025-08-04 20:47:36,636 [ajp-nio-127.0.0.1-8889-exec-2 | cID:751f8368-c613f903-0be57341-24b624dd] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.34 s, CPU [user: 0.152 s, system: 0.0279 s], Allocated memory: 35.4 MB, transactions: 0, PolarionAuthenticator: 0.252 s [100% authenticate (1x)] (1x), RPC: 0.0612 s [98% decodeRequest (1x)] (3x)
2025-08-04 20:47:36,740 [ajp-nio-127.0.0.1-8889-exec-4 | cID:751f837d-c613f903-0be57341-c33a0396] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/?url=WBS': Total: 0.422 s, CPU [user: 0.0641 s, system: 0.0202 s], Allocated memory: 15.6 MB, transactions: 15, PolarionAuthenticator: 0.235 s [100% authenticate (1x)] (1x)
2025-08-04 20:54:37,227 [ajp-nio-127.0.0.1-8889-exec-4 | cID:7525eefb-c613f903-0be57341-d98be276] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1/items/item_1754308945532_0.3181767251506181?projectId=WBS': Total: 0.164 s, CPU [user: 0.0178 s, system: 0.039 s], Allocated memory: 526.2 kB, transactions: 0, svn: 0.0393 s [94% info (1x)] (2x)
2025-08-04 20:54:37,227 [ajp-nio-127.0.0.1-8889-exec-5 | cID:7525eefb-c613f903-0be57341-8bc531df] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1/items/item_1754308963391_0.5443518235211999?projectId=WBS': Total: 0.148 s, CPU [user: 0.0171 s, system: 0.033 s], Allocated memory: 1.1 MB, transactions: 0
2025-08-04 20:58:44,534 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.1 s, CPU [user: 0.002 s, system: 0.00165 s], Allocated memory: 130.7 kB, transactions: 0, PullingJob: 0.0839 s [100% collectChanges (1x)] (1x), svn: 0.0838 s [100% getLatestRevision (1x)] (1x)
2025-08-04 21:07:57,186 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.131 s, CPU [user: 0.00248 s, system: 0.0135 s], Allocated memory: 129.8 kB, transactions: 0, PullingJob: 0.0718 s [100% collectChanges (1x)] (1x), svn: 0.0472 s [100% getLatestRevision (1x)] (1x)
2025-08-04 21:13:41,565 [ajp-nio-127.0.0.1-8889-exec-7 | cID:75376547-c613f903-0be57341-4489c0c0] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754313221411': Total: 0.112 s, CPU [user: 0.00851 s, system: 0.0365 s], Allocated memory: 395.8 kB, transactions: 0, svn: 0.0373 s [81% testConnection (1x)] (3x)
2025-08-04 21:20:25,616 [ajp-nio-127.0.0.1-8889-exec-1 | cID:753d8f9f-c613f903-0be57341-0909558f] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754313625451': Total: 0.111 s, CPU [user: 0.009 s, system: 0.0349 s], Allocated memory: 286.1 kB, transactions: 0, svn: 0.0342 s [84% info (1x)] (2x)
2025-08-04 21:20:50,386 [ajp-nio-127.0.0.1-8889-exec-9 | cID:753df050-c613f903-0be57341-9c01000a] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754313650219': Total: 0.114 s, CPU [user: 0.0116 s, system: 0.0434 s], Allocated memory: 312.2 kB, transactions: 0, svn: 0.0289 s [87% info (1x)] (2x)
2025-08-04 21:23:01,134 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.146 s, CPU [user: 0.00207 s, system: 0.00441 s], Allocated memory: 129.7 kB, transactions: 0, PullingJob: 0.141 s [100% collectChanges (1x)] (1x), svn: 0.14 s [100% getLatestRevision (1x)] (1x)
2025-08-04 21:23:46,205 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.146 s, CPU [user: 0.00186 s, system: 0.00377 s], Allocated memory: 129.9 kB, transactions: 0, PullingJob: 0.144 s [100% collectChanges (1x)] (1x), svn: 0.144 s [100% getLatestRevision (1x)] (1x)
2025-08-04 21:33:22,967 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.248 s, CPU [user: 0.00206 s, system: 0.00797 s], Allocated memory: 129.9 kB, transactions: 0, PullingJob: 0.173 s [100% collectChanges (1x)] (1x), svn: 0.169 s [100% getLatestRevision (1x)] (1x)
2025-08-04 21:36:24,572 [ajp-nio-127.0.0.1-8889-exec-9 | cID:754c314f-c613f903-0be57341-ec16f936] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/template-version/reviews/WBS-1/check-upgrade?projectId=WBS&_t=1754314584238': Total: 0.117 s, CPU [user: 0.00982 s, system: 0.015 s], Allocated memory: 606.0 kB, transactions: 0, svn: 0.0299 s [80% info (2x), 20% getFile content (1x)] (3x)
2025-08-04 21:38:56,198 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.46240234375
2025-08-04 21:39:06,190 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.2830078125
2025-08-04 21:39:16,193 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.14716796875
2025-08-04 21:39:26,192 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.047607421875
2025-08-04 21:44:38,583 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.151 s, CPU [user: 0.00163 s, system: 0.00264 s], Allocated memory: 130.2 kB, transactions: 0, PullingJob: 0.0589 s [100% collectChanges (1x)] (1x), svn: 0.0588 s [100% getLatestRevision (1x)] (1x)
