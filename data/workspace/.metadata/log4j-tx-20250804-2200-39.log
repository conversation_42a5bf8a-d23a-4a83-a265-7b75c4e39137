2025-08-04 22:00:58,441 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0561 s [54% update (144x), 46% query (12x)] (221x), svn: 0.0137 s [46% testConnection (1x), 41% getLatestRevision (2x)] (4x)
2025-08-04 22:00:58,607 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0599 s [68% getDir2 content (2x), 25% info (3x)] (6x)
2025-08-04 22:00:59,648 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.246 s, system: 0.325 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.163 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:00:59,648 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.138 s, system: 0.233 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.11 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:00:59,648 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.109 s, system: 0.146 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.123 s [39% log2 (10x), 34% log (1x), 12% info (5x)] (24x), ObjectMaps: 0.105 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 22:00:59,648 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.0579 s, system: 0.0885 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.111 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 22:00:59,648 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.287 s, system: 0.398 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.18 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:00:59,648 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.0787 s, system: 0.121 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.101 s [66% log2 (10x), 29% getLatestRevision (2x)] (13x), ObjectMaps: 0.0569 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 22:00:59,649 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.727 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.349 s [57% log2 (36x), 16% getLatestRevision (9x), 12% log (1x)] (61x)
2025-08-04 22:00:59,910 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.217 s [100% getReadConfiguration (48x)] (48x), svn: 0.0845 s [88% info (18x)] (38x)
2025-08-04 22:01:00,257 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.228 s [78% info (94x), 14% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.178 s [100% getReadConfiguration (94x)] (94x), PersistenceEngineListener: 0.0242 s [100% objectsModified (8x)] (16x)
2025-08-04 22:01:00,566 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.132 s, CPU [user: 0.045 s, system: 0.0112 s], Allocated memory: 9.9 MB
2025-08-04 22:01:00,650 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [WikiPageAttachment]: finished. Total: 0.115 s, CPU [user: 0.000644 s, system: 0.000314 s], Allocated memory: 78.9 kB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 22:01:00,761 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.466 s [100% doFinishStartup (1x)] (1x), DB: 0.118 s [40% update (45x), 27% execute (15x), 22% query (20x)] (122x), commit: 0.0761 s [73% Revision (1x), 27% BuildArtifact (1x)] (2x), SubterraURITable: 0.0622 s [100% addIfNotExistsDB (20x)] (20x), Lucene: 0.039 s [100% refresh (2x)] (2x), resolve: 0.0332 s [83% BuildArtifact (11x)] (13x), GlobalHandler: 0.026 s [75% put (13x), 25% get (13x)] (26x)
2025-08-04 22:01:04,068 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.394 s [82% info (158x)] (170x)
2025-08-04 22:01:04,421 [ajp-nio-127.0.0.1-8889-exec-1 | cID:7562c4bc-c0a844bd-409140b2-065fb219] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews?projectId=WBS': Total: 0.488 s, CPU [user: 0.234 s, system: 0.0321 s], Allocated memory: 18.2 MB, transactions: 6, resolve: 0.223 s [55% WorkItem (1x), 45% User (1x)] (2x), svn: 0.104 s [41% info (12x), 19% getDir2 content (2x), 15% getFile content (5x), 13% getLatestRevision (2x)] (28x), RepositoryConfigService: 0.075 s [72% getReadConfiguration (6x), 28% getExistingPrefixes (1x)] (7x), calculatedFieldsContributor: 0.0314 s [100% fields (1x)] (1x), ObjectMaps: 0.0257 s [49% getPrimaryObjectProperty (2x), 42% getPrimaryObjectLocation (2x)] (8x)
2025-08-04 22:01:04,917 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.798 s, CPU [user: 0.00462 s, system: 0.00158 s], Allocated memory: 404.6 kB, transactions: 1
2025-08-04 22:01:04,917 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.798 s, CPU [user: 0.00557 s, system: 0.00227 s], Allocated memory: 711.0 kB, transactions: 1
2025-08-04 22:01:04,917 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 78, resolve: 0.229 s [53% WorkItem (1x), 45% User (2x)] (5x), svn: 0.104 s [41% info (12x), 19% getDir2 content (2x), 15% getFile content (5x), 13% getLatestRevision (2x)] (28x), RepositoryConfigService: 0.075 s [72% getReadConfiguration (6x), 28% getExistingPrefixes (1x)] (7x), Incremental Baseline: 0.0387 s [100% WorkItem (23x)] (23x), calculatedFieldsContributor: 0.0314 s [100% fields (1x)] (1x), ObjectMaps: 0.028 s [47% getPrimaryObjectLocation (3x), 45% getPrimaryObjectProperty (2x)] (9x), Lucene: 0.0268 s [59% refresh (2x), 41% add (1x)] (3x), Full Baseline: 0.018 s [100% WorkItem (2x)] (2x), notification worker: 0.0164 s [62% RevisionActivityCreator (18x), 11% WorkItemActivityCreator (9x), 10% TestRunActivityCreator (9x)] (54x), persistence listener: 0.015 s [74% indexRefreshPersistenceListener (9x), 14% WorkItemActivityCreator (9x)] (63x), icvaDerivedFieldsContributor: 0.0124 s [100% fields (1x)] (1x)
2025-08-04 22:01:04,918 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.857 s, CPU [user: 0.167 s, system: 0.0299 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.686 s [99% getDatedRevision (181x)] (183x), Lucene: 0.074 s [79% buildBaselineSnapshots (1x), 21% buildBaseline (26x)] (27x)
2025-08-04 22:01:05,418 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d58b153047_0_661d58b153047_0_: finished. Total: 1.34 s, CPU [user: 0.402 s, system: 0.12 s], Allocated memory: 56.5 MB, svn: 0.786 s [56% getDatedRevision (181x), 23% getDir2 content (24x), 18% getFile content (119x)] (337x), resolve: 0.506 s [100% Category (117x)] (117x), ObjectMaps: 0.201 s [45% getPrimaryObjectProperty (117x), 31% getPrimaryObjectLocation (117x), 25% getLastPromoted (117x)] (473x)
2025-08-04 22:01:05,639 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d58b2bb076_0_661d58b2bb076_0_: finished. Total: 0.123 s, CPU [user: 0.0531 s, system: 0.0109 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0595 s [56% getReadConfiguration (180x), 44% getReadUserConfiguration (10x)] (190x), svn: 0.0583 s [58% info (21x), 38% getFile content (16x)] (39x), resolve: 0.0357 s [100% User (9x)] (9x), ObjectMaps: 0.0149 s [46% getPrimaryObjectProperty (8x), 29% getLastPromoted (8x), 24% getPrimaryObjectLocation (8x)] (33x)
2025-08-04 22:01:05,924 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d58b2eec78_0_661d58b2eec78_0_: finished. Total: 0.201 s, CPU [user: 0.0614 s, system: 0.00893 s], Allocated memory: 20.7 MB, svn: 0.163 s [52% getDir2 content (13x), 31% info (35x)] (93x), RepositoryConfigService: 0.0662 s [70% getReadConfiguration (170x), 30% getExistingPrefixes (11x)] (192x)
2025-08-04 22:01:07,067 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d58b321079_0_661d58b321079_0_: finished. Total: 1.14 s, CPU [user: 0.399 s, system: 0.0388 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.808 s [68% getReadConfiguration (8682x), 32% getExistingPrefixes (129x)] (9021x), svn: 0.762 s [34% info (226x), 33% getDir2 content (17x), 33% getFile content (412x)] (656x)
2025-08-04 22:01:07,855 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d58b46047c_0_661d58b46047c_0_: finished. Total: 0.654 s, CPU [user: 0.153 s, system: 0.016 s], Allocated memory: 391.1 MB, RepositoryConfigService: 0.486 s [52% getReadConfiguration (2788x), 48% getExistingPrefixes (89x)] (3026x), svn: 0.472 s [51% info (152x), 26% getDir2 content (17x), 23% getFile content (186x)] (356x)
2025-08-04 22:01:07,970 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d58b503c7d_0_661d58b503c7d_0_: finished. Total: 0.114 s, CPU [user: 0.0231 s, system: 0.00397 s], Allocated memory: 13.3 MB, svn: 0.103 s [48% getDir2 content (13x), 34% info (37x)] (83x), RepositoryConfigService: 0.0388 s [65% getReadConfiguration (128x), 35% getExistingPrefixes (12x)] (150x)
2025-08-04 22:01:07,970 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.9 s, CPU [user: 1.19 s, system: 0.215 s], Allocated memory: 1.7 GB, transactions: 11, svn: 2.58 s [29% getDir2 content (108x), 29% info (590x), 24% getFile content (865x)] (1761x), RepositoryConfigService: 1.55 s [63% getReadConfiguration (12166x), 36% getExistingPrefixes (271x)] (12860x), resolve: 0.596 s [85% Category (117x)] (139x), ObjectMaps: 0.236 s [47% getPrimaryObjectProperty (131x), 29% getPrimaryObjectLocation (137x), 24% getLastPromoted (131x)] (537x)
2025-08-04 22:01:07,970 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 3.26 s [34% getDatedRevision (362x), 23% getDir2 content (108x), 23% info (590x)] (1945x), RepositoryConfigService: 1.55 s [63% getReadConfiguration (12166x), 36% getExistingPrefixes (271x)] (12860x), resolve: 0.596 s [85% Category (117x)] (140x), ObjectMaps: 0.236 s [47% getPrimaryObjectProperty (131x), 29% getPrimaryObjectLocation (137x), 24% getLastPromoted (131x)] (537x)
2025-08-04 22:01:13,645 [ajp-nio-127.0.0.1-8889-exec-3 | cID:7562ea04-c0a844bd-409140b2-7058f14b] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754316073430': Total: 0.163 s, CPU [user: 0.0234 s, system: 0.0423 s], Allocated memory: 902.9 kB, transactions: 0, svn: 0.0631 s [80% testConnection (1x), 13% info (1x)] (3x)
