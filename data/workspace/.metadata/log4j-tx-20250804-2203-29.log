2025-08-04 22:03:36,800 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0762 s [71% update (144x), 29% query (12x)] (221x), svn: 0.0131 s [54% getLatestRevision (2x), 37% testConnection (1x)] (4x)
2025-08-04 22:03:36,905 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0337 s [61% getDir2 content (2x), 28% info (3x)] (6x)
2025-08-04 22:03:37,664 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.755 s, CPU [user: 0.118 s, system: 0.232 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0773 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0453 s [82% log2 (5x)] (7x)
2025-08-04 22:03:37,664 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.755 s, CPU [user: 0.207 s, system: 0.298 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:03:37,664 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.755 s, CPU [user: 0.0828 s, system: 0.132 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.0879 s [83% log2 (10x)] (13x), ObjectMaps: 0.0594 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 22:03:37,664 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.755 s, CPU [user: 0.241 s, system: 0.357 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.126 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:03:37,664 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.755 s, CPU [user: 0.0858 s, system: 0.153 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0921 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0498 s [78% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-08-04 22:03:37,665 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.755 s, CPU [user: 0.0753 s, system: 0.104 s], Allocated memory: 9.7 MB, transactions: 0, svn: 0.0894 s [32% log (1x), 25% log2 (5x), 20% testConnection (1x), 12% info (5x)] (18x), ObjectMaps: 0.0733 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 22:03:37,665 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.552 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.31 s [62% log2 (36x), 13% testConnection (6x), 12% getLatestRevision (9x)] (61x)
2025-08-04 22:03:37,875 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.179 s [100% getReadConfiguration (48x)] (48x), svn: 0.0595 s [85% info (18x)] (38x)
2025-08-04 22:03:38,123 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.181 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.144 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 22:03:38,346 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.21 s [100% doFinishStartup (1x)] (1x), commit: 0.0471 s [100% Revision (1x)] (1x), Lucene: 0.0275 s [100% refresh (1x)] (1x), DB: 0.0154 s [58% query (1x), 25% update (3x)] (8x), derivedLinkedRevisionsContributor: 0.0144 s [100% objectsToInv (1x)] (1x), SubterraURITable: 0.0114 s [100% addIfNotExistsDB (1x)] (1x)
2025-08-04 22:03:40,618 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.281 s [90% info (158x)] (168x)
2025-08-04 22:03:41,424 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.696 s, CPU [user: 0.00679 s, system: 0.00187 s], Allocated memory: 693.2 kB, transactions: 1
2025-08-04 22:03:41,425 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, persistence listener: 0.0297 s [62% indexRefreshPersistenceListener (1x), 21% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.027 s [100% WorkItem (23x)] (23x), notification worker: 0.0263 s [81% RevisionActivityCreator (2x)] (6x), Lucene: 0.0243 s [74% add (1x), 26% refresh (1x)] (2x), resolve: 0.0236 s [60% User (1x), 40% Revision (2x)] (3x), Full Baseline: 0.021 s [100% WorkItem (2x)] (2x), PullingJob: 0.00685 s [100% collectChanges (1x)] (1x), svn: 0.00656 s [52% getLatestRevision (1x), 48% testConnection (1x)] (2x), permissions: 0.0028 s [100% readInstance (2x)] (2x), ObjectMaps: 0.00217 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-08-04 22:03:41,426 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.833 s, CPU [user: 0.165 s, system: 0.0241 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.597 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0664 s [80% buildBaselineSnapshots (1x), 20% buildBaseline (26x)] (27x)
2025-08-04 22:03:41,810 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d594a37c41_0_661d594a37c41_0_: finished. Total: 1.17 s, CPU [user: 0.379 s, system: 0.0911 s], Allocated memory: 58.6 MB, svn: 0.691 s [50% getDatedRevision (181x), 29% getDir2 content (25x), 19% getFile content (119x)] (328x), resolve: 0.421 s [100% Category (117x)] (117x), ObjectMaps: 0.134 s [44% getPrimaryObjectProperty (117x), 35% getPrimaryObjectLocation (117x), 21% getLastPromoted (117x)] (473x)
2025-08-04 22:03:42,003 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d594b73448_0_661d594b73448_0_: finished. Total: 0.101 s, CPU [user: 0.0488 s, system: 0.00817 s], Allocated memory: 8.8 MB, RepositoryConfigService: 0.0457 s [52% getReadConfiguration (180x), 48% getReadUserConfiguration (10x)] (190x), svn: 0.0453 s [52% info (21x), 39% getFile content (17x)] (41x), resolve: 0.0333 s [100% User (9x)] (9x), ObjectMaps: 0.0181 s [52% getPrimaryObjectProperty (9x), 27% getPrimaryObjectLocation (9x), 21% getLastPromoted (9x)] (37x)
2025-08-04 22:03:42,183 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d594b9604a_0_661d594b9604a_0_: finished. Total: 0.143 s, CPU [user: 0.0471 s, system: 0.00493 s], Allocated memory: 19.6 MB, svn: 0.113 s [79% getDir2 content (17x), 21% getFile content (44x)] (62x), RepositoryConfigService: 0.037 s [97% getReadConfiguration (170x)] (192x)
2025-08-04 22:03:42,943 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d594bba04b_0_661d594bba04b_0_: finished. Total: 0.759 s, CPU [user: 0.322 s, system: 0.0315 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.544 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.424 s [60% getFile content (412x), 40% getDir2 content (21x)] (434x), GC: 0.048 s [100% G1 Young Generation (5x)] (5x)
2025-08-04 22:03:43,050 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d594c77c4c_0_661d594c77c4c_0_: finished. Total: 0.107 s, CPU [user: 0.0196 s, system: 0.00237 s], Allocated memory: 18.0 MB, svn: 0.0991 s [90% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0155 s [98% getReadConfiguration (124x)] (148x)
2025-08-04 22:03:43,383 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d594c9984e_0_661d594c9984e_0_: finished. Total: 0.305 s, CPU [user: 0.123 s, system: 0.00856 s], Allocated memory: 386.2 MB, RepositoryConfigService: 0.203 s [96% getReadConfiguration (2788x)] (3026x), svn: 0.187 s [56% getFile content (186x), 44% getDir2 content (21x)] (208x)
2025-08-04 22:03:43,447 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.82 s, CPU [user: 1.03 s, system: 0.16 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.69 s [42% getDir2 content (133x), 34% getFile content (867x), 20% getDatedRevision (181x)] (1227x), RepositoryConfigService: 0.904 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.504 s [84% Category (117x)] (139x), ObjectMaps: 0.173 s [47% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 20% getLastPromoted (132x)] (541x)
2025-08-04 22:03:43,447 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 2.29 s [41% getDatedRevision (362x), 31% getDir2 content (133x), 25% getFile content (867x)] (1410x), RepositoryConfigService: 0.904 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.504 s [84% Category (117x)] (140x), ObjectMaps: 0.173 s [47% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 20% getLastPromoted (132x)] (541x)
2025-08-04 22:03:56,037 [ajp-nio-127.0.0.1-8889-exec-2 | cID:756562eb-c0a844bd-4faad6d6-fd34e1ac] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754316235288': Total: 0.536 s, CPU [user: 0.192 s, system: 0.0815 s], Allocated memory: 12.2 MB, transactions: 5, resolve: 0.129 s [89% WorkItem (1x)] (2x), svn: 0.0874 s [46% info (5x), 19% testConnection (1x), 14% getLatestRevision (1x), 13% getDir2 content (1x)] (15x), RepositoryConfigService: 0.0715 s [82% getReadConfiguration (6x)] (7x)
2025-08-04 22:04:23,838 [ajp-nio-127.0.0.1-8889-exec-10 | cID:7565d121-c0a844bd-4faad6d6-4b3da2c5] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754316263679': Total: 0.123 s, CPU [user: 0.00897 s, system: 0.0362 s], Allocated memory: 421.7 kB, transactions: 0, svn: 0.0424 s [71% testConnection (1x), 23% info (1x)] (3x), resolve: 0.0156 s [100% WorkItem (1x)] (1x)
