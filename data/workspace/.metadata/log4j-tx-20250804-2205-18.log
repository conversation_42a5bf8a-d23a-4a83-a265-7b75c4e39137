2025-08-04 22:05:27,239 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0476 s [71% update (144x), 29% query (12x)] (221x), svn: 0.0114 s [43% testConnection (1x), 42% getLatestRevision (2x)] (4x)
2025-08-04 22:05:27,343 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0366 s [57% getDir2 content (2x), 36% info (3x)] (6x)
2025-08-04 22:05:28,154 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.806 s, CPU [user: 0.238 s, system: 0.329 s], Allocated memory: 68.8 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:05:28,154 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.807 s, CPU [user: 0.122 s, system: 0.197 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.101 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:05:28,155 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.807 s, CPU [user: 0.052 s, system: 0.0759 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0731 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0689 s [57% log2 (5x), 31% testConnection (1x)] (7x)
2025-08-04 22:05:28,155 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.806 s, CPU [user: 0.0871 s, system: 0.133 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.105 s [61% log2 (10x), 27% getLatestRevision (2x)] (13x), ObjectMaps: 0.0996 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 22:05:28,155 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.807 s, CPU [user: 0.214 s, system: 0.276 s], Allocated memory: 53.2 MB, transactions: 0, ObjectMaps: 0.128 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:05:28,155 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.807 s, CPU [user: 0.103 s, system: 0.114 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.219 s [65% log2 (10x), 11% info (5x), 10% getLatestRevision (3x)] (24x), ObjectMaps: 0.0533 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 22:05:28,155 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.574 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.459 s [63% log2 (36x), 15% getLatestRevision (9x), 12% testConnection (6x)] (61x)
2025-08-04 22:05:28,372 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.187 s [100% getReadConfiguration (48x)] (48x), svn: 0.0721 s [88% info (18x)] (38x)
2025-08-04 22:05:28,668 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.232 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.173 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 22:05:28,995 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.172 s, CPU [user: 0.0266 s, system: 0.00579 s], Allocated memory: 2.4 MB
2025-08-04 22:05:28,995 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.173 s, CPU [user: 0.0266 s, system: 0.00717 s], Allocated memory: 3.2 MB
2025-08-04 22:05:28,996 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.174 s, CPU [user: 0.019 s, system: 0.0048 s], Allocated memory: 2.2 MB
2025-08-04 22:05:28,999 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.178 s, CPU [user: 0.00498 s, system: 0.00248 s], Allocated memory: 884.9 kB
2025-08-04 22:05:29,046 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.222 s, CPU [user: 0.0243 s, system: 0.00675 s], Allocated memory: 8.5 MB, GC: 0.015 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 22:05:29,082 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.401 s [100% doFinishStartup (1x)] (1x), Lucene: 0.139 s [100% refresh (1x)] (1x), commit: 0.0672 s [100% Revision (1x)] (1x)
2025-08-04 22:05:31,531 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.299 s [89% info (158x)] (168x)
2025-08-04 22:05:32,301 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.69 s, CPU [user: 0.00603 s, system: 0.00153 s], Allocated memory: 692.0 kB, transactions: 1
2025-08-04 22:05:32,302 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, notification worker: 0.0308 s [55% RevisionActivityCreator (2x), 12% WorkItemActivityCreator (1x), 11% BuildActivityCreator (1x), 11% PlanActivityCreator (1x)] (6x), Incremental Baseline: 0.0288 s [100% WorkItem (23x)] (23x), persistence listener: 0.028 s [80% indexRefreshPersistenceListener (1x), 13% RevisionActivityCreator (2x)] (7x), resolve: 0.0132 s [81% User (1x)] (3x), Lucene: 0.00669 s [100% refresh (1x)] (1x), PullingJob: 0.00554 s [100% collectChanges (1x)] (1x), svn: 0.00543 s [53% getLatestRevision (1x), 47% testConnection (1x)] (2x), Full Baseline: 0.00506 s [100% WorkItem (2x)] (2x), ObjectMaps: 0.00358 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-08-04 22:05:32,302 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.773 s, CPU [user: 0.171 s, system: 0.0263 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.585 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0723 s [82% buildBaselineSnapshots (1x)] (27x)
2025-08-04 22:05:32,666 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d59b684440_0_661d59b684440_0_: finished. Total: 1.13 s, CPU [user: 0.378 s, system: 0.0936 s], Allocated memory: 58.3 MB, svn: 0.625 s [54% getDatedRevision (181x), 27% getDir2 content (25x)] (328x), resolve: 0.419 s [100% Category (117x)] (117x), ObjectMaps: 0.145 s [43% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 22:05:32,875 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d59b7b5448_0_661d59b7b5448_0_: finished. Total: 0.118 s, CPU [user: 0.0506 s, system: 0.00938 s], Allocated memory: 8.8 MB, RepositoryConfigService: 0.0524 s [51% getReadConfiguration (180x), 49% getReadUserConfiguration (10x)] (190x), svn: 0.0439 s [50% info (21x), 39% getFile content (17x)] (41x), resolve: 0.0415 s [100% User (9x)] (9x), ObjectMaps: 0.026 s [54% getPrimaryObjectProperty (9x), 24% getPrimaryObjectLocation (9x), 22% getLastPromoted (9x)] (37x)
2025-08-04 22:05:33,053 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d59b7db04a_0_661d59b7db04a_0_: finished. Total: 0.144 s, CPU [user: 0.0535 s, system: 0.00507 s], Allocated memory: 19.7 MB, svn: 0.104 s [62% getDir2 content (17x), 38% getFile content (44x)] (62x), RepositoryConfigService: 0.065 s [99% getReadConfiguration (170x)] (192x)
2025-08-04 22:05:33,678 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d59b7ff44b_0_661d59b7ff44b_0_: finished. Total: 0.625 s, CPU [user: 0.3 s, system: 0.012 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.465 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.317 s [62% getFile content (412x), 38% getDir2 content (21x)] (434x), GC: 0.036 s [100% G1 Young Generation (4x)] (4x)
2025-08-04 22:05:33,995 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d59b8b384e_0_661d59b8b384e_0_: finished. Total: 0.22 s, CPU [user: 0.0893 s, system: 0.00483 s], Allocated memory: 385.0 MB, svn: 0.141 s [50% getFile content (186x), 50% getDir2 content (21x)] (208x), RepositoryConfigService: 0.138 s [97% getReadConfiguration (2788x)] (3026x)
2025-08-04 22:05:34,052 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.52 s, CPU [user: 0.966 s, system: 0.139 s], Allocated memory: 1.6 GB, transactions: 11, svn: 1.41 s [38% getDir2 content (133x), 34% getFile content (867x), 24% getDatedRevision (181x)] (1227x), RepositoryConfigService: 0.788 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.521 s [80% Category (117x)] (139x), ObjectMaps: 0.198 s [47% getPrimaryObjectProperty (132x), 31% getPrimaryObjectLocation (138x), 22% getLastPromoted (132x)] (541x)
2025-08-04 22:05:34,052 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 2 s [46% getDatedRevision (362x), 27% getDir2 content (133x), 24% getFile content (867x)] (1410x), RepositoryConfigService: 0.788 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.521 s [80% Category (117x)] (140x), ObjectMaps: 0.198 s [47% getPrimaryObjectProperty (132x), 31% getPrimaryObjectLocation (138x), 22% getLastPromoted (132x)] (541x), Lucene: 0.13 s [46% buildBaselineSnapshots (2x), 25% search (5x), 17% add (1x)] (60x)
2025-08-04 22:06:38,832 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.094140625
2025-08-04 22:07:55,294 [ajp-nio-127.0.0.1-8889-exec-2 | cID:75690a29-c0a844bd-503e7f43-984c2c1e] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754316474856': Total: 0.371 s, CPU [user: 0.13 s, system: 0.07 s], Allocated memory: 12.3 MB, transactions: 5, resolve: 0.0678 s [94% WorkItem (1x)] (2x), svn: 0.0677 s [38% info (5x), 31% testConnection (1x), 13% getLatestRevision (1x)] (15x), RepositoryConfigService: 0.0646 s [84% getReadConfiguration (6x)] (7x)
2025-08-04 22:09:23,450 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.12 s, CPU [user: 0.00246 s, system: 0.0138 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.0999 s [100% collectChanges (1x)] (1x), svn: 0.0933 s [100% getLatestRevision (1x)] (1x)
2025-08-04 22:09:26,437 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.114 s, CPU [user: 0.00221 s, system: 0.00236 s], Allocated memory: 129.8 kB, transactions: 0, PullingJob: 0.113 s [100% collectChanges (1x)] (1x), svn: 0.113 s [100% getLatestRevision (1x)] (1x)
2025-08-04 22:14:38,871 [ajp-nio-127.0.0.1-8889-exec-7 | cID:756f3205-c0a844bd-503e7f43-81366f0d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs/test/execute': Total: 0.528 s, CPU [user: 0.254 s, system: 0.0946 s], Allocated memory: 56.0 MB, transactions: 1, PolarionAuthenticator: 0.314 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0312 s [100% getReadConfiguration (1x)] (1x), svn: 0.0284 s [53% testConnection (1x), 33% getFile content (3x)] (6x)
2025-08-04 22:14:39,164 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer] INFO  TXLOGGER - Tx 661d5bcd35c56_0_661d5bcd35c56_0_: finished. Total: 0.101 s, CPU [user: 0.00783 s, system: 0.0023 s], Allocated memory: 675.0 kB, GC: 0.062 s [100% G1 Young Generation (1x)] (1x), svn: 0.023 s [53% getFile content (2x), 46% testConnection (1x)] (4x)
2025-08-04 22:14:40,365 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d5bcd4f857_0_661d5bcd4f857_0_: finished. Total: 1.2 s, CPU [user: 0.498 s, system: 0.0961 s], Allocated memory: 240.8 MB, ObjectEnum Document: 1.09 s [100% available (6x)] (6x), resolve: 0.847 s [99% Module (97x)] (802x), svn: 0.472 s [46% info (92x), 28% getDir2 content (46x), 25% getFile content (46x)] (185x), ModulePageParser: 0.195 s [100% parsePage (46x)] (46x), DB: 0.142 s [57% query (194x), 22% update (194x), 20% commit (194x)] (5917x)
2025-08-04 22:14:44,328 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d5bd23f859_0_661d5bd23f859_0_: finished. Total: 0.105 s, CPU [user: 0.0346 s, system: 0.00732 s], Allocated memory: 4.9 MB, resolve: 0.0592 s [71% WorkItem (1x), 29% Attachment (1x)] (2x), svn: 0.0514 s [46% info (7x), 25% getLatestRevision (1x), 10% log (1x)] (13x)
2025-08-04 22:14:45,806 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 6.94 s, CPU [user: 0.86 s, system: 0.178 s], Allocated memory: 293.9 MB, transactions: 4, SynchronizationTask: 1.57 s [53% Update FEISHU-test items (1x), 21% Mapping created and updated items (1x), 11% Retrieving FEISHU-test items (1x)] (10x), ObjectEnum Document: 1.09 s [100% available (6x)] (6x), resolve: 0.906 s [92% Module (97x)] (807x), svn: 0.547 s [44% info (99x), 25% getDir2 content (47x), 25% getFile content (49x)] (202x)
2025-08-04 22:15:34,799 [ajp-nio-127.0.0.1-8889-exec-9 | cID:75700dfc-c0a844bd-503e7f43-720a094f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.145 s, CPU [user: 0.0491 s, system: 0.0285 s], Allocated memory: 4.4 MB, transactions: 0
2025-08-04 22:15:35,280 [ajp-nio-127.0.0.1-8889-exec-5 | cID:75700ff4-c0a844bd-503e7f43-fde37dc2] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.124 s, CPU [user: 0.0122 s, system: 0.00566 s], Allocated memory: 1.1 MB, transactions: 0, UICustomizationDataProvider: 0.0965 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0897 s [100% readUserData (1x)] (1x)
2025-08-04 22:15:37,761 [Worker-0: Synchronization of test (manually started) | u:admin | job: synchronizer | u:admin] INFO  TXLOGGER - Tx 661d5c0630c5a_0_661d5c0630c5a_0_: finished. Total: 0.349 s, CPU [user: 0.0746 s, system: 0.0503 s], Allocated memory: 104.0 MB, ObjectEnum Document: 0.323 s [100% available (6x)] (6x), DB: 0.178 s [54% query (194x), 25% update (194x), 22% commit (194x)] (5917x), resolve: 0.0866 s [94% Module (97x)] (802x), GlobalHandler: 0.0783 s [100% get (1242x)] (1244x), EHCache: 0.0761 s [100% GET (802x)] (802x), GC: 0.042 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 22:16:21,881 [Worker-0: Synchronization of test (manually started) | u:admin] INFO  TXLOGGER - Summary for 'job: synchronizer': Total: 44.5 s, CPU [user: 0.198 s, system: 0.111 s], Allocated memory: 117.7 MB, transactions: 3, SynchronizationTask: 40.9 s [98% Update FEISHU-test items (1x)] (10x)
2025-08-04 22:17:11,459 [ajp-nio-127.0.0.1-8889-exec-1 | cID:7571875f-c0a844bd-503e7f43-0359fccb] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews?projectId=WBS': Total: 0.194 s, CPU [user: 0.0356 s, system: 0.0416 s], Allocated memory: 1.8 MB, transactions: 1, svn: 0.0134 s [49% info (1x), 40% testConnection (1x)] (3x)
