2025-08-04 22:18:56,520 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0518 s [67% update (144x), 32% query (12x)] (221x), svn: 0.0124 s [59% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-08-04 22:18:56,639 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0317 s [58% getDir2 content (2x), 33% info (3x)] (6x)
2025-08-04 22:18:57,378 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.22 s, system: 0.353 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:18:57,378 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.0529 s, system: 0.105 s], Allocated memory: 7.4 MB, transactions: 0, ObjectMaps: 0.0692 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-04 22:18:57,378 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.19 s, system: 0.296 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.108 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:18:57,378 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.105 s, system: 0.235 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0629 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-04 22:18:57,378 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.0748 s, system: 0.137 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0742 s [82% log2 (10x)] (13x), ObjectMaps: 0.0589 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 22:18:57,379 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.098 s, system: 0.166 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.13 s [33% log2 (10x), 27% getLatestRevision (3x), 16% info (5x), 16% log (1x)] (24x), ObjectMaps: 0.0889 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-04 22:18:57,379 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.509 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.297 s [54% log2 (36x), 20% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-08-04 22:18:57,617 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.197 s [100% getReadConfiguration (48x)] (48x), svn: 0.0627 s [85% info (18x)] (38x)
2025-08-04 22:18:57,884 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.208 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.158 s [100% getReadConfiguration (54x)] (54x)
2025-08-04 22:18:58,101 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.11 s, CPU [user: 0.0295 s, system: 0.0054 s], Allocated memory: 3.2 MB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 22:18:58,101 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.11 s, CPU [user: 0.0194 s, system: 0.0053 s], Allocated memory: 2.8 MB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 22:18:58,103 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.112 s, CPU [user: 0.0178 s, system: 0.00396 s], Allocated memory: 1.9 MB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 22:18:58,106 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.115 s, CPU [user: 0.0051 s, system: 0.00245 s], Allocated memory: 899.2 kB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 22:18:58,144 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.152 s, CPU [user: 0.0236 s, system: 0.00679 s], Allocated memory: 8.6 MB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-08-04 22:18:58,234 [main | u:p] INFO  TXLOGGER - Tx Lucene Commit [head]: finished. Total: 0.118 s, CPU [user: 0.025 s, system: 0.0048 s], Allocated memory: 10.2 MB, commit: 0.117 s [100% Revision (1x)] (1x)
2025-08-04 22:18:58,240 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.341 s [100% doFinishStartup (1x)] (1x), commit: 0.117 s [100% Revision (1x)] (1x), Lucene: 0.0799 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0171 s [100% objectsToInv (1x)] (1x)
2025-08-04 22:19:00,672 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.305 s [90% info (158x)] (168x)
2025-08-04 22:19:01,441 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.688 s, CPU [user: 0.00639 s, system: 0.00142 s], Allocated memory: 584.2 kB, transactions: 1
2025-08-04 22:19:01,441 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, Incremental Baseline: 0.03 s [100% WorkItem (23x)] (23x), resolve: 0.0194 s [79% User (1x), 21% Revision (2x)] (3x), Full Baseline: 0.0193 s [100% WorkItem (2x)] (2x), persistence listener: 0.0165 s [70% indexRefreshPersistenceListener (1x), 24% WorkItemActivityCreator (1x)] (7x), notification worker: 0.0134 s [73% RevisionActivityCreator (2x), 11% TestRunActivityCreator (1x)] (6x), ObjectMaps: 0.00824 s [100% getPrimaryObjectLocation (1x)] (1x), Lucene: 0.00593 s [100% refresh (1x)] (1x), PullingJob: 0.00551 s [100% collectChanges (1x)] (1x), svn: 0.00532 s [57% testConnection (1x), 43% getLatestRevision (1x)] (2x), EHCache: 0.00152 s [99% GET (15x)] (40x)
2025-08-04 22:19:01,442 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.771 s, CPU [user: 0.159 s, system: 0.022 s], Allocated memory: 19.8 MB, transactions: 27, svn: 0.587 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0674 s [80% buildBaselineSnapshots (1x), 20% buildBaseline (26x)] (27x)
2025-08-04 22:19:01,808 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d5cccb1040_0_661d5cccb1040_0_: finished. Total: 1.13 s, CPU [user: 0.359 s, system: 0.0891 s], Allocated memory: 58.6 MB, svn: 0.671 s [50% getDatedRevision (181x), 32% getDir2 content (25x)] (328x), resolve: 0.373 s [100% Category (117x)] (117x), ObjectMaps: 0.124 s [42% getPrimaryObjectProperty (117x), 35% getPrimaryObjectLocation (117x), 23% getLastPromoted (117x)] (473x)
2025-08-04 22:19:02,153 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d5cce0004a_0_661d5cce0004a_0_: finished. Total: 0.137 s, CPU [user: 0.0524 s, system: 0.00447 s], Allocated memory: 19.6 MB, svn: 0.0956 s [63% getDir2 content (17x), 37% getFile content (44x)] (62x), RepositoryConfigService: 0.0572 s [98% getReadConfiguration (170x)] (192x)
2025-08-04 22:19:02,806 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d5cce2244b_0_661d5cce2244b_0_: finished. Total: 0.653 s, CPU [user: 0.312 s, system: 0.0138 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.465 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.351 s [60% getFile content (412x), 40% getDir2 content (21x)] (434x)
2025-08-04 22:19:03,167 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661d5ccedf44e_0_661d5ccedf44e_0_: finished. Total: 0.258 s, CPU [user: 0.104 s, system: 0.0059 s], Allocated memory: 389.1 MB, RepositoryConfigService: 0.166 s [97% getReadConfiguration (2788x)] (3026x), svn: 0.158 s [51% getFile content (186x), 48% getDir2 content (21x)] (208x)
2025-08-04 22:19:03,238 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.56 s, CPU [user: 0.97 s, system: 0.134 s], Allocated memory: 1.7 GB, transactions: 11, svn: 1.51 s [42% getDir2 content (133x), 32% getFile content (867x), 22% getDatedRevision (181x)] (1227x), RepositoryConfigService: 0.799 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.452 s [82% Category (117x)] (139x), ObjectMaps: 0.162 s [46% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 21% getLastPromoted (132x)] (541x)
2025-08-04 22:19:03,238 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 64, svn: 2.1 s [44% getDatedRevision (362x), 30% getDir2 content (133x), 23% getFile content (867x)] (1410x), RepositoryConfigService: 0.799 s [95% getReadConfiguration (12166x)] (12860x), resolve: 0.452 s [82% Category (117x)] (140x), ObjectMaps: 0.162 s [46% getPrimaryObjectProperty (132x), 33% getPrimaryObjectLocation (138x), 21% getLastPromoted (132x)] (541x), Lucene: 0.115 s [47% buildBaselineSnapshots (2x), 30% search (5x), 15% buildBaseline (52x)] (60x)
2025-08-04 22:19:41,279 [ajp-nio-127.0.0.1-8889-exec-2 | cID:7573cfba-c0a844bd-45066bc9-f3c905ca] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754317180819': Total: 0.42 s, CPU [user: 0.138 s, system: 0.0748 s], Allocated memory: 11.0 MB, transactions: 5, resolve: 0.0803 s [83% WorkItem (1x)] (2x), svn: 0.0586 s [46% info (5x), 16% testConnection (1x), 15% getLatestRevision (1x), 12% getFile content (3x)] (15x), RepositoryConfigService: 0.0457 s [79% getReadConfiguration (6x), 21% getExistingPrefixes (1x)] (7x)
