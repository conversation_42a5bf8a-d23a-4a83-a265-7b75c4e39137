2025-08-05 16:59:06,770 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.071 s [50% query (12x), 50% update (144x)] (221x), svn: 0.0218 s [62% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-08-05 16:59:06,941 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0626 s [55% getDir2 content (2x), 40% info (3x)] (6x)
2025-08-05 16:59:07,766 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.822 s, CPU [user: 0.125 s, system: 0.222 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0756 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-05 16:59:07,766 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.822 s, CPU [user: 0.0914 s, system: 0.142 s], Allocated memory: 12.7 MB, transactions: 0, ObjectMaps: 0.0958 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0834 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-08-05 16:59:07,766 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.821 s, CPU [user: 0.0866 s, system: 0.113 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0857 s [80% log2 (10x)] (13x), ObjectMaps: 0.0593 s [100% getAllPrimaryObjects (2x)] (14x)
2025-08-05 16:59:07,766 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.822 s, CPU [user: 0.223 s, system: 0.29 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.129 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-05 16:59:07,766 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.822 s, CPU [user: 0.0799 s, system: 0.102 s], Allocated memory: 9.5 MB, transactions: 0, svn: 0.138 s [37% log (1x), 23% info (5x), 19% log2 (5x), 11% getLatestRevision (2x)] (18x), ObjectMaps: 0.0642 s [100% getAllPrimaryObjects (1x)] (10x)
2025-08-05 16:59:07,766 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.821 s, CPU [user: 0.248 s, system: 0.351 s], Allocated memory: 68.5 MB, transactions: 0, ObjectMaps: 0.128 s [100% getAllPrimaryObjects (1x)] (7x)
2025-08-05 16:59:07,766 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.552 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.381 s [54% log2 (36x), 13% log (1x), 13% getLatestRevision (9x)] (61x)
2025-08-05 16:59:07,907 [main | u:p] INFO  TXLOGGER - Tx 661e5d2ca5801_0_661e5d2ca5801_0_: finished. Total: 0.107 s, CPU [user: 0.0784 s, system: 0.00719 s], Allocated memory: 21.8 MB
2025-08-05 16:59:08,071 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.26 s [100% getReadConfiguration (48x)] (48x), svn: 0.1 s [90% info (18x)] (38x)
2025-08-05 16:59:08,615 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.383 s [76% info (94x), 13% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.312 s [100% getReadConfiguration (94x)] (94x)
2025-08-05 16:59:08,893 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.144 s, CPU [user: 0.0243 s, system: 0.00596 s], Allocated memory: 8.6 MB
2025-08-05 16:59:09,132 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.48 s [100% doFinishStartup (1x)] (1x), DB: 0.129 s [52% update (45x), 21% query (20x), 14% execute (15x)] (122x), SubterraURITable: 0.086 s [100% addIfNotExistsDB (20x)] (20x), commit: 0.0826 s [73% Revision (1x), 27% BuildArtifact (1x)] (2x), Lucene: 0.0584 s [100% refresh (2x)] (2x), resolve: 0.0301 s [77% BuildArtifact (11x), 23% Revision (2x)] (13x)
2025-08-05 16:59:13,023 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.447 s [76% info (158x), 19% getDir2 content (7x)] (170x)
2025-08-05 16:59:13,028 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, persistence listener: 0.0136 s [80% indexRefreshPersistenceListener (1x), 11% WorkItemActivityCreator (1x)] (7x), notification worker: 0.000811 s [100% BuildActivityCreator (1x)] (1x)
2025-08-05 16:59:14,219 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.09 s, CPU [user: 0.00491 s, system: 0.00166 s], Allocated memory: 494.6 kB, transactions: 1
2025-08-05 16:59:14,219 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.09 s, CPU [user: 0.00465 s, system: 0.00167 s], Allocated memory: 556.5 kB, transactions: 1
2025-08-05 16:59:14,219 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 73, Incremental Baseline: 0.0509 s [100% WorkItem (24x)] (24x), Full Baseline: 0.0342 s [100% WorkItem (2x)] (2x), resolve: 0.0301 s [88% User (1x)] (3x), notification worker: 0.0301 s [46% RevisionActivityCreator (18x), 24% WorkItemActivityCreator (9x), 16% TestRunActivityCreator (9x)] (53x), Lucene: 0.0185 s [66% refresh (2x), 34% add (1x)] (3x), ObjectMaps: 0.00943 s [100% getPrimaryObjectLocation (1x)] (1x), DB: 0.00835 s [62% update (2x), 38% commit (2x)] (4x)
2025-08-05 16:59:14,220 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.2 s, CPU [user: 0.201 s, system: 0.0302 s], Allocated memory: 19.8 MB, transactions: 28, svn: 0.943 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0943 s [71% buildBaselineSnapshots (1x), 29% buildBaseline (27x)] (28x)
2025-08-05 16:59:14,793 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e5d31c1445_0_661e5d31c1445_0_: finished. Total: 1.76 s, CPU [user: 0.505 s, system: 0.127 s], Allocated memory: 58.8 MB, svn: 1.07 s [50% getDatedRevision (181x), 25% getDir2 content (25x), 25% getFile content (119x)] (328x), resolve: 0.749 s [100% Category (117x)] (117x), ObjectMaps: 0.239 s [41% getPrimaryObjectProperty (117x), 33% getPrimaryObjectLocation (117x), 26% getLastPromoted (117x)] (473x)
2025-08-05 16:59:15,101 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e5d339e070_0_661e5d339e070_0_: finished. Total: 0.165 s, CPU [user: 0.0677 s, system: 0.0131 s], Allocated memory: 8.8 MB, svn: 0.0827 s [51% info (21x), 40% getFile content (17x)] (41x), RepositoryConfigService: 0.0775 s [56% getReadConfiguration (180x), 44% getReadUserConfiguration (10x)] (190x), resolve: 0.0549 s [100% User (9x)] (9x), ObjectMaps: 0.0266 s [52% getPrimaryObjectProperty (9x), 25% getPrimaryObjectLocation (9x), 23% getLastPromoted (9x)] (37x)
2025-08-05 16:59:15,377 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e5d33d4472_0_661e5d33d4472_0_: finished. Total: 0.224 s, CPU [user: 0.0691 s, system: 0.00984 s], Allocated memory: 19.8 MB, svn: 0.171 s [73% getDir2 content (17x), 27% getFile content (44x)] (62x), RepositoryConfigService: 0.0797 s [97% getReadConfiguration (170x)] (192x)
2025-08-05 16:59:16,423 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e5d340c473_0_661e5d340c473_0_: finished. Total: 1.05 s, CPU [user: 0.415 s, system: 0.0442 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.696 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.608 s [52% getFile content (412x), 48% getDir2 content (21x)] (434x)
2025-08-05 16:59:16,568 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e5d3511c74_0_661e5d3511c74_0_: finished. Total: 0.145 s, CPU [user: 0.0346 s, system: 0.00422 s], Allocated memory: 17.9 MB, svn: 0.129 s [83% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0336 s [98% getReadConfiguration (124x)] (148x)
2025-08-05 16:59:17,056 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e5d3543876_0_661e5d3543876_0_: finished. Total: 0.433 s, CPU [user: 0.162 s, system: 0.0144 s], Allocated memory: 385.0 MB, RepositoryConfigService: 0.275 s [96% getReadConfiguration (2788x)] (3026x), svn: 0.263 s [54% getFile content (186x), 46% getDir2 content (21x)] (208x)
2025-08-05 16:59:17,192 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661e5d35b0077_0_661e5d35b0077_0_: finished. Total: 0.135 s, CPU [user: 0.0328 s, system: 0.00452 s], Allocated memory: 13.1 MB, svn: 0.119 s [79% getDir2 content (18x), 21% getFile content (33x)] (52x), RepositoryConfigService: 0.0372 s [99% getReadConfiguration (128x)] (150x)
2025-08-05 16:59:17,192 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.17 s, CPU [user: 1.39 s, system: 0.238 s], Allocated memory: 1.6 GB, transactions: 11, svn: 2.58 s [41% getDir2 content (133x), 34% getFile content (867x), 21% getDatedRevision (181x)] (1227x), RepositoryConfigService: 1.26 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.886 s [85% Category (117x)] (139x), ObjectMaps: 0.295 s [44% getPrimaryObjectProperty (132x), 31% getPrimaryObjectLocation (138x), 24% getLastPromoted (132x)] (541x)
2025-08-05 16:59:17,192 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 66, svn: 3.53 s [42% getDatedRevision (362x), 30% getDir2 content (133x), 25% getFile content (867x)] (1411x), RepositoryConfigService: 1.26 s [94% getReadConfiguration (12166x)] (12860x), resolve: 0.886 s [85% Category (117x)] (140x), ObjectMaps: 0.295 s [44% getPrimaryObjectProperty (132x), 31% getPrimaryObjectLocation (138x), 24% getLastPromoted (132x)] (541x)
2025-08-05 16:59:40,135 [ajp-nio-127.0.0.1-8889-exec-2 | cID:79752f06-c0a844bd-19cc229b-8e2e529a] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/reviews/WBS-1?projectId=WBS&_t=1754384379600': Total: 0.478 s, CPU [user: 0.162 s, system: 0.103 s], Allocated memory: 10.6 MB, transactions: 5, resolve: 0.102 s [92% WorkItem (1x)] (2x), svn: 0.0774 s [45% info (5x), 16% getFile content (3x), 15% testConnection (1x), 12% getDir2 content (1x)] (15x), RepositoryConfigService: 0.0703 s [81% getReadConfiguration (6x)] (7x)
2025-08-05 17:00:58,072 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0171875
2025-08-05 17:01:29,829 [ajp-nio-127.0.0.1-8889-exec-2 | cID:7976dcf7-c0a844bd-19cc229b-744a7785] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754384489479': Total: 0.107 s, CPU [user: 0.0114 s, system: 0.0277 s], Allocated memory: 806.1 kB, transactions: 0, svn: 0.0776 s [55% info (2x), 37% getDir2 content (1x)] (4x)
2025-08-05 17:03:43,024 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.593 s, CPU [user: 0.00267 s, system: 0.0121 s], Allocated memory: 130.5 kB, transactions: 0, PullingJob: 0.519 s [100% collectChanges (1x)] (1x), svn: 0.513 s [100% getLatestRevision (1x)] (1x)
2025-08-05 17:10:05,612 [ajp-nio-127.0.0.1-8889-exec-3 | cID:797ebbbe-c0a844bd-19cc229b-66c5a43d] INFO  TXLOGGER - Summary for 'servlet /polarion/checklist/api/templates?projectId=WBS&_t=1754385005316': Total: 0.107 s, CPU [user: 0.0185 s, system: 0.0251 s], Allocated memory: 726.1 kB, transactions: 0, svn: 0.0624 s [53% info (2x), 42% getDir2 content (1x)] (4x)
