package com.polarion.synchronizer.proxy.feishu;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * 飞书附件对象
 * 参考 JIRA 的 JiraAttachment 实现
 */
public class FeishuAttachment {
    
    @NotNull
    private final String fileId;
    
    @NotNull
    private final String fileName;
    
    @Nullable
    private final String downloadUrl;
    
    @Nullable
    private final String fileSize;
    
    @Nullable
    private final String mimeType;
    
    @Nullable
    private final String uploadTime;
    
    @Nullable
    private final String uploaderId;

    public FeishuAttachment(@NotNull String fileId, @NotNull String fileName) {
        this(fileId, fileName, null, null, null, null, null);
    }

    public FeishuAttachment(@NotNull String fileId, 
                           @NotNull String fileName,
                           @Nullable String downloadUrl) {
        this(fileId, fileName, downloadUrl, null, null, null, null);
    }

    public FeishuAttachment(@NotNull String fileId,
                           @NotNull String fileName,
                           @Nullable String downloadUrl,
                           @Nullable String fileSize,
                           @Nullable String mimeType,
                           @Nullable String uploadTime,
                           @Nullable String uploaderId) {
        this.fileId = fileId;
        this.fileName = fileName;
        this.downloadUrl = downloadUrl;
        this.fileSize = fileSize;
        this.mimeType = mimeType;
        this.uploadTime = uploadTime;
        this.uploaderId = uploaderId;
    }

    @NotNull
    public String getFileId() {
        return fileId;
    }

    @NotNull
    public String getFileName() {
        return fileName;
    }

    @Nullable
    public String getDownloadUrl() {
        return downloadUrl;
    }

    @Nullable
    public String getFileSize() {
        return fileSize;
    }

    @Nullable
    public String getMimeType() {
        return mimeType;
    }

    @Nullable
    public String getUploadTime() {
        return uploadTime;
    }

    @Nullable
    public String getUploaderId() {
        return uploaderId;
    }

    @Override
    public String toString() {
        return "FeishuAttachment{" +
                "fileId='" + fileId + '\'' +
                ", fileName='" + fileName + '\'' +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", fileSize='" + fileSize + '\'' +
                ", mimeType='" + mimeType + '\'' +
                ", uploadTime='" + uploadTime + '\'' +
                ", uploaderId='" + uploaderId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        FeishuAttachment that = (FeishuAttachment) obj;

        if (!fileId.equals(that.fileId)) return false;
        return fileName.equals(that.fileName);
    }

    @Override
    public int hashCode() {
        int result = fileId.hashCode();
        result = 31 * result + fileName.hashCode();
        return result;
    }

    /**
     * 从飞书 API 响应创建附件对象
     */
    @NotNull
    public static FeishuAttachment fromApiResponse(@NotNull Object apiResponse) {
        // 这里需要根据实际的飞书 API 响应格式来解析
        // 目前使用简单的 Map 解析
        if (apiResponse instanceof java.util.Map) {
            @SuppressWarnings("unchecked")
            java.util.Map<String, Object> responseMap = (java.util.Map<String, Object>) apiResponse;
            
            String fileId = getStringValue(responseMap, "file_id");
            String fileName = getStringValue(responseMap, "file_name");
            String downloadUrl = getStringValue(responseMap, "download_url");
            String fileSize = getStringValue(responseMap, "file_size");
            String mimeType = getStringValue(responseMap, "mime_type");
            String uploadTime = getStringValue(responseMap, "upload_time");
            String uploaderId = getStringValue(responseMap, "uploader_id");
            
            if (fileId != null && fileName != null) {
                return new FeishuAttachment(fileId, fileName, downloadUrl, fileSize, mimeType, uploadTime, uploaderId);
            }
        }
        
        throw new IllegalArgumentException("无法从 API 响应创建飞书附件对象: " + apiResponse);
    }

    /**
     * 从 Map 中安全获取字符串值
     */
    @Nullable
    private static String getStringValue(@NotNull java.util.Map<String, Object> map, @NotNull String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 检查是否是图片文件
     */
    public boolean isImage() {
        if (mimeType != null) {
            return mimeType.startsWith("image/");
        }
        
        // 根据文件扩展名判断
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".jpg") || 
               lowerFileName.endsWith(".jpeg") || 
               lowerFileName.endsWith(".png") || 
               lowerFileName.endsWith(".gif") || 
               lowerFileName.endsWith(".bmp") || 
               lowerFileName.endsWith(".webp");
    }

    /**
     * 获取文件扩展名
     */
    @NotNull
    public String getFileExtension() {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 获取显示名称（用于调试）
     */
    @NotNull
    public String getDisplayName() {
        return fileName + " (" + fileId + ")";
    }

    /**
     * 检查附件是否有效
     */
    public boolean isValid() {
        return fileId != null && !fileId.trim().isEmpty() && 
               fileName != null && !fileName.trim().isEmpty();
    }
}
