package com.polarion.synchronizer.proxy.feishu.translators;

import java.util.Collection;

import javax.inject.Inject;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.google.inject.assistedinject.Assisted;
import com.polarion.synchronizer.ILogger;
import com.polarion.synchronizer.ISynchronizationContext;
import com.polarion.synchronizer.mapping.TranslationResult;
import com.polarion.synchronizer.mapping.ValueMapping;
import com.polarion.synchronizer.model.Side;
import com.polarion.synchronizer.spi.translators.TypesafeTranslator;

/**
 * Polarion富文本字段到飞书富文本字段的转换器
 * 处理HTML格式的双向映射和转换
 * 
 * 转换策略：
 * - Polarion HTML → 飞书 MultiTextDetail (优先使用docHTML字段)
 * - 飞书 MultiTextDetail → Polarion HTML (优先从docHTML字段获取)
 */
public class PolarionToFeishuRichTextTranslator extends TypesafeTranslator<String, String, String> {
    
    @NotNull
    private final ILogger logger;
    
    @NotNull
    private final Collection<ValueMapping> valueMappings;
    
    @NotNull
    private final Side fromSide;

    @Inject
    public PolarionToFeishuRichTextTranslator(@Assisted Collection<ValueMapping> valueMappings, 
                                             @Assisted Side fromSide,
                                             @NotNull ISynchronizationContext context) {
        super(String.class, String.class);
        this.valueMappings = valueMappings;
        this.fromSide = fromSide;
        this.logger = context.getLogger();
    }

    @Override
    public TranslationResult<String> translateUnidirectionalTypesafe(@Nullable String sourceValue,
                                                                    @Nullable String targetValue) {
        String mappedValue = convertRichText(sourceValue);
        return createUnidirectionalResult(mappedValue, targetValue);
    }

    @Override
    public TranslationResult<String> translateBidirectionalTypesafe(@Nullable String sourceBaseline,
                                                                   @Nullable String sourceValue,
                                                                   @Nullable String targetBaseline,
                                                                   @Nullable String targetValue) {
        String mappedValue = convertRichText(sourceValue);
        return createBidirectionalResult(sourceBaseline, sourceValue, mappedValue, targetBaseline, targetValue);
    }

    /**
     * 转换富文本字段值
     */
    @Nullable
    private String convertRichText(@Nullable String richTextValue) {
        if (richTextValue == null || richTextValue.trim().isEmpty()) {
            return null;
        }

        try {
            // 首先检查是否有直接的值映射配置
            String mappedValue = applyValueMappings(richTextValue);
            if (mappedValue != null && !mappedValue.equals(richTextValue)) {
                logger.debug("通过值映射转换富文本: " + richTextValue.substring(0, Math.min(50, richTextValue.length())) + "... -> " + mappedValue.substring(0, Math.min(50, mappedValue.length())) + "...");
                richTextValue = mappedValue;
            }

            // 根据同步方向进行转换
            if (fromSide == Side.LEFT) {
                // Polarion -> 飞书：将HTML转换为MultiTextDetail的JSON字符串表示
                return convertPolarionToFeishuRichText(richTextValue);
            } else {
                // 飞书 -> Polarion (这种情况下输入应该是MultiTextDetail的JSON，但为了类型安全处理)
                logger.warn("富文本转换器收到意外的转换方向，输入类型为String但fromSide为RIGHT");
                return convertPolarionToFeishuRichText(richTextValue);
            }

        } catch (Exception e) {
            logger.warn("富文本字段转换失败: " + (richTextValue != null ? richTextValue.substring(0, Math.min(100, richTextValue.length())) : "null"), e);
            return richTextValue; // 转换失败时返回原值
        }
    }

    /**
     * 将Polarion HTML富文本转换为飞书富文本格式的字符串表示
     *
     * 注意：在TypesafeTranslator<String, String, String>架构下，这里返回的是字符串
     * 实际的MultiTextDetail对象转换会在FeishuProxy中处理
     */
    @NotNull
    private String convertPolarionToFeishuRichText(@NotNull String polarionHtml) {
        try {
            logger.debug("转换Polarion HTML富文本到飞书格式，内容长度: " + polarionHtml.length());

            // 在当前架构下，我们主要进行HTML内容的预处理和验证
            // 实际的MultiTextDetail对象创建会在FeishuProxy中进行

            // 验证HTML内容是否有效
            if (polarionHtml.trim().isEmpty()) {
                logger.debug("Polarion HTML内容为空");
                return "";
            }

            // 进行基本的HTML清理和验证（如果需要）
            String processedHtml = preprocessHtmlForFeishu(polarionHtml);

            logger.debug("成功预处理Polarion HTML，准备用于飞书富文本");

            // 返回处理后的HTML内容
            // FeishuProxy会将此HTML内容包装为MultiTextDetail对象
            return processedHtml;

        } catch (Exception e) {
            logger.warn("转换Polarion HTML富文本失败: " + polarionHtml.substring(0, Math.min(100, polarionHtml.length())), e);
            return polarionHtml;
        }
    }

    /**
     * 为飞书预处理HTML内容
     */
    @NotNull
    private String preprocessHtmlForFeishu(@NotNull String html) {
        // 这里可以添加特定的HTML预处理逻辑
        // 例如：清理不支持的标签、转换特殊格式等

        // 目前简单返回原HTML，后续可以根据飞书的具体要求进行扩展
        return html;
    }

    /**
     * 从HTML中提取纯文本内容
     */
    @Nullable
    private String extractPlainTextFromHtml(@Nullable String html) {
        if (html == null || html.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 简单的HTML标签移除（可以后续使用更复杂的HTML解析器）
            String plainText = html
                .replaceAll("<[^>]+>", "") // 移除HTML标签
                .replaceAll("&nbsp;", " ") // 替换HTML实体
                .replaceAll("&lt;", "<")
                .replaceAll("&gt;", ">")
                .replaceAll("&amp;", "&")
                .replaceAll("&quot;", "\"")
                .replaceAll("&#39;", "'")
                .replaceAll("\\s+", " ") // 合并多个空白字符
                .trim();
                
            return plainText.isEmpty() ? null : plainText;
            
        } catch (Exception e) {
            logger.warn("从HTML提取纯文本失败", e);
            return html; // 失败时返回原始HTML
        }
    }



    /**
     * 应用值映射配置
     */
    @Nullable
    private String applyValueMappings(@NotNull String value) {
        for (ValueMapping mapping : valueMappings) {
            if (fromSide == Side.LEFT && value.equals(mapping.getLeft())) {
                return mapping.getRight();
            } else if (fromSide == Side.RIGHT && value.equals(mapping.getRight())) {
                return mapping.getLeft();
            }
        }
        return null;
    }
}
