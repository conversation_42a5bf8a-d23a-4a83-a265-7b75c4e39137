package com.polarion.synchronizer.proxy.feishu.translators.html2feishu;

import org.jetbrains.annotations.NotNull;
import org.jsoup.nodes.Element;

/**
 * 飞书标签处理器基类
 * 参考 JIRA 转换器中的 Tag 接口设计
 */
public abstract class FeishuTag {
    
    /**
     * 处理 HTML 元素，转换为飞书格式
     * 
     * @param element HTML 元素
     * @param result 结果字符串构建器
     * @param converter 转换器实例，用于递归处理子元素
     */
    public abstract void process(@NotNull Element element, 
                                @NotNull StringBuilder result, 
                                @NotNull HTML2FeishuConverter converter);
}

/**
 * 简单标签处理器 - 直接包装开始和结束标签
 */
class SimpleTag extends FeishuTag {
    private final String openTag;
    private final String closeTag;
    
    public SimpleTag(@NotNull String openTag, @NotNull String closeTag) {
        this.openTag = openTag;
        this.closeTag = closeTag;
    }
    
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append(openTag);
        converter.processElement(element, result);
        result.append(closeTag);
    }
}

/**
 * 段落标签处理器
 */
class ParagraphTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<p>");
        converter.processElement(element, result);
        result.append("</p>");
    }
}

/**
 * 换行标签处理器
 */
class LineBreakTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<br>");
    }
}

/**
 * Div 标签处理器
 */
class DivTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<div>");
        converter.processElement(element, result);
        result.append("</div>");
    }
}

/**
 * 标题标签处理器
 */
class HeadingTag extends FeishuTag {
    private final int level;
    
    public HeadingTag(int level) {
        this.level = Math.max(1, Math.min(6, level));
    }
    
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<h").append(level).append(">");
        converter.processElement(element, result);
        result.append("</h").append(level).append(">");
    }
}

/**
 * 链接标签处理器
 */
class LinkTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        String href = element.attr("href");
        String title = element.attr("title");
        
        result.append("<a href=\"").append(escapeAttribute(href)).append("\"");
        if (!title.isEmpty()) {
            result.append(" title=\"").append(escapeAttribute(title)).append("\"");
        }
        result.append(">");
        converter.processElement(element, result);
        result.append("</a>");
    }
    
    private String escapeAttribute(String value) {
        return value.replace("\"", "&quot;").replace("'", "&#39;");
    }
}

/**
 * 无序列表标签处理器
 */
class UnorderedListTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<ul>");
        converter.processElement(element, result);
        result.append("</ul>");
    }
}

/**
 * 有序列表标签处理器
 */
class OrderedListTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<ol>");
        converter.processElement(element, result);
        result.append("</ol>");
    }
}

/**
 * 列表项标签处理器
 */
class ListItemTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<li>");
        converter.processElement(element, result);
        result.append("</li>");
    }
}

/**
 * 表格标签处理器
 */
class TableTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<table>");
        converter.processElement(element, result);
        result.append("</table>");
    }
}

/**
 * 表格行标签处理器
 */
class TableRowTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<tr>");
        converter.processElement(element, result);
        result.append("</tr>");
    }
}

/**
 * 表格头标签处理器
 */
class TableHeaderTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<th>");
        converter.processElement(element, result);
        result.append("</th>");
    }
}

/**
 * 表格单元格标签处理器
 */
class TableCellTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<td>");
        converter.processElement(element, result);
        result.append("</td>");
    }
}

/**
 * 图片标签处理器（参考 JIRA 的附件处理）
 */
class ImageTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element,
                       @NotNull StringBuilder result,
                       @NotNull HTML2FeishuConverter converter) {
        String src = element.attr("src");
        String alt = element.attr("alt");
        String title = element.attr("title");
        String width = element.attr("width");
        String height = element.attr("height");

        // 处理 Polarion 附件格式 (attachment:filename.jpg)
        if (src.startsWith("attachment:")) {
            String attachmentName = src.substring("attachment:".length());
            // 这里可以根据需要转换为飞书的附件 URL 格式
            // 目前保持原格式，让飞书端处理
            converter.getLogger().debug("处理 Polarion 附件图片: " + attachmentName);
        }

        result.append("<img src=\"").append(escapeAttribute(src)).append("\"");

        if (!alt.isEmpty()) {
            result.append(" alt=\"").append(escapeAttribute(alt)).append("\"");
        }
        if (!title.isEmpty()) {
            result.append(" title=\"").append(escapeAttribute(title)).append("\"");
        }
        if (!width.isEmpty()) {
            result.append(" width=\"").append(escapeAttribute(width)).append("\"");
        }
        if (!height.isEmpty()) {
            result.append(" height=\"").append(escapeAttribute(height)).append("\"");
        }

        result.append(">");
    }

    private String escapeAttribute(String value) {
        return value.replace("\"", "&quot;").replace("'", "&#39;");
    }
}

/**
 * 引用标签处理器
 */
class BlockquoteTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<blockquote>");
        converter.processElement(element, result);
        result.append("</blockquote>");
    }
}

/**
 * 代码标签处理器
 */
class CodeTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<code>");
        converter.processElement(element, result);
        result.append("</code>");
    }
}

/**
 * 预格式化标签处理器
 */
class PreTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        result.append("<pre>");
        converter.processElement(element, result);
        result.append("</pre>");
    }
}

/**
 * Span 标签处理器
 */
class SpanTag extends FeishuTag {
    @Override
    public void process(@NotNull Element element, 
                       @NotNull StringBuilder result, 
                       @NotNull HTML2FeishuConverter converter) {
        String style = element.attr("style");
        String className = element.attr("class");
        
        result.append("<span");
        if (!style.isEmpty()) {
            result.append(" style=\"").append(escapeAttribute(style)).append("\"");
        }
        if (!className.isEmpty()) {
            result.append(" class=\"").append(escapeAttribute(className)).append("\"");
        }
        result.append(">");
        
        converter.processElement(element, result);
        result.append("</span>");
    }
    
    private String escapeAttribute(String value) {
        return value.replace("\"", "&quot;").replace("'", "&#39;");
    }
}
