package com.polarion.synchronizer.proxy.feishu.translators.html2feishu;

import com.polarion.alm.tracker.ITestManagementService;
import com.polarion.synchronizer.ILogger;
import com.polarion.synchronizer.proxy.polarion.IPolarionProxy;
import com.polarion.synchronizer.proxy.feishu.FeishuRichText;
import com.lark.project.service.workitem.model.MultiTextDetail;
import com.lark.project.Client;
import com.polarion.synchronizer.proxy.feishu.FeishuAttachmentHandler;
import com.polarion.synchronizer.proxy.feishu.FeishuProxyConfiguration;
import com.polarion.synchronizer.model.Attachment;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.safety.Whitelist;
import org.jsoup.safety.Cleaner;

import java.util.*;

/**
 * HTML 到飞书富文本格式的转换器
 * 参考 JIRA 的 HTML2JiraWikiConverter 实现模式
 */
public class HTML2FeishuConverter {
    
    @NotNull
    private final IPolarionProxy polarionProxy;
    
    @NotNull
    private final ITestManagementService testManagementService;
    
    @NotNull
    private final String baseUrl;
    
    @NotNull
    private final ILogger logger;

    @Nullable
    private final Client feishuClient;

    @Nullable
    private final String projectKey;

    @Nullable
    private final FeishuProxyConfiguration configuration;

    @Nullable
    private FeishuAttachmentHandler attachmentHandler;

    @NotNull
    private final Map<String, FeishuTag> tagMap;

    public HTML2FeishuConverter(@NotNull IPolarionProxy polarionProxy,
                               @NotNull ITestManagementService testManagementService,
                               @NotNull String baseUrl,
                               @NotNull ILogger logger) {
        this(polarionProxy, testManagementService, baseUrl, logger, null, null);
    }

    public HTML2FeishuConverter(@NotNull IPolarionProxy polarionProxy,
                               @NotNull ITestManagementService testManagementService,
                               @NotNull String baseUrl,
                               @NotNull ILogger logger,
                               @Nullable Client feishuClient,
                               @Nullable String projectKey,
                               @Nullable FeishuProxyConfiguration configuration) {
        this.polarionProxy = polarionProxy;
        this.testManagementService = testManagementService;
        this.baseUrl = baseUrl;
        this.logger = logger;
        this.feishuClient = feishuClient;
        this.projectKey = projectKey;
        this.configuration = configuration;

        // 初始化附件处理器
        if (feishuClient != null && configuration != null) {
            this.attachmentHandler = new FeishuAttachmentHandler(
                feishuClient.getAttachmentService(), configuration);
        }

        this.tagMap = new HashMap<>();
        initTagMap();
    }

    /**
     * 初始化标签映射
     */
    private void initTagMap() {
        // 基本文本格式
        tagMap.put("strong", new SimpleTag("<strong>", "</strong>"));
        tagMap.put("b", new SimpleTag("<strong>", "</strong>"));
        tagMap.put("em", new SimpleTag("<em>", "</em>"));
        tagMap.put("i", new SimpleTag("<em>", "</em>"));
        tagMap.put("u", new SimpleTag("<u>", "</u>"));
        tagMap.put("s", new SimpleTag("<s>", "</s>"));
        tagMap.put("strike", new SimpleTag("<s>", "</s>"));
        
        // 段落和换行
        tagMap.put("p", new ParagraphTag());
        tagMap.put("br", new LineBreakTag());
        tagMap.put("div", new DivTag());
        
        // 标题
        tagMap.put("h1", new HeadingTag(1));
        tagMap.put("h2", new HeadingTag(2));
        tagMap.put("h3", new HeadingTag(3));
        tagMap.put("h4", new HeadingTag(4));
        tagMap.put("h5", new HeadingTag(5));
        tagMap.put("h6", new HeadingTag(6));
        
        // 链接
        tagMap.put("a", new LinkTag());
        
        // 列表
        tagMap.put("ul", new UnorderedListTag());
        tagMap.put("ol", new OrderedListTag());
        tagMap.put("li", new ListItemTag());
        
        // 表格
        tagMap.put("table", new TableTag());
        tagMap.put("thead", new SimpleTag("", ""));
        tagMap.put("tbody", new SimpleTag("", ""));
        tagMap.put("tr", new TableRowTag());
        tagMap.put("th", new TableHeaderTag());
        tagMap.put("td", new TableCellTag());
        
        // 图片
        tagMap.put("img", new ImageTag());
        
        // 引用
        tagMap.put("blockquote", new BlockquoteTag());
        
        // 代码
        tagMap.put("code", new CodeTag());
        tagMap.put("pre", new PreTag());
        
        // 其他
        tagMap.put("span", new SpanTag());
        tagMap.put("sub", new SimpleTag("<sub>", "</sub>"));
        tagMap.put("sup", new SimpleTag("<sup>", "</sup>"));
    }

    /**
     * 转换 HTML 内容为飞书富文本格式
     */
    @NotNull
    public MultiTextDetail convertToMultiText(@NotNull String htmlContent) {
        try {
            logger.debug("开始转换 HTML 到飞书 MultiTextDetail，内容长度: " + htmlContent.length());

            // 进行HTML转换
            String convertedHtml = convert(htmlContent);

            // 创建富文本对象
            MultiTextDetail richText = new MultiTextDetail();
            richText.setDocHTML(convertedHtml);
            richText.setDocText(extractPlainText(convertedHtml));
            richText.setIsEmpty(convertedHtml.trim().isEmpty());
            return richText;

        } catch (Exception e) {
            logger.error("转换 HTML 时发生异常", e);
            // 返回空的富文本对象
            MultiTextDetail richText = new MultiTextDetail();
            richText.setDocHTML("");
            richText.setDocText("");
            richText.setIsEmpty(true);
            return richText;
        }
    }

    /**
     * 转换 HTML 内容为飞书富文本格式
     */
    @NotNull
    public String convert(@NotNull String htmlContent) {
        try {
            logger.debug("开始转换 HTML 到飞书格式，内容长度: " + htmlContent.length());
            
            // 清理和解析 HTML
            Whitelist whitelist = createWhitelist();
            String cleanHTML = Jsoup.clean(htmlContent, baseUrl, whitelist,
                new Document.OutputSettings().prettyPrint(false));

            Document doc = (new Cleaner(whitelist)).clean(Jsoup.parse(cleanHTML, baseUrl));
            
            // 转换内容
            StringBuilder result = new StringBuilder();
            processElement(doc.body(), result);
            
            String convertedHTML = result.toString().trim();
            logger.debug("HTML 转换完成，结果长度: " + convertedHTML.length());
            
            return convertedHTML;
            
        } catch (Exception e) {
            logger.warn("HTML 转换失败: " + e.getMessage(), e);
            // 降级处理：返回清理后的基本 HTML
            return cleanBasicHTML(htmlContent);
        }
    }

    /**
     * 创建安全列表
     */
    @NotNull
    private Whitelist createWhitelist() {
        return Whitelist.relaxed()
            .addTags("div", "span", "sub", "sup", "code", "pre")
            .addAttributes(":all", "style", "class", "id")
            .addAttributes("a", "href", "title")
            .addAttributes("img", "src", "alt", "title", "width", "height")
            .addProtocols("a", "href", "http", "https", "mailto")
            .addProtocols("img", "src", "http", "https");
    }

    /**
     * 处理元素（公开方法供标签处理器调用）
     */
    public void processElement(@NotNull Element element, @NotNull StringBuilder result) {
        for (Node child : element.childNodes()) {
            if (child instanceof TextNode) {
                processTextNode((TextNode) child, result);
            } else if (child instanceof Element) {
                processElementNode((Element) child, result);
            }
        }
    }

    /**
     * 处理文本节点
     */
    private void processTextNode(@NotNull TextNode textNode, @NotNull StringBuilder result) {
        String text = textNode.text();
        if (!text.isEmpty()) {
            // 转义特殊字符
            text = escapeSpecialCharacters(text);
            result.append(text);
        }
    }

    /**
     * 处理元素节点
     */
    private void processElementNode(@NotNull Element element, @NotNull StringBuilder result) {
        String tagName = element.tagName().toLowerCase();
        FeishuTag tag = tagMap.get(tagName);
        
        if (tag != null) {
            tag.process(element, result, this);
        } else {
            // 未知标签，直接处理子元素
            logger.debug("未知标签: " + tagName + "，直接处理子元素");
            processElement(element, result);
        }
    }

    /**
     * 转义特殊字符
     */
    @NotNull
    private String escapeSpecialCharacters(@NotNull String text) {
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#39;");
    }

    /**
     * 清理基本 HTML（降级处理）
     */
    @NotNull
    private String cleanBasicHTML(@NotNull String html) {
        try {
            Document doc = Jsoup.parse(html);
            doc.outputSettings().prettyPrint(false);
            return doc.body().html();
        } catch (Exception e) {
            logger.warn("基本 HTML 清理失败", e);
            return html;
        }
    }

    // Getter 方法供标签处理器使用
    @NotNull
    public IPolarionProxy getPolarionProxy() {
        return polarionProxy;
    }

    @NotNull
    public ITestManagementService getTestManagementService() {
        return testManagementService;
    }

    @NotNull
    public String getBaseUrl() {
        return baseUrl;
    }

    @NotNull
    public ILogger getLogger() {
        return logger;
    }



    /**
     * 从HTML中提取纯文本
     */
    @NotNull
    private String extractPlainText(@NotNull String html) {
        if (html == null || html.trim().isEmpty()) {
            return "";
        }

        return html.replaceAll("<[^>]+>", "")
                  .replaceAll("&nbsp;", " ")
                  .replaceAll("&lt;", "<")
                  .replaceAll("&gt;", ">")
                  .replaceAll("&amp;", "&")
                  .replaceAll("&quot;", "\"")
                  .replaceAll("&#39;", "'")
                  .replaceAll("\\s+", " ")
                  .trim();
    }

    /**
     * 根据附件名称获取附件ID
     */
    @Nullable
    public String getAttachmentIdByName(@NotNull String attachmentName) {
        try {
            // 通过 polarionProxy 获取附件映射
            // 这里需要遍历附件映射，查找匹配的文件名
            if (polarionProxy instanceof com.polarion.synchronizer.proxy.feishu.FeishuProxy) {
                com.polarion.synchronizer.proxy.feishu.FeishuProxy feishuProxy =
                    (com.polarion.synchronizer.proxy.feishu.FeishuProxy) polarionProxy;

                // 获取附件映射
                java.util.Map<String, String> attachmentMap = feishuProxy.getAttachmentIdNameMap();

                // 遍历映射查找匹配的文件名
                for (java.util.Map.Entry<String, String> entry : attachmentMap.entrySet()) {
                    if (attachmentName.equals(entry.getValue())) {
                        logger.debug("找到附件ID映射: " + attachmentName + " -> " + entry.getKey());
                        return entry.getKey();
                    }
                }
            }

            logger.debug("未找到附件ID映射: " + attachmentName);
            return null;

        } catch (Exception e) {
            logger.warn("查找附件ID时发生异常: " + attachmentName, e);
            return null;
        }
    }

    /**
     * 处理附件URL转换
     */
    @NotNull
    public String processAttachmentUrl(@NotNull String originalUrl, @NotNull String attachmentName) {
        logger.debug("处理附件URL转换: " + originalUrl + " -> " + attachmentName);

        // 如果有附件ID映射，转换为飞书的文件ID格式
        String fileId = getAttachmentIdByName(attachmentName);
        if (fileId != null && !fileId.trim().isEmpty()) {
            String convertedUrl = "/file/" + fileId + "/" + attachmentName;
            logger.debug("通过ID映射转换: " + attachmentName + " -> " + convertedUrl);
            return convertedUrl;
        }

        // 默认转换为相对路径格式（飞书可以识别的附件格式）
        String defaultUrl = "/attachment/" + attachmentName;
        logger.debug("使用默认格式转换: " + attachmentName + " -> " + defaultUrl);
        return defaultUrl;
    }
}
