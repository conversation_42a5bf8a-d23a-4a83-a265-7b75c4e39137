package com.polarion.synchronizer.proxy.feishu.translators.html2feishu;

import com.polarion.core.util.logging.ILogger;
import com.polarion.synchronizer.model.IPolarionProxy;
import com.polarion.alm.tracker.ITestManagementService;
import com.lark.project.Client;
import com.lark.project.service.attachment.builder.SpecialUploadAttachmentReq;
import com.lark.project.service.attachment.builder.SpecialUploadAttachmentResp;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.workitem.model.MultiTextDetail;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 富文本图片处理器
 * 参考 JIRA 转换器的实现，处理 Polarion 附件上传到飞书
 */
public class RichTextImageHandler {
    
    @NotNull
    private final IPolarionProxy polarionProxy;
    
    @NotNull
    private final ITestManagementService testManagementService;
    
    @NotNull
    private final String projectKey;
    
    @NotNull
    private final Client feishuClient;
    
    @NotNull
    private final ILogger logger;
    
    // 图片标签的正则表达式
    private static final Pattern IMG_PATTERN = Pattern.compile("<img[^>]+src=[\"']([^\"']+)[\"'][^>]*>");
    
    public RichTextImageHandler(@NotNull IPolarionProxy polarionProxy,
                               @NotNull ITestManagementService testManagementService,
                               @NotNull String projectKey,
                               @NotNull Client feishuClient,
                               @NotNull ILogger logger) {
        this.polarionProxy = polarionProxy;
        this.testManagementService = testManagementService;
        this.projectKey = projectKey;
        this.feishuClient = feishuClient;
        this.logger = logger;
    }

    /**
     * 处理包含图片的HTML内容，上传附件并替换URL
     */
    @NotNull
    public MultiTextDetail processHtmlWithImages(@NotNull String htmlContent) throws Exception {
        logger.debug("开始处理HTML中的图片附件，内容长度: " + htmlContent.length());
        
        String processedHtml = htmlContent;
        Matcher matcher = IMG_PATTERN.matcher(htmlContent);
        
        while (matcher.find()) {
            String originalSrc = matcher.group(1);
            logger.debug("发现图片标签: " + originalSrc);
            
            // 检查是否是需要上传的 Polarion 附件
            if (isPolarionAttachment(originalSrc)) {
                String attachmentName = extractAttachmentName(originalSrc);
                logger.debug("检测到 Polarion 附件: " + attachmentName);
                
                try {
                    // 上传附件到飞书
                    String newUrl = uploadAttachmentToFeishu(attachmentName);
                    if (newUrl != null) {
                        // 替换HTML中的图片URL
                        processedHtml = processedHtml.replace(originalSrc, newUrl);
                        logger.debug("成功替换图片URL: " + originalSrc + " -> " + newUrl);
                    } else {
                        logger.warn("附件上传失败，保持原URL: " + originalSrc);
                    }
                } catch (Exception e) {
                    logger.error("处理附件时发生异常: " + attachmentName, e);
                    // 继续处理其他图片，不中断整个流程
                }
            }
        }
        
        // 创建富文本对象
        MultiTextDetail richText = new MultiTextDetail();
        richText.setDocHTML(processedHtml);
        richText.setDocText(extractPlainText(processedHtml));
        richText.setIsEmpty(processedHtml.trim().isEmpty());
        
        logger.debug("HTML图片处理完成，处理后长度: " + processedHtml.length());
        return richText;
    }

    /**
     * 检查是否是 Polarion 附件格式
     */
    private boolean isPolarionAttachment(@NotNull String src) {
        return src.startsWith("attachment:");
    }

    /**
     * 从附件URL中提取附件名称
     */
    @NotNull
    private String extractAttachmentName(@NotNull String src) {
        if (src.startsWith("attachment:")) {
            return src.substring("attachment:".length());
        }
        return src;
    }

    /**
     * 上传附件到飞书项目
     */
    @Nullable
    private String uploadAttachmentToFeishu(@NotNull String attachmentName) throws Exception {
        logger.debug("开始上传附件到飞书: " + attachmentName);
        
        // 1. 从 Polarion 获取附件内容
        File tempFile = downloadAttachmentFromPolarion(attachmentName);
        if (tempFile == null) {
            logger.warn("无法从 Polarion 下载附件: " + attachmentName);
            return null;
        }
        
        try {
            // 2. 上传到飞书
            SpecialUploadAttachmentReq uploadReq = SpecialUploadAttachmentReq.newBuilder()
                .projectKey(projectKey)
                .file(tempFile)
                .fileMimeType(getMimeType(tempFile))
                .build();
            
            RequestOptions options = RequestOptions.newBuilder().build();
            SpecialUploadAttachmentResp uploadResp = feishuClient.getAttachmentService()
                .specialUploadAttachment(uploadReq, options);
            
            if (uploadResp.success() && uploadResp.getData() != null) {
                String fileUrl = uploadResp.getData().getFileUrl();
                logger.debug("附件上传成功: " + attachmentName + " -> " + fileUrl);
                return fileUrl;
            } else {
                logger.warn("附件上传失败: " + attachmentName + 
                           ", 错误: " + (uploadResp.getErrMsg() != null ? uploadResp.getErrMsg() : "未知错误"));
                return null;
            }
            
        } finally {
            // 3. 清理临时文件
            if (tempFile.exists()) {
                tempFile.delete();
                logger.debug("清理临时文件: " + tempFile.getAbsolutePath());
            }
        }
    }

    /**
     * 从 Polarion 下载附件内容
     */
    @Nullable
    private File downloadAttachmentFromPolarion(@NotNull String attachmentName) {
        try {
            logger.debug("从 Polarion 下载附件: " + attachmentName);
            
            // 这里需要通过 Polarion API 获取附件内容
            // 由于我们在转换器中，可能需要从当前工作项的附件中查找
            // 这是一个简化的实现，实际可能需要更复杂的逻辑
            
            // 创建临时文件
            String fileExtension = getFileExtension(attachmentName);
            File tempFile = File.createTempFile("polarion_attachment_", fileExtension);
            
            // 尝试通过 testManagementService 或其他方式获取附件内容
            // 这里是一个占位实现，实际需要根据 Polarion API 调整
            InputStream attachmentStream = getAttachmentStreamFromPolarion(attachmentName);
            if (attachmentStream != null) {
                try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = attachmentStream.read(buffer)) != -1) {
                        fos.write(buffer, 0, bytesRead);
                    }
                }
                
                logger.debug("成功下载附件到临时文件: " + tempFile.getAbsolutePath());
                return tempFile;
            } else {
                logger.warn("无法获取附件流: " + attachmentName);
                tempFile.delete();
                return null;
            }
            
        } catch (Exception e) {
            logger.error("下载 Polarion 附件失败: " + attachmentName, e);
            return null;
        }
    }

    /**
     * 从 Polarion 获取附件流
     */
    @Nullable
    private InputStream getAttachmentStreamFromPolarion(@NotNull String attachmentName) {
        try {
            logger.debug("尝试从 Polarion 获取附件流: " + attachmentName);

            // 方案1: 通过 testManagementService 获取附件
            // 这需要当前的工作项上下文，在转换器中可能不容易获取

            // 方案2: 通过 polarionProxy 获取附件
            // 如果 polarionProxy 是 FeishuProxy，可以尝试获取附件映射
            if (polarionProxy instanceof com.polarion.synchronizer.proxy.feishu.FeishuProxy) {
                com.polarion.synchronizer.proxy.feishu.FeishuProxy feishuProxy =
                    (com.polarion.synchronizer.proxy.feishu.FeishuProxy) polarionProxy;

                // 这里需要更复杂的逻辑来获取附件内容
                // 目前作为占位实现
                logger.debug("通过 FeishuProxy 查找附件: " + attachmentName);
            }

            // 方案3: 如果附件名称包含路径信息，尝试直接读取文件
            // 这种情况下，attachment:filename 可能实际指向本地文件
            if (attachmentName.contains("/") || attachmentName.contains("\\")) {
                File attachmentFile = new File(attachmentName);
                if (attachmentFile.exists() && attachmentFile.isFile()) {
                    logger.debug("找到本地附件文件: " + attachmentFile.getAbsolutePath());
                    return new java.io.FileInputStream(attachmentFile);
                }
            }

            // 目前返回 null，表示无法获取附件内容
            logger.warn("无法获取 Polarion 附件内容: " + attachmentName +
                       " (需要实现具体的附件获取逻辑)");
            return null;

        } catch (Exception e) {
            logger.error("获取 Polarion 附件流时发生异常: " + attachmentName, e);
            return null;
        }
    }

    /**
     * 获取文件的MIME类型
     */
    @NotNull
    private String getMimeType(@NotNull File file) {
        String name = file.getName().toLowerCase();
        if (name.endsWith(".png")) return "image/png";
        if (name.endsWith(".jpg") || name.endsWith(".jpeg")) return "image/jpeg";
        if (name.endsWith(".gif")) return "image/gif";
        if (name.endsWith(".bmp")) return "image/bmp";
        if (name.endsWith(".webp")) return "image/webp";
        return "image/png"; // 默认
    }

    /**
     * 获取文件扩展名
     */
    @NotNull
    private String getFileExtension(@NotNull String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }
        return ".tmp";
    }

    /**
     * 从HTML中提取纯文本
     */
    @NotNull
    private String extractPlainText(@NotNull String html) {
        if (html == null || html.trim().isEmpty()) {
            return "";
        }
        
        return html.replaceAll("<[^>]+>", "")
                  .replaceAll("&nbsp;", " ")
                  .replaceAll("&lt;", "<")
                  .replaceAll("&gt;", ">")
                  .replaceAll("&amp;", "&")
                  .replaceAll("&quot;", "\"")
                  .replaceAll("&#39;", "'")
                  .replaceAll("\\s+", " ")
                  .trim();
    }
}
